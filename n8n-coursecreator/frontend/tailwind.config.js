/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        // Dark theme backgrounds
        dark: {
          100: "#1a1a1a",
          200: "#121212",
          300: "#0a0a0a",
          400: "#2a2a2a",
          500: "#1f1f1f",
        },
        // Vibrant accent colors
        electric: {
          blue: "#00d4ff",
          green: "#00ff88",
          purple: "#a855f7",
          pink: "#ff6b9d",
          coral: "#ff6b6b",
        },
        // Text colors
        text: {
          primary: "#ffffff",
          secondary: "#e5e5e5",
          muted: "#9ca3af",
          accent: "#00d4ff",
        },
        // Status colors
        status: {
          success: "#00ff88",
          warning: "#ffd700",
          error: "#ff6b6b",
          info: "#00d4ff",
        },
      },
      fontFamily: {
        sans: ["Inter", "ui-sans-serif", "system-ui"],
        mono: ["JetBrains Mono", "ui-monospace", "SFMono-Regular"],
      },
      animation: {
        "glow": "glow 2s ease-in-out infinite alternate",
        "pulse-glow": "pulse-glow 2s ease-in-out infinite",
        "gradient": "gradient 6s ease infinite",
        "float": "float 3s ease-in-out infinite",
      },
      keyframes: {
        glow: {
          "0%": { 
            boxShadow: "0 0 5px #00d4ff, 0 0 10px #00d4ff, 0 0 15px #00d4ff" 
          },
          "100%": { 
            boxShadow: "0 0 10px #00d4ff, 0 0 20px #00d4ff, 0 0 30px #00d4ff" 
          },
        },
        "pulse-glow": {
          "0%, 100%": { 
            boxShadow: "0 0 5px #00d4ff40" 
          },
          "50%": { 
            boxShadow: "0 0 20px #00d4ff80, 0 0 30px #00d4ff40" 
          },
        },
        gradient: {
          "0%, 100%": {
            backgroundPosition: "0% 50%"
          },
          "50%": {
            backgroundPosition: "100% 50%"
          },
        },
        float: {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-10px)" },
        },
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic": "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
        "electric-gradient": "linear-gradient(135deg, #00d4ff 0%, #00ff88 50%, #a855f7 100%)",
        "dark-gradient": "linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #121212 100%)",
      },
      backdropBlur: {
        xs: "2px",
      },
    },
  },
  plugins: [],
}