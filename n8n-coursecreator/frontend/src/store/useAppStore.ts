import { create } from 'zustand';
import { ApiService, TaskStatus } from '@/services/api';

interface Task {
  id: string;
  type: 'course' | 'book';
  title: string;
  status: TaskStatus;
  createdAt: Date;
}

interface AppState {
  // Tasks
  tasks: Task[];
  activeTasks: string[];
  
  // UI State
  sidebarOpen: boolean;
  theme: 'dark' | 'light';
  
  // Data
  recentProjects: any[];
  statistics: {
    coursesCreated: number;
    booksGenerated: number;
    totalTasks: number;
  };
}

interface AppActions {
  // Task management
  addTask: (task: Omit<Task, 'createdAt'>) => void;
  updateTaskStatus: (taskId: string, status: TaskStatus) => void;
  removeTask: (taskId: string) => void;
  pollTaskStatus: (taskId: string) => Promise<void>;
  
  // UI actions
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  setTheme: (theme: 'dark' | 'light') => void;
  
  // Data actions
  updateStatistics: () => void;
  addRecentProject: (project: any) => void;
}

export const useAppStore = create<AppState & AppActions>((set, get) => ({
  // Initial state
  tasks: [],
  activeTasks: [],
  sidebarOpen: true,
  theme: 'dark',
  recentProjects: [],
  statistics: {
    coursesCreated: 0,
    booksGenerated: 0,
    totalTasks: 0,
  },

  // Task management actions
  addTask: (task) => {
    const newTask: Task = {
      ...task,
      createdAt: new Date(),
    };
    
    set((state) => ({
      tasks: [newTask, ...state.tasks],
      activeTasks: [...state.activeTasks, task.id],
    }));
    
    // Start polling for this task
    get().pollTaskStatus(task.id);
  },

  updateTaskStatus: (taskId, status) => {
    set((state) => ({
      tasks: state.tasks.map((task) =>
        task.id === taskId ? { ...task, status } : task
      ),
      activeTasks: 
        status.status === 'completed' || status.status === 'failed'
          ? state.activeTasks.filter(id => id !== taskId)
          : state.activeTasks,
    }));
    
    // Update statistics when task completes
    if (status.status === 'completed') {
      get().updateStatistics();
    }
  },

  removeTask: (taskId) => {
    set((state) => ({
      tasks: state.tasks.filter((task) => task.id !== taskId),
      activeTasks: state.activeTasks.filter(id => id !== taskId),
    }));
  },

  pollTaskStatus: async (taskId) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await ApiService.getTaskStatus(taskId);
        if (response.success && response.data) {
          get().updateTaskStatus(taskId, response.data);
          
          // Stop polling if task is complete or failed
          if (response.data.status === 'completed' || response.data.status === 'failed') {
            clearInterval(pollInterval);
          }
        }
      } catch (error) {
        console.error('Error polling task status:', error);
        // Continue polling even on error, but maybe add exponential backoff
      }
    }, 2000); // Poll every 2 seconds

    // Cleanup after 10 minutes
    setTimeout(() => clearInterval(pollInterval), 10 * 60 * 1000);
  },

  // UI actions
  toggleSidebar: () => {
    set((state) => ({ sidebarOpen: !state.sidebarOpen }));
  },

  setSidebarOpen: (open) => {
    set({ sidebarOpen: open });
  },

  setTheme: (theme) => {
    set({ theme });
    document.documentElement.className = theme;
  },

  // Data actions
  updateStatistics: () => {
    const { tasks } = get();
    const completedTasks = tasks.filter(task => task.status.status === 'completed');
    
    set({
      statistics: {
        coursesCreated: completedTasks.filter(task => task.type === 'course').length,
        booksGenerated: completedTasks.filter(task => task.type === 'book').length,
        totalTasks: tasks.length,
      },
    });
  },

  addRecentProject: (project) => {
    set((state) => ({
      recentProjects: [project, ...state.recentProjects.slice(0, 9)], // Keep only 10 recent
    }));
  },
}));