import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// Create axios instance
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// API Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp?: string;
}

export interface TaskStatus {
  status: 'started' | 'running' | 'completed' | 'failed';
  progress: number;
  message: string;
  created_at: string;
  completed_at?: string;
  result?: any;
  error?: string;
}

// Course related types
export interface CourseRequest {
  course_title: string;
  style?: string;
  tone?: string;
  depth?: string;
  target_audience?: string;
  duration?: string;
  context?: string;
  avatar_id?: string;
  voice_id?: string;
  video_quality?: string;
  qa_count?: number;
  include_audio?: boolean;
  include_video?: boolean;
}

export interface CourseOutlineRequest {
  course_title: string;
  style?: string;
  tone?: string;
  depth?: string;
  target_audience?: string;
  duration?: string;
}

// Book related types
export interface BookRequest {
  book_title: string;
  research_data?: any;
  style?: string;
  citation_style?: string;
  target_audience?: string;
  estimated_pages?: number;
  context?: string;
  include_annotations?: boolean;
}

export interface BookOutlineRequest {
  book_title: string;
  research_data?: any;
  style?: string;
  citation_style?: string;
  target_audience?: string;
  estimated_pages?: number;
}

// API Service Class
export class ApiService {
  // Health check
  static async healthCheck() {
    const response = await api.get<{ status: string; timestamp: string }>('/health');
    return response.data;
  }

  // Course Creation APIs
  static async createCourseOutline(request: CourseOutlineRequest) {
    const response = await api.post<ApiResponse>('/course/outline', request);
    return response.data;
  }

  static async generateCourseScripts(data: any) {
    const response = await api.post<ApiResponse>('/course/scripts', data);
    return response.data;
  }

  static async createCourseVideos(data: any) {
    const response = await api.post<ApiResponse>('/course/videos', data);
    return response.data;
  }

  static async generateCourseQA(data: any) {
    const response = await api.post<ApiResponse>('/course/qa', data);
    return response.data;
  }

  static async createFullCourse(request: CourseRequest) {
    const response = await api.post<ApiResponse<{ task_id: string }>>('/course/create', request);
    return response.data;
  }

  // Book Generation APIs
  static async processBookResearch(data: any) {
    const response = await api.post<ApiResponse>('/book/research', data);
    return response.data;
  }

  static async createBookOutline(request: BookOutlineRequest) {
    const response = await api.post<ApiResponse>('/book/outline', request);
    return response.data;
  }

  static async generateBookChapters(data: any) {
    const response = await api.post<ApiResponse>('/book/chapters', data);
    return response.data;
  }

  static async compileBookBibliography(data: any) {
    const response = await api.post<ApiResponse>('/book/bibliography', data);
    return response.data;
  }

  static async generateFullBook(request: BookRequest) {
    const response = await api.post<ApiResponse<{ task_id: string }>>('/book/generate', request);
    return response.data;
  }

  // Task Status API
  static async getTaskStatus(taskId: string) {
    const response = await api.get<ApiResponse<TaskStatus>>(`/task/${taskId}`);
    return response.data;
  }

  // Root endpoint info
  static async getApiInfo() {
    const response = await api.get('/');
    return response.data;
  }
}

export default ApiService;