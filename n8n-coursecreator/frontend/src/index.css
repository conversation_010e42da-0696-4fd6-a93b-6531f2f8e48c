@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-dark-300 text-text-primary font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-dark-400;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-electric-blue/30 rounded-full hover:bg-electric-blue/50;
  }
  
  /* Custom glow effects */
  .glow-electric {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }
  
  .glow-green {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
  }
  
  .glow-purple {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
  }
}

@layer components {
  /* Glass morphism effect */
  .glass {
    @apply backdrop-blur-xl bg-white/5 border border-white/10;
  }
  
  .glass-dark {
    @apply backdrop-blur-xl bg-black/20 border border-white/5;
  }
  
  /* Button variants */
  .btn-primary {
    @apply px-6 py-3 bg-electric-blue text-dark-300 font-semibold rounded-lg hover:bg-electric-blue/90 transition-all duration-200 hover:scale-105 active:scale-95;
  }
  
  .btn-secondary {
    @apply px-6 py-3 bg-dark-400 text-text-primary font-semibold rounded-lg border border-electric-blue/30 hover:border-electric-blue hover:bg-dark-400/80 transition-all duration-200;
  }
  
  .btn-ghost {
    @apply px-6 py-3 text-text-primary font-semibold rounded-lg hover:bg-white/5 transition-all duration-200;
  }
  
  /* Card styles */
  .card {
    @apply bg-dark-500 border border-white/10 rounded-xl p-6 hover:border-electric-blue/30 transition-all duration-300;
  }
  
  .card-glow {
    @apply bg-dark-500 border border-white/10 rounded-xl p-6 hover:border-electric-blue/50 hover:shadow-[0_0_30px_rgba(0,212,255,0.15)] transition-all duration-300;
  }
  
  /* Input styles */
  .input {
    @apply w-full px-4 py-3 bg-dark-400 border border-white/10 rounded-lg text-text-primary placeholder-text-muted focus:border-electric-blue focus:outline-none focus:ring-2 focus:ring-electric-blue/20 transition-all duration-200;
  }
  
  .textarea {
    @apply w-full px-4 py-3 bg-dark-400 border border-white/10 rounded-lg text-text-primary placeholder-text-muted focus:border-electric-blue focus:outline-none focus:ring-2 focus:ring-electric-blue/20 transition-all duration-200 min-h-[120px] resize-vertical;
  }
  
  /* Progress bar */
  .progress-bar {
    @apply w-full h-2 bg-dark-400 rounded-full overflow-hidden;
  }
  
  .progress-fill {
    @apply h-full bg-gradient-to-r from-electric-blue via-electric-green to-electric-purple transition-all duration-500 ease-out;
  }
}

/* Animation utilities */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: shimmer 2s infinite;
}