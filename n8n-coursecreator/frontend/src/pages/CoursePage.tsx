import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { CourseWizard, CourseData } from '@/components/course/CourseWizard';
import { useAppStore } from '@/store/useAppStore';
import { ApiService } from '@/services/api';
import toast from 'react-hot-toast';

export function CoursePage() {
  const navigate = useNavigate();
  const { addTask, addRecentProject } = useAppStore();
  const [isCreating, setIsCreating] = useState(false);

  const handleCourseComplete = async (courseData: CourseData) => {
    setIsCreating(true);
    
    try {
      // Create the course using the full course creation API
      const response = await ApiService.createFullCourse({
        course_title: courseData.title,
        style: courseData.style,
        tone: courseData.tone,
        depth: courseData.depth,
        target_audience: courseData.target_audience,
        duration: courseData.duration,
        context: courseData.context,
        avatar_id: courseData.avatar_id,
        voice_id: courseData.voice_id,
        video_quality: courseData.video_quality,
        qa_count: courseData.qa_count,
        include_audio: courseData.include_audio,
        include_video: courseData.include_video,
      });

      if (response.success && response.data?.task_id) {
        // Add task to the store for monitoring
        addTask({
          id: response.data.task_id,
          type: 'course',
          title: courseData.title,
          status: {
            status: 'started',
            progress: 0,
            message: 'Course creation started',
            created_at: new Date().toISOString(),
          },
        });

        // Add to recent projects
        addRecentProject({
          id: response.data.task_id,
          type: 'course',
          title: courseData.title,
          createdAt: new Date(),
          data: courseData,
        });

        toast.success('Course creation started! You can monitor progress in the Tasks section.');
        navigate('/tasks');
      } else {
        throw new Error(response.error || 'Failed to start course creation');
      }
    } catch (error) {
      console.error('Error creating course:', error);
      toast.error('Failed to start course creation. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  const handleCancel = () => {
    navigate('/');
  };

  if (isCreating) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center space-y-6"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            className="w-16 h-16 border-4 border-electric-blue border-t-transparent rounded-full mx-auto"
          />
          <div>
            <h2 className="text-2xl font-bold text-text-primary mb-2">Creating Your Course</h2>
            <p className="text-text-muted">
              Please wait while we process your course creation request...
            </p>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/')}
          icon={<ArrowLeft className="w-4 h-4" />}
        >
          Back to Dashboard
        </Button>
        <div className="flex items-center space-x-2">
          <Sparkles className="w-6 h-6 text-electric-blue" />
          <h1 className="text-2xl font-bold text-text-primary">Course Creator</h1>
        </div>
      </div>

      {/* Course Wizard */}
      <CourseWizard 
        onComplete={handleCourseComplete}
        onCancel={handleCancel}
      />
    </div>
  );
}