import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Activity, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw, 
  GraduationCap,
  BookOpen,
  Play,
  Pause,
  X,
  Eye,
  Download
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAppStore } from '@/store/useAppStore';
import { cn } from '@/utils/cn';

export function TasksPage() {
  const { tasks, activeTasks, removeTask, updateStatistics } = useAppStore();

  useEffect(() => {
    updateStatistics();
  }, [updateStatistics]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-electric-green" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-electric-coral" />;
      case 'running':
        return <RefreshCw className="w-5 h-5 text-electric-blue animate-spin" />;
      default:
        return <Clock className="w-5 h-5 text-electric-purple" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-electric-green border-electric-green/20 bg-electric-green/5';
      case 'failed':
        return 'text-electric-coral border-electric-coral/20 bg-electric-coral/5';
      case 'running':
        return 'text-electric-blue border-electric-blue/20 bg-electric-blue/5';
      default:
        return 'text-electric-purple border-electric-purple/20 bg-electric-purple/5';
    }
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const diff = end.getTime() - start.getTime();
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const activeTasks_ = tasks.filter(task => activeTasks.includes(task.id));
  const completedTasks = tasks.filter(task => !activeTasks.includes(task.id));

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">Task Monitor</h1>
          <p className="text-text-secondary">Track the progress of your AI-powered content generation</p>
        </div>
        <div className="flex items-center space-x-2">
          <Activity className="w-6 h-6 text-electric-blue" />
          <span className="text-lg font-semibold text-text-primary">
            {activeTasks.length} Active
          </span>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-electric-blue/10 border border-electric-blue/20 rounded-lg">
              <Activity className="w-5 h-5 text-electric-blue" />
            </div>
            <div>
              <div className="text-2xl font-bold text-text-primary">{activeTasks.length}</div>
              <div className="text-sm text-text-muted">Active Tasks</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-electric-green/10 border border-electric-green/20 rounded-lg">
              <CheckCircle className="w-5 h-5 text-electric-green" />
            </div>
            <div>
              <div className="text-2xl font-bold text-text-primary">
                {tasks.filter(t => t.status.status === 'completed').length}
              </div>
              <div className="text-sm text-text-muted">Completed</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-electric-coral/10 border border-electric-coral/20 rounded-lg">
              <AlertCircle className="w-5 h-5 text-electric-coral" />
            </div>
            <div>
              <div className="text-2xl font-bold text-text-primary">
                {tasks.filter(t => t.status.status === 'failed').length}
              </div>
              <div className="text-sm text-text-muted">Failed</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-electric-purple/10 border border-electric-purple/20 rounded-lg">
              <Clock className="w-5 h-5 text-electric-purple" />
            </div>
            <div>
              <div className="text-2xl font-bold text-text-primary">{tasks.length}</div>
              <div className="text-sm text-text-muted">Total Tasks</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Active Tasks */}
      {activeTasks_.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-text-primary flex items-center space-x-2">
            <RefreshCw className="w-5 h-5 text-electric-blue animate-spin" />
            <span>Active Tasks</span>
          </h2>
          
          <div className="space-y-4">
            {activeTasks_.map((task) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                layout
              >
                <Card className="p-6" glow>
                  <div className="space-y-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className={`p-3 rounded-lg ${
                          task.type === 'course' 
                            ? 'bg-electric-blue/10 border border-electric-blue/20'
                            : 'bg-electric-green/10 border border-electric-green/20'
                        }`}>
                          {task.type === 'course' ? (
                            <GraduationCap className={`w-6 h-6 ${
                              task.type === 'course' ? 'text-electric-blue' : 'text-electric-green'
                            }`} />
                          ) : (
                            <BookOpen className={`w-6 h-6 ${
                              task.type === 'course' ? 'text-electric-blue' : 'text-electric-green'
                            }`} />
                          )}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg font-semibold text-text-primary">{task.title}</h3>
                          <p className="text-text-muted capitalize">{task.type} generation</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <div className="flex items-center space-x-1">
                              {getStatusIcon(task.status.status)}
                              <span className="text-sm text-text-secondary capitalize">
                                {task.status.status}
                              </span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="w-4 h-4 text-text-muted" />
                              <span className="text-sm text-text-muted">
                                {formatDuration(task.status.created_at, task.status.completed_at)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm" icon={<Eye className="w-4 h-4" />}>
                          Details
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => removeTask(task.id)}
                          icon={<X className="w-4 h-4" />}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-text-muted">{task.status.message}</span>
                        <span className="text-text-primary font-medium">{task.status.progress}%</span>
                      </div>
                      <div className="progress-bar">
                        <motion.div
                          className="progress-fill"
                          initial={{ width: 0 }}
                          animate={{ width: `${task.status.progress}%` }}
                          transition={{ duration: 0.5 }}
                        />
                      </div>
                    </div>

                    {/* Task Status Badge */}
                    <div className="flex items-center justify-between">
                      <div className={cn(
                        'px-3 py-1 rounded-full text-sm font-medium border',
                        getStatusColor(task.status.status)
                      )}>
                        {task.status.status === 'running' ? 'In Progress' : task.status.status}
                      </div>
                      
                      <div className="text-sm text-text-muted">
                        Started {new Date(task.createdAt).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Completed Tasks */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-text-primary flex items-center space-x-2">
          <CheckCircle className="w-5 h-5 text-electric-green" />
          <span>Task History</span>
        </h2>
        
        {completedTasks.length === 0 && activeTasks_.length === 0 && (
          <Card className="p-8 text-center">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-electric-blue/10 border border-electric-blue/20 rounded-full flex items-center justify-center mx-auto">
                <Activity className="w-8 h-8 text-electric-blue" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-text-primary mb-2">No Tasks Yet</h3>
                <p className="text-text-muted mb-4">
                  Start creating courses or generating books to see tasks here.
                </p>
                <div className="flex items-center justify-center space-x-4">
                  <Button onClick={() => window.location.href = '/course'}>
                    Create Course
                  </Button>
                  <Button onClick={() => window.location.href = '/book'} variant="outline">
                    Generate Book
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        )}
        
        {completedTasks.length > 0 && (
          <div className="space-y-3">
            {completedTasks.map((task) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                layout
              >
                <Card className="p-4" hover>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-lg ${
                        task.type === 'course' 
                          ? 'bg-electric-blue/10 border border-electric-blue/20'
                          : 'bg-electric-green/10 border border-electric-green/20'
                      }`}>
                        {task.type === 'course' ? (
                          <GraduationCap className={`w-5 h-5 ${
                            task.type === 'course' ? 'text-electric-blue' : 'text-electric-green'
                          }`} />
                        ) : (
                          <BookOpen className={`w-5 h-5 ${
                            task.type === 'course' ? 'text-electric-blue' : 'text-electric-green'
                          }`} />
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-text-primary">{task.title}</h4>
                        <div className="flex items-center space-x-4 text-sm text-text-muted">
                          <span className="capitalize">{task.type}</span>
                          <span>•</span>
                          <div className="flex items-center space-x-1">
                            {getStatusIcon(task.status.status)}
                            <span className="capitalize">{task.status.status}</span>
                          </div>
                          <span>•</span>
                          <span>{new Date(task.createdAt).toLocaleDateString()}</span>
                          {task.status.completed_at && (
                            <>
                              <span>•</span>
                              <span>{formatDuration(task.status.created_at, task.status.completed_at)}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {task.status.status === 'completed' && (
                        <Button variant="ghost" size="sm" icon={<Download className="w-4 h-4" />}>
                          Download
                        </Button>
                      )}
                      <Button variant="ghost" size="sm" icon={<Eye className="w-4 h-4" />}>
                        View
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => removeTask(task.id)}
                        icon={<X className="w-4 h-4" />}
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}