import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  GraduationCap,
  BookOpen,
  Activity,
  Zap,
  TrendingUp,
  Clock,
  Users,
  Sparkles,
  ArrowRight,
  Play,
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAppStore } from '@/store/useAppStore';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export function Dashboard() {
  const navigate = useNavigate();
  const { statistics, tasks, activeTasks, updateStatistics } = useAppStore();

  useEffect(() => {
    updateStatistics();
  }, [updateStatistics]);

  const recentTasks = tasks.slice(0, 5);

  const quickActions = [
    {
      title: 'Create Course',
      description: 'Build comprehensive courses with AI-generated content, scripts, and videos',
      icon: GraduationCap,
      color: 'electric-blue',
      route: '/course',
      features: ['Auto-generated outlines', 'Video creation', 'Q&A generation'],
    },
    {
      title: 'Generate Book',
      description: 'Transform research into professional books with citations and bibliography',
      icon: BookOpen,
      color: 'electric-green',
      route: '/book',
      features: ['Research processing', 'Chapter generation', 'Auto-bibliography'],
    },
  ];

  const stats = [
    {
      label: 'Courses Created',
      value: statistics.coursesCreated,
      icon: GraduationCap,
      color: 'text-electric-blue',
      change: '+12%',
    },
    {
      label: 'Books Generated',
      value: statistics.booksGenerated,
      icon: BookOpen,
      color: 'text-electric-green',
      change: '+8%',
    },
    {
      label: 'Active Tasks',
      value: activeTasks.length,
      icon: Activity,
      color: 'text-electric-purple',
      change: '0%',
    },
    {
      label: 'Total Projects',
      value: statistics.totalTasks,
      icon: TrendingUp,
      color: 'text-electric-coral',
      change: '+25%',
    },
  ];

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Hero Section */}
      <motion.section variants={itemVariants} className="relative">
        <div className="text-center space-y-6">
          {/* Animated background elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: "linear",
              }}
              className="absolute top-10 left-10 w-32 h-32 bg-electric-blue/5 rounded-full blur-3xl"
            />
            <motion.div
              animate={{
                scale: [1.2, 1, 1.2],
                rotate: [360, 180, 0],
              }}
              transition={{
                duration: 25,
                repeat: Infinity,
                ease: "linear",
              }}
              className="absolute bottom-10 right-10 w-40 h-40 bg-electric-purple/5 rounded-full blur-3xl"
            />
          </div>

          <div className="relative">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: 'spring', damping: 15, stiffness: 200 }}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-electric-blue/10 border border-electric-blue/20 rounded-full mb-6"
            >
              <Sparkles className="w-4 h-4 text-electric-blue" />
              <span className="text-sm font-medium text-electric-blue">AI-Powered Content Creation</span>
            </motion.div>

            <h1 className="text-5xl md:text-6xl font-bold mb-4">
              <span className="bg-gradient-to-r from-electric-blue via-electric-green to-electric-purple bg-clip-text text-transparent">
                Create. Generate. Innovate.
              </span>
            </h1>
            
            <p className="text-xl text-text-secondary max-w-2xl mx-auto leading-relaxed">
              Transform your ideas into professional courses and comprehensive books with our AI-powered n8n workflows.
            </p>
          </div>
        </div>
      </motion.section>

      {/* Quick Actions */}
      <motion.section variants={itemVariants} className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-text-primary">Quick Actions</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/tasks')}
            icon={<Activity className="w-4 h-4" />}
          >
            View All Tasks
          </Button>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Card
                key={action.title}
                hover
                gradient
                onClick={() => navigate(action.route)}
                className="p-6 h-full"
              >
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className={`p-3 bg-${action.color}/10 border border-${action.color}/20 rounded-xl`}>
                      <Icon className={`w-6 h-6 text-${action.color}`} />
                    </div>
                    <ArrowRight className="w-5 h-5 text-text-muted group-hover:text-electric-blue transition-colors" />
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold text-text-primary">{action.title}</h3>
                    <p className="text-text-secondary">{action.description}</p>
                  </div>

                  <div className="space-y-2">
                    {action.features.map((feature) => (
                      <div key={feature} className="flex items-center space-x-2 text-sm text-text-muted">
                        <div className="w-1.5 h-1.5 bg-electric-blue rounded-full"></div>
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Button variant="outline" size="sm" className="w-full">
                    <Play className="w-4 h-4 mr-2" />
                    Get Started
                  </Button>
                </div>
              </Card>
            );
          })}
        </div>
      </motion.section>

      {/* Statistics */}
      <motion.section variants={itemVariants} className="space-y-6">
        <h2 className="text-2xl font-bold text-text-primary">Statistics</h2>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={stat.label} className="p-4" hover>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Icon className={`w-5 h-5 ${stat.color}`} />
                    <span className="text-xs text-electric-green font-medium">{stat.change}</span>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-text-primary">{stat.value}</div>
                    <div className="text-sm text-text-muted">{stat.label}</div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </motion.section>

      {/* Recent Activity */}
      <motion.section variants={itemVariants} className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-text-primary">Recent Activity</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/tasks')}
          >
            View All
          </Button>
        </div>

        {recentTasks.length === 0 ? (
          <Card className="p-8 text-center">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-electric-blue/10 border border-electric-blue/20 rounded-full flex items-center justify-center mx-auto">
                <Zap className="w-8 h-8 text-electric-blue" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-text-primary mb-2">No recent activity</h3>
                <p className="text-text-muted mb-4">Start creating your first course or book to see activity here.</p>
                <div className="flex items-center justify-center space-x-4">
                  <Button onClick={() => navigate('/course')} size="sm">
                    Create Course
                  </Button>
                  <Button onClick={() => navigate('/book')} variant="outline" size="sm">
                    Generate Book
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        ) : (
          <div className="space-y-3">
            {recentTasks.map((task, index) => (
              <Card key={task.id} className="p-4" hover>
                <div className="flex items-center space-x-4">
                  <div className={`p-2 rounded-lg ${
                    task.type === 'course' 
                      ? 'bg-electric-blue/10 border border-electric-blue/20' 
                      : 'bg-electric-green/10 border border-electric-green/20'
                  }`}>
                    {task.type === 'course' ? (
                      <GraduationCap className={`w-4 h-4 ${
                        task.type === 'course' ? 'text-electric-blue' : 'text-electric-green'
                      }`} />
                    ) : (
                      <BookOpen className={`w-4 h-4 ${
                        task.type === 'course' ? 'text-electric-blue' : 'text-electric-green'
                      }`} />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-text-primary truncate">{task.title}</h4>
                    <div className="flex items-center space-x-2 text-sm text-text-muted">
                      <span className="capitalize">{task.status.status}</span>
                      <span>•</span>
                      <span>{task.status.progress}%</span>
                      <span>•</span>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{new Date(task.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    task.status.status === 'completed' 
                      ? 'bg-electric-green/10 text-electric-green border border-electric-green/20'
                      : task.status.status === 'failed'
                      ? 'bg-electric-coral/10 text-electric-coral border border-electric-coral/20'
                      : 'bg-electric-blue/10 text-electric-blue border border-electric-blue/20'
                  }`}>
                    {task.status.status}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </motion.section>
    </motion.div>
  );
}