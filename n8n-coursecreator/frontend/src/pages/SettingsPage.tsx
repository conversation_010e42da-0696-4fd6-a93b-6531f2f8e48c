import React from 'react';
import { motion } from 'framer-motion';
import { Settings, Save, RefreshCw } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export function SettingsPage() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-text-primary mb-2">Settings</h1>
        <p className="text-text-secondary">Configure your preferences and API settings</p>
      </div>

      {/* API Configuration */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-4">API Configuration</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              API Base URL
            </label>
            <input
              type="url"
              defaultValue="http://localhost:8000"
              className="input"
              placeholder="http://localhost:8000"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              API Timeout (seconds)
            </label>
            <input
              type="number"
              defaultValue={30}
              className="input"
              placeholder="30"
            />
          </div>
        </div>
      </Card>

      {/* Preferences */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-4">Preferences</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Default Course Style
            </label>
            <select className="input">
              <option value="professional">Professional</option>
              <option value="academic">Academic</option>
              <option value="casual">Casual</option>
              <option value="technical">Technical</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Default Citation Style
            </label>
            <select className="input">
              <option value="APA">APA</option>
              <option value="MLA">MLA</option>
              <option value="Chicago">Chicago</option>
              <option value="Harvard">Harvard</option>
            </select>
          </div>
          
          <div>
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="checkbox"
                defaultChecked
                className="w-4 h-4 text-electric-blue bg-dark-400 border-white/20 rounded focus:ring-electric-blue"
              />
              <div>
                <div className="font-medium text-text-primary">Auto-save progress</div>
                <div className="text-sm text-text-muted">Automatically save your work as you go</div>
              </div>
            </label>
          </div>
          
          <div>
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="checkbox"
                defaultChecked
                className="w-4 h-4 text-electric-blue bg-dark-400 border-white/20 rounded focus:ring-electric-blue"
              />
              <div>
                <div className="font-medium text-text-primary">Enable notifications</div>
                <div className="text-sm text-text-muted">Get notified when tasks complete</div>
              </div>
            </label>
          </div>
        </div>
      </Card>

      {/* Export/Import */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-4">Data Management</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-dark-400 rounded-lg">
            <div>
              <div className="font-medium text-text-primary">Export Settings</div>
              <div className="text-sm text-text-muted">Download your preferences and project data</div>
            </div>
            <Button variant="outline" size="sm">
              Export
            </Button>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-dark-400 rounded-lg">
            <div>
              <div className="font-medium text-text-primary">Import Settings</div>
              <div className="text-sm text-text-muted">Restore from previously exported data</div>
            </div>
            <Button variant="outline" size="sm">
              Import
            </Button>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-electric-coral/5 border border-electric-coral/20 rounded-lg">
            <div>
              <div className="font-medium text-electric-coral">Reset All Settings</div>
              <div className="text-sm text-text-muted">Restore default settings and clear all data</div>
            </div>
            <Button variant="outline" size="sm" className="border-electric-coral text-electric-coral hover:bg-electric-coral/10">
              Reset
            </Button>
          </div>
        </div>
      </Card>

      {/* Actions */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" icon={<RefreshCw className="w-4 h-4" />}>
              Reset to Defaults
            </Button>
          </div>
          
          <Button icon={<Save className="w-4 h-4" />}>
            Save Changes
          </Button>
        </div>
      </Card>
    </div>
  );
}