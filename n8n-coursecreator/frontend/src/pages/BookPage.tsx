import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLef<PERSON>, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { BookWizard, BookData } from '@/components/book/BookWizard';
import { useAppStore } from '@/store/useAppStore';
import { ApiService } from '@/services/api';
import toast from 'react-hot-toast';

export function BookPage() {
  const navigate = useNavigate();
  const { addTask, addRecentProject } = useAppStore();
  const [isGenerating, setIsGenerating] = useState(false);

  const handleBookComplete = async (bookData: BookData) => {
    setIsGenerating(true);
    
    try {
      // Create the book using the full book generation API
      const response = await ApiService.generateFullBook({
        book_title: bookData.title,
        research_data: bookData.research_data,
        style: bookData.style,
        citation_style: bookData.citation_style,
        target_audience: bookData.target_audience,
        estimated_pages: bookData.estimated_pages,
        context: bookData.context,
        include_annotations: bookData.include_annotations,
      });

      if (response.success && response.data?.task_id) {
        // Add task to the store for monitoring
        addTask({
          id: response.data.task_id,
          type: 'book',
          title: bookData.title,
          status: {
            status: 'started',
            progress: 0,
            message: 'Book generation started',
            created_at: new Date().toISOString(),
          },
        });

        // Add to recent projects
        addRecentProject({
          id: response.data.task_id,
          type: 'book',
          title: bookData.title,
          createdAt: new Date(),
          data: bookData,
        });

        toast.success('Book generation started! You can monitor progress in the Tasks section.');
        navigate('/tasks');
      } else {
        throw new Error(response.error || 'Failed to start book generation');
      }
    } catch (error) {
      console.error('Error generating book:', error);
      toast.error('Failed to start book generation. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCancel = () => {
    navigate('/');
  };

  if (isGenerating) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center space-y-6"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            className="w-16 h-16 border-4 border-electric-green border-t-transparent rounded-full mx-auto"
          />
          <div>
            <h2 className="text-2xl font-bold text-text-primary mb-2">Generating Your Book</h2>
            <p className="text-text-muted">
              Please wait while we process your book generation request...
            </p>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/')}
          icon={<ArrowLeft className="w-4 h-4" />}
        >
          Back to Dashboard
        </Button>
        <div className="flex items-center space-x-2">
          <Sparkles className="w-6 h-6 text-electric-green" />
          <h1 className="text-2xl font-bold text-text-primary">Book Generator</h1>
        </div>
      </div>

      {/* Book Wizard */}
      <BookWizard 
        onComplete={handleBookComplete}
        onCancel={handleCancel}
      />
    </div>
  );
}