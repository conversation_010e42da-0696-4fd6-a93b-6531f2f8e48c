import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { RefreshC<PERSON>, CheckCircle, FileText, BookOpen, List } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ApiService } from '@/services/api';
import { BookData } from '../BookWizard';
import toast from 'react-hot-toast';

interface BookOutlineStepProps {
  data: BookData;
  onUpdate: (updates: Partial<BookData>) => void;
}

export function BookOutlineStep({ data, onUpdate }: BookOutlineStepProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(!!data.outline);

  const generateOutline = async () => {
    if (!data.title.trim()) {
      toast.error('Please provide a book title first');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await ApiService.createBookOutline({
        book_title: data.title,
        research_data: data.research_data,
        style: data.style,
        citation_style: data.citation_style,
        target_audience: data.target_audience,
        estimated_pages: data.estimated_pages,
      });

      if (response.success && response.data) {
        onUpdate({ outline: response.data });
        setHasGenerated(true);
        toast.success('Book outline generated successfully!');
      } else {
        throw new Error(response.error || 'Failed to generate outline');
      }
    } catch (error) {
      console.error('Error generating outline:', error);
      toast.error('Failed to generate outline. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const regenerateOutline = () => {
    onUpdate({ outline: undefined });
    setHasGenerated(false);
    generateOutline();
  };

  return (
    <div className="space-y-6">
      {/* Generation Controls */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">Book Outline</h2>
            <p className="text-text-muted">AI-generated book structure and chapters</p>
          </div>
          
          <div className="flex items-center space-x-3">
            {hasGenerated && (
              <Button
                variant="outline"
                onClick={regenerateOutline}
                loading={isGenerating}
                icon={<RefreshCw className="w-4 h-4" />}
              >
                Regenerate
              </Button>
            )}
            {!hasGenerated && (
              <Button
                onClick={generateOutline}
                loading={isGenerating}
                icon={<FileText className="w-4 h-4" />}
              >
                Generate Outline
              </Button>
            )}
          </div>
        </div>

        {/* Book Summary */}
        <div className="grid md:grid-cols-4 gap-4 mb-6">
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <FileText className="w-4 h-4 text-electric-green" />
              <span className="text-sm font-medium text-text-primary">Style</span>
            </div>
            <span className="text-text-muted capitalize">{data.style}</span>
          </div>
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <List className="w-4 h-4 text-electric-blue" />
              <span className="text-sm font-medium text-text-primary">Citation</span>
            </div>
            <span className="text-text-muted">{data.citation_style}</span>
          </div>
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <BookOpen className="w-4 h-4 text-electric-purple" />
              <span className="text-sm font-medium text-text-primary">Pages</span>
            </div>
            <span className="text-text-muted">{data.estimated_pages}</span>
          </div>
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="w-4 h-4 text-electric-coral" />
              <span className="text-sm font-medium text-text-primary">Audience</span>
            </div>
            <span className="text-text-muted">{data.target_audience}</span>
          </div>
        </div>
      </Card>

      {/* Outline Generation Progress */}
      {isGenerating && (
        <Card className="p-8">
          <div className="text-center space-y-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              className="w-12 h-12 border-2 border-electric-green border-t-transparent rounded-full mx-auto"
            />
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-2">Generating Book Outline</h3>
              <p className="text-text-muted">AI is analyzing your requirements and research to create a comprehensive book structure...</p>
            </div>
          </div>
        </Card>
      )}

      {/* Generated Outline */}
      {data.outline && !isGenerating && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="p-6">
            <div className="space-y-6">
              {/* Book Header */}
              <div className="border-b border-white/10 pb-6">
                <h3 className="text-2xl font-bold text-text-primary mb-2">
                  {data.outline.title || data.title}
                </h3>
                {data.outline.subtitle && (
                  <h4 className="text-lg text-electric-green mb-3">{data.outline.subtitle}</h4>
                )}
                {data.outline.description && (
                  <p className="text-text-secondary leading-relaxed">
                    {data.outline.description}
                  </p>
                )}
              </div>

              {/* Main Argument */}
              {data.outline.main_argument && (
                <div>
                  <h4 className="text-lg font-semibold text-text-primary mb-3">Main Argument</h4>
                  <p className="text-text-secondary p-4 bg-dark-400 rounded-lg">
                    {data.outline.main_argument}
                  </p>
                </div>
              )}

              {/* Book Chapters */}
              {data.outline.chapters && data.outline.chapters.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-text-primary mb-4">Book Chapters</h4>
                  <div className="space-y-4">
                    {data.outline.chapters.map((chapter: any, index: number) => (
                      <Card key={index} className="p-4 bg-dark-400">
                        <div className="space-y-3">
                          <div className="flex items-start justify-between">
                            <div>
                              <h5 className="font-semibold text-text-primary">
                                Chapter {index + 1}: {chapter.title}
                              </h5>
                              {chapter.description && (
                                <p className="text-sm text-text-muted mt-1">
                                  {chapter.description}
                                </p>
                              )}
                            </div>
                            {chapter.estimated_pages && (
                              <div className="flex items-center space-x-1 text-sm text-electric-green">
                                <BookOpen className="w-4 h-4" />
                                <span>{chapter.estimated_pages} pages</span>
                              </div>
                            )}
                          </div>

                          {/* Chapter Sections */}
                          {chapter.sections && chapter.sections.length > 0 && (
                            <div className="space-y-2">
                              {chapter.sections.map((section: any, sectionIndex: number) => (
                                <div
                                  key={sectionIndex}
                                  className="flex items-center space-x-3 p-3 bg-dark-500 rounded-lg"
                                >
                                  <div className="w-6 h-6 bg-electric-green/10 border border-electric-green/20 rounded-full flex items-center justify-center">
                                    <span className="text-xs font-medium text-electric-green">
                                      {sectionIndex + 1}
                                    </span>
                                  </div>
                                  <div className="flex-1">
                                    <div className="font-medium text-text-primary text-sm">
                                      {section.title}
                                    </div>
                                    {section.description && (
                                      <div className="text-xs text-text-muted">
                                        {section.description}
                                      </div>
                                    )}
                                  </div>
                                  {section.pages && (
                                    <div className="text-xs text-text-muted">
                                      {section.pages} pages
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}

                          {/* Key Points */}
                          {chapter.key_points && chapter.key_points.length > 0 && (
                            <div className="space-y-2">
                              <h6 className="text-sm font-medium text-text-primary">Key Points:</h6>
                              <div className="grid md:grid-cols-2 gap-2">
                                {chapter.key_points.map((point: string, pointIndex: number) => (
                                  <div key={pointIndex} className="flex items-start space-x-2">
                                    <CheckCircle className="w-4 h-4 text-electric-blue flex-shrink-0 mt-0.5" />
                                    <span className="text-sm text-text-secondary">{point}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Target Audience & Goals */}
              <div className="grid md:grid-cols-2 gap-6">
                {data.outline.target_audience && (
                  <div>
                    <h4 className="text-lg font-semibold text-text-primary mb-3">Target Audience</h4>
                    <p className="text-text-secondary p-4 bg-dark-400 rounded-lg">
                      {data.outline.target_audience}
                    </p>
                  </div>
                )}
                
                {data.outline.estimated_pages && (
                  <div>
                    <h4 className="text-lg font-semibold text-text-primary mb-3">Book Statistics</h4>
                    <div className="p-4 bg-dark-400 rounded-lg space-y-2">
                      <div className="flex justify-between">
                        <span className="text-text-muted">Estimated Pages:</span>
                        <span className="text-text-primary font-medium">{data.outline.estimated_pages}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text-muted">Chapters:</span>
                        <span className="text-text-primary font-medium">{data.outline.chapters?.length || 0}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </motion.div>
      )}

      {!data.outline && !isGenerating && (
        <Card className="p-8 text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 bg-electric-green/10 border border-electric-green/20 rounded-full flex items-center justify-center mx-auto">
              <FileText className="w-8 h-8 text-electric-green" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Ready to Generate Outline</h3>
              <p className="text-text-muted mb-4">
                Click the "Generate Outline" button to create an AI-powered book structure based on your preferences and research.
              </p>
              <Button onClick={generateOutline} icon={<FileText className="w-4 h-4" />}>
                Generate Book Outline
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}