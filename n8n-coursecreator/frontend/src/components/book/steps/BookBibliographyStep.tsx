import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { List, Download, Refresh<PERSON>w, BookO<PERSON>, Copy, ExternalLink } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ApiService } from '@/services/api';
import { BookData } from '../BookWizard';
import toast from 'react-hot-toast';

interface BookBibliographyStepProps {
  data: BookData;
  onUpdate: (updates: Partial<BookData>) => void;
}

export function BookBibliographyStep({ data, onUpdate }: BookBibliographyStepProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(!!data.bibliography);

  const generateBibliography = async () => {
    if (!data.chapters?.length && !data.research_data) {
      toast.error('Please generate chapters or provide research data first');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await ApiService.compileBookBibliography({
        chapters: data.chapters,
        research_data: data.research_data,
        citation_style: data.citation_style,
        include_annotations: data.include_annotations,
      });

      if (response.success && response.data) {
        onUpdate({ bibliography: response.data });
        setHasGenerated(true);
        toast.success('Bibliography generated successfully!');
      } else {
        throw new Error(response.error || 'Failed to generate bibliography');
      }
    } catch (error) {
      console.error('Error generating bibliography:', error);
      toast.error('Failed to generate bibliography. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const regenerateBibliography = () => {
    onUpdate({ bibliography: undefined });
    setHasGenerated(false);
    generateBibliography();
  };

  const copyBibliography = () => {
    if (data.bibliography?.entries) {
      const text = data.bibliography.entries.map((entry: any) => entry.citation).join('\n\n');
      navigator.clipboard.writeText(text);
      toast.success('Bibliography copied to clipboard!');
    }
  };

  return (
    <div className="space-y-6">
      {/* Generation Controls */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">Bibliography</h2>
            <p className="text-text-muted">AI-compiled citations and references</p>
          </div>
          
          <div className="flex items-center space-x-3">
            {hasGenerated && (
              <Button
                variant="outline"
                onClick={regenerateBibliography}
                loading={isGenerating}
                icon={<RefreshCw className="w-4 h-4" />}
              >
                Regenerate
              </Button>
            )}
            {!hasGenerated && (
              <Button
                onClick={generateBibliography}
                loading={isGenerating}
                disabled={!data.chapters?.length && !data.research_data}
                icon={<List className="w-4 h-4" />}
              >
                Generate Bibliography
              </Button>
            )}
          </div>
        </div>

        {/* Bibliography Settings */}
        <div className="grid md:grid-cols-3 gap-4">
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <List className="w-4 h-4 text-electric-blue" />
              <span className="text-sm font-medium text-text-primary">Citation Style</span>
            </div>
            <span className="text-text-muted">{data.citation_style}</span>
          </div>
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <BookOpen className="w-4 h-4 text-electric-green" />
              <span className="text-sm font-medium text-text-primary">Annotations</span>
            </div>
            <span className="text-text-muted">{data.include_annotations ? 'Included' : 'Not included'}</span>
          </div>
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <RefreshCw className="w-4 h-4 text-electric-purple" />
              <span className="text-sm font-medium text-text-primary">Auto-formatted</span>
            </div>
            <span className="text-text-muted">AI Generated</span>
          </div>
        </div>
      </Card>

      {/* Bibliography Generation Progress */}
      {isGenerating && (
        <Card className="p-8">
          <div className="text-center space-y-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              className="w-12 h-12 border-2 border-electric-coral border-t-transparent rounded-full mx-auto"
            />
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-2">Compiling Bibliography</h3>
              <p className="text-text-muted">AI is extracting citations and formatting them according to {data.citation_style} style...</p>
            </div>
          </div>
        </Card>
      )}

      {/* Generated Bibliography */}
      {data.bibliography && !isGenerating && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-6"
        >
          {/* Bibliography Summary */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-text-primary">Bibliography Summary</h3>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={copyBibliography} icon={<Copy className="w-4 h-4" />}>
                  Copy All
                </Button>
                <Button variant="outline" size="sm" icon={<Download className="w-4 h-4" />}>
                  Export
                </Button>
              </div>
            </div>
            
            <div className="grid md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-coral mb-1">
                  {data.bibliography.entries?.length || 0}
                </div>
                <div className="text-sm text-text-muted">Total References</div>
              </div>
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-blue mb-1">
                  {data.bibliography.entries?.filter((entry: any) => entry.type === 'journal').length || 0}
                </div>
                <div className="text-sm text-text-muted">Journal Articles</div>
              </div>
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-green mb-1">
                  {data.bibliography.entries?.filter((entry: any) => entry.type === 'book').length || 0}
                </div>
                <div className="text-sm text-text-muted">Books</div>
              </div>
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-purple mb-1">
                  {data.bibliography.entries?.filter((entry: any) => entry.type === 'web').length || 0}
                </div>
                <div className="text-sm text-text-muted">Web Sources</div>
              </div>
            </div>
          </Card>

          {/* Bibliography Entries */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-text-primary mb-4">References ({data.citation_style} Style)</h3>
            
            {data.bibliography.entries && data.bibliography.entries.length > 0 ? (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {data.bibliography.entries.map((entry: any, index: number) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 bg-dark-400 rounded-lg hover:bg-dark-400/80 transition-colors"
                  >
                    <div className="space-y-3">
                      {/* Entry Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            entry.type === 'journal' ? 'bg-electric-blue/10 text-electric-blue' :
                            entry.type === 'book' ? 'bg-electric-green/10 text-electric-green' :
                            entry.type === 'web' ? 'bg-electric-purple/10 text-electric-purple' :
                            'bg-electric-coral/10 text-electric-coral'
                          }`}>
                            {entry.type?.toUpperCase() || 'SOURCE'}
                          </span>
                          <span className="text-sm text-text-muted">Reference {index + 1}</span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => navigator.clipboard.writeText(entry.citation)}
                            icon={<Copy className="w-3 h-3" />}
                          >
                            Copy
                          </Button>
                          {entry.url && (
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              onClick={() => window.open(entry.url, '_blank')}
                              icon={<ExternalLink className="w-3 h-3" />}
                            >
                              Link
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* Citation Text */}
                      <div className="text-sm text-text-secondary font-mono leading-relaxed">
                        {entry.citation || `Sample citation ${index + 1} in ${data.citation_style} format would appear here.`}
                      </div>

                      {/* Annotation */}
                      {data.include_annotations && entry.annotation && (
                        <div className="border-l-2 border-electric-blue pl-4">
                          <div className="text-xs font-medium text-electric-blue mb-1">Annotation:</div>
                          <div className="text-sm text-text-muted">{entry.annotation}</div>
                        </div>
                      )}

                      {/* Entry Details */}
                      {entry.details && (
                        <div className="flex items-center space-x-4 text-xs text-text-muted">
                          {entry.details.year && (
                            <span>Year: {entry.details.year}</span>
                          )}
                          {entry.details.pages && (
                            <span>Pages: {entry.details.pages}</span>
                          )}
                          {entry.details.doi && (
                            <span>DOI: {entry.details.doi}</span>
                          )}
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <List className="w-12 h-12 text-text-muted mx-auto mb-4" />
                <p className="text-text-muted">No bibliography entries found. Generate chapters first to extract citations.</p>
              </div>
            )}
          </Card>

          {/* Bibliography Statistics */}
          {data.bibliography.statistics && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-text-primary mb-4">Citation Analysis</h3>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="p-4 bg-dark-400 rounded-lg">
                  <div className="text-sm text-text-muted mb-1">Most Cited Author</div>
                  <div className="font-medium text-text-primary">
                    {data.bibliography.statistics.top_author || 'Various Authors'}
                  </div>
                </div>
                <div className="p-4 bg-dark-400 rounded-lg">
                  <div className="text-sm text-text-muted mb-1">Publication Years</div>
                  <div className="font-medium text-text-primary">
                    {data.bibliography.statistics.year_range || '2020-2024'}
                  </div>
                </div>
                <div className="p-4 bg-dark-400 rounded-lg">
                  <div className="text-sm text-text-muted mb-1">Average Age</div>
                  <div className="font-medium text-text-primary">
                    {data.bibliography.statistics.avg_age || '3.2'} years
                  </div>
                </div>
              </div>
            </Card>
          )}
        </motion.div>
      )}

      {!data.bibliography && !isGenerating && (
        <Card className="p-8 text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 bg-electric-coral/10 border border-electric-coral/20 rounded-full flex items-center justify-center mx-auto">
              <List className="w-8 h-8 text-electric-coral" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Ready to Generate Bibliography</h3>
              <p className="text-text-muted mb-4">
                {!data.chapters?.length && !data.research_data
                  ? 'Generate chapters or provide research data first, then compile a formatted bibliography.'
                  : 'Compile and format all citations into a comprehensive bibliography.'
                }
              </p>
              <Button 
                onClick={generateBibliography} 
                disabled={!data.chapters?.length && !data.research_data}
                icon={<List className="w-4 h-4" />}
              >
                Generate Bibliography
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}