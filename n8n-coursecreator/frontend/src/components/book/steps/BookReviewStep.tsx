import React from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  FileText, 
  BookOpen, 
  List,
  Download, 
  Share,
  Calendar,
  Users,
  Search,
  Sparkles
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { BookData } from '../BookWizard';

interface BookReviewStepProps {
  data: BookData;
  onUpdate: (updates: Partial<BookData>) => void;
}

export function BookReviewStep({ data }: BookReviewStepProps) {
  const completionStats = {
    research: !!data.research_data,
    outline: !!data.outline,
    chapters: !!(data.chapters && data.chapters.length > 0),
    bibliography: !!data.bibliography,
  };

  const completedCount = Object.values(completionStats).filter(Boolean).length;
  const totalSteps = Object.keys(completionStats).length;
  const completionPercentage = Math.round((completedCount / totalSteps) * 100);

  const bookStats = {
    chapters: data.outline?.chapters?.length || data.chapters?.length || 0,
    estimatedPages: data.estimated_pages || 0,
    generatedChapters: data.chapters?.length || 0,
    citations: data.bibliography?.entries?.length || 0,
    researchFiles: data.research_data ? 1 : 0,
  };

  return (
    <div className="space-y-6">
      {/* Book Overview */}
      <Card className="p-6">
        <div className="text-center space-y-4 mb-6">
          <div className="w-16 h-16 bg-electric-green/10 border border-electric-green/20 rounded-full flex items-center justify-center mx-auto">
            <BookOpen className="w-8 h-8 text-electric-green" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-text-primary mb-2">{data.title}</h2>
            {data.subtitle && (
              <h3 className="text-lg text-electric-green mb-2">{data.subtitle}</h3>
            )}
            {data.description && (
              <p className="text-text-secondary max-w-2xl mx-auto">{data.description}</p>
            )}
          </div>
        </div>

        {/* Completion Progress */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-lg font-semibold text-text-primary">Book Completion</span>
            <span className="text-2xl font-bold text-electric-green">{completionPercentage}%</span>
          </div>
          
          <div className="progress-bar">
            <motion.div
              className="progress-fill"
              initial={{ width: 0 }}
              animate={{ width: `${completionPercentage}%` }}
              transition={{ duration: 1, ease: 'easeOut' }}
            />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Search className={`w-5 h-5 ${completionStats.research ? 'text-electric-green' : 'text-text-muted'}`} />
              <span className="text-sm text-text-secondary">Research</span>
            </div>
            <div className="flex items-center space-x-2">
              <FileText className={`w-5 h-5 ${completionStats.outline ? 'text-electric-green' : 'text-text-muted'}`} />
              <span className="text-sm text-text-secondary">Outline</span>
            </div>
            <div className="flex items-center space-x-2">
              <BookOpen className={`w-5 h-5 ${completionStats.chapters ? 'text-electric-green' : 'text-text-muted'}`} />
              <span className="text-sm text-text-secondary">Chapters</span>
            </div>
            <div className="flex items-center space-x-2">
              <List className={`w-5 h-5 ${completionStats.bibliography ? 'text-electric-green' : 'text-text-muted'}`} />
              <span className="text-sm text-text-secondary">Bibliography</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Book Statistics */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-text-primary mb-4">Book Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center p-4 bg-dark-400 rounded-lg">
            <div className="text-2xl font-bold text-electric-green mb-1">{bookStats.chapters}</div>
            <div className="text-sm text-text-muted">Chapters</div>
          </div>
          <div className="text-center p-4 bg-dark-400 rounded-lg">
            <div className="text-2xl font-bold text-electric-blue mb-1">{bookStats.estimatedPages}</div>
            <div className="text-sm text-text-muted">Est. Pages</div>
          </div>
          <div className="text-center p-4 bg-dark-400 rounded-lg">
            <div className="text-2xl font-bold text-electric-purple mb-1">{bookStats.generatedChapters}</div>
            <div className="text-sm text-text-muted">Generated</div>
          </div>
          <div className="text-center p-4 bg-dark-400 rounded-lg">
            <div className="text-2xl font-bold text-electric-coral mb-1">{bookStats.citations}</div>
            <div className="text-sm text-text-muted">Citations</div>
          </div>
          <div className="text-center p-4 bg-dark-400 rounded-lg">
            <div className="text-2xl font-bold text-electric-pink mb-1">{bookStats.researchFiles}</div>
            <div className="text-sm text-text-muted">Research</div>
          </div>
        </div>
      </Card>

      {/* Book Details */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Book Information</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Users className="w-5 h-5 text-electric-green" />
              <div>
                <div className="text-sm text-text-muted">Target Audience</div>
                <div className="text-text-primary">{data.target_audience}</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <BookOpen className="w-5 h-5 text-electric-blue" />
              <div>
                <div className="text-sm text-text-muted">Estimated Pages</div>
                <div className="text-text-primary">{data.estimated_pages}</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <FileText className="w-5 h-5 text-electric-purple" />
              <div>
                <div className="text-sm text-text-muted">Writing Style</div>
                <div className="text-text-primary capitalize">{data.style}</div>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Format & Style</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <List className="w-5 h-5 text-electric-coral" />
              <div>
                <div className="text-sm text-text-muted">Citation Style</div>
                <div className="text-text-primary">{data.citation_style}</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-5 h-5 text-electric-pink" />
              <div>
                <div className="text-sm text-text-muted">Annotations</div>
                <div className="text-text-primary">{data.include_annotations ? 'Included' : 'Not included'}</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Calendar className="w-5 h-5 text-electric-blue" />
              <div>
                <div className="text-sm text-text-muted">Created</div>
                <div className="text-text-primary">{new Date().toLocaleDateString()}</div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Book Outline Preview */}
      {data.outline && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Book Structure</h3>
          <div className="space-y-3 max-h-60 overflow-y-auto">
            {data.outline.chapters?.map((chapter: any, index: number) => (
              <div key={index} className="p-3 bg-dark-400 rounded-lg">
                <div className="font-medium text-text-primary">
                  Chapter {index + 1}: {chapter.title}
                </div>
                {chapter.description && (
                  <div className="text-sm text-text-muted mt-1">
                    {chapter.description}
                  </div>
                )}
                <div className="text-xs text-electric-green mt-2">
                  {chapter.sections?.length || 0} section{(chapter.sections?.length || 0) !== 1 ? 's' : ''}
                  {chapter.estimated_pages && ` • ${chapter.estimated_pages} pages`}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Research Summary */}
      {data.research_data && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Research Foundation</h3>
          <div className="flex items-center space-x-4 p-4 bg-dark-400 rounded-lg">
            <Search className="w-8 h-8 text-electric-blue" />
            <div>
              <div className="font-medium text-text-primary">Research Data Processed</div>
              <div className="text-sm text-text-muted">
                Your research materials have been analyzed and integrated into the book content.
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Additional Context */}
      {data.context && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Additional Context</h3>
          <p className="text-text-secondary">{data.context}</p>
        </Card>
      )}

      {/* Action Buttons */}
      <Card className="p-6">
        <div className="flex items-center justify-center space-x-4">
          <Button variant="outline" icon={<Download className="w-4 h-4" />}>
            Export Book
          </Button>
          <Button variant="outline" icon={<Share className="w-4 h-4" />}>
            Share Book
          </Button>
        </div>
      </Card>

      {/* Completion Status */}
      {completionPercentage < 100 && (
        <Card className="p-6 bg-electric-green/5 border-electric-green/20">
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-electric-green">Complete Your Book</h3>
            <p className="text-text-secondary">
              Your book is {completionPercentage}% complete. Generate the remaining components to create a comprehensive book.
            </p>
            <div className="space-y-2">
              {!completionStats.research && (
                <div className="flex items-center space-x-2 text-sm text-text-muted">
                  <Search className="w-4 h-4" />
                  <span>Process research data (optional)</span>
                </div>
              )}
              {!completionStats.outline && (
                <div className="flex items-center space-x-2 text-sm text-text-muted">
                  <FileText className="w-4 h-4" />
                  <span>Generate book outline</span>
                </div>
              )}
              {!completionStats.chapters && (
                <div className="flex items-center space-x-2 text-sm text-text-muted">
                  <BookOpen className="w-4 h-4" />
                  <span>Generate book chapters</span>
                </div>
              )}
              {!completionStats.bibliography && (
                <div className="flex items-center space-x-2 text-sm text-text-muted">
                  <List className="w-4 h-4" />
                  <span>Compile bibliography</span>
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {completionPercentage === 100 && (
        <Card className="p-6 bg-electric-green/5 border-electric-green/20">
          <div className="text-center space-y-3">
            <CheckCircle className="w-12 h-12 text-electric-green mx-auto" />
            <h3 className="text-lg font-semibold text-electric-green">Book Ready!</h3>
            <p className="text-text-secondary">
              Your book is complete and ready to be generated. Click "Generate Book" to create the final package.
            </p>
            <div className="flex items-center justify-center space-x-2 text-sm text-electric-green">
              <Sparkles className="w-4 h-4" />
              <span>All components generated successfully</span>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}