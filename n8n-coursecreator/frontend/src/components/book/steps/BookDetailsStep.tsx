import React from 'react';
import { motion } from 'framer-motion';
import { Info } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { BookData } from '../BookWizard';

interface BookDetailsStepProps {
  data: BookData;
  onUpdate: (updates: Partial<BookData>) => void;
}

const styleOptions = [
  { value: 'academic', label: 'Academic', description: 'Scholarly, research-based writing' },
  { value: 'professional', label: 'Professional', description: 'Business-focused, authoritative' },
  { value: 'popular', label: 'Popular', description: 'General audience, accessible' },
  { value: 'technical', label: 'Technical', description: 'Detailed, technical content' },
];

const citationOptions = [
  { value: 'APA', label: 'APA', description: 'American Psychological Association' },
  { value: 'MLA', label: 'MLA', description: 'Modern Language Association' },
  { value: 'Chicago', label: 'Chicago', description: 'Chicago Manual of Style' },
  { value: 'Harvard', label: 'Harvard', description: 'Harvard referencing system' },
];

const audienceOptions = [
  { value: 'general readers', label: 'General Readers', description: 'Broad public audience' },
  { value: 'students', label: 'Students', description: 'Academic learners' },
  { value: 'professionals', label: 'Professionals', description: 'Industry practitioners' },
  { value: 'researchers', label: 'Researchers', description: 'Academic researchers' },
  { value: 'specialists', label: 'Specialists', description: 'Subject matter experts' },
];

const pageLengthOptions = [
  { value: 100, label: '100 pages', description: 'Short book or guide' },
  { value: 200, label: '200 pages', description: 'Standard length book' },
  { value: 300, label: '300 pages', description: 'Comprehensive treatment' },
  { value: 400, label: '400+ pages', description: 'Extensive, detailed work' },
];

export function BookDetailsStep({ data, onUpdate }: BookDetailsStepProps) {
  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold text-text-primary mb-4">Book Information</h2>
            
            <div className="grid gap-6">
              {/* Book Title */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Book Title *
                </label>
                <input
                  type="text"
                  value={data.title}
                  onChange={(e) => onUpdate({ title: e.target.value })}
                  placeholder="Enter your book title..."
                  className="input"
                />
              </div>

              {/* Book Subtitle */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Subtitle
                </label>
                <input
                  type="text"
                  value={data.subtitle}
                  onChange={(e) => onUpdate({ subtitle: e.target.value })}
                  placeholder="Optional subtitle..."
                  className="input"
                />
              </div>

              {/* Book Description */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Description
                </label>
                <textarea
                  value={data.description}
                  onChange={(e) => onUpdate({ description: e.target.value })}
                  placeholder="Describe what your book will cover..."
                  className="textarea"
                  rows={4}
                />
              </div>

              {/* Target Audience */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Target Audience
                </label>
                <input
                  type="text"
                  value={data.target_audience}
                  onChange={(e) => onUpdate({ target_audience: e.target.value })}
                  placeholder="e.g., students, professionals, general readers..."
                  className="input"
                />
              </div>

              {/* Additional Context */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Additional Context
                </label>
                <textarea
                  value={data.context}
                  onChange={(e) => onUpdate({ context: e.target.value })}
                  placeholder="Any specific requirements, themes, or additional information..."
                  className="textarea"
                  rows={3}
                />
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Writing Style */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Writing Style</h3>
        <div className="grid md:grid-cols-2 gap-4">
          {styleOptions.map((option) => (
            <motion.div
              key={option.value}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                data.style === option.value
                  ? 'border-electric-green bg-electric-green/5'
                  : 'border-white/10 hover:border-electric-green/30'
              }`}
              onClick={() => onUpdate({ style: option.value })}
            >
              <div className="font-medium text-text-primary">{option.label}</div>
              <div className="text-sm text-text-muted">{option.description}</div>
            </motion.div>
          ))}
        </div>
      </Card>

      {/* Citation Style */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Citation Style</h3>
        <div className="grid md:grid-cols-2 gap-4">
          {citationOptions.map((option) => (
            <motion.div
              key={option.value}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                data.citation_style === option.value
                  ? 'border-electric-blue bg-electric-blue/5'
                  : 'border-white/10 hover:border-electric-blue/30'
              }`}
              onClick={() => onUpdate({ citation_style: option.value })}
            >
              <div className="font-medium text-text-primary">{option.label}</div>
              <div className="text-sm text-text-muted">{option.description}</div>
            </motion.div>
          ))}
        </div>
      </Card>

      {/* Target Audience & Length */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Target Audience</h3>
          <div className="space-y-3">
            {audienceOptions.map((option) => (
              <motion.div
                key={option.value}
                whileHover={{ scale: 1.01 }}
                className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                  data.target_audience === option.value
                    ? 'border-electric-purple bg-electric-purple/5'
                    : 'border-white/10 hover:border-electric-purple/30'
                }`}
                onClick={() => onUpdate({ target_audience: option.value })}
              >
                <div className="font-medium text-text-primary">{option.label}</div>
                <div className="text-sm text-text-muted">{option.description}</div>
              </motion.div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Estimated Length</h3>
          <div className="space-y-3">
            {pageLengthOptions.map((option) => (
              <motion.div
                key={option.value}
                whileHover={{ scale: 1.01 }}
                className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                  data.estimated_pages === option.value
                    ? 'border-electric-coral bg-electric-coral/5'
                    : 'border-white/10 hover:border-electric-coral/30'
                }`}
                onClick={() => onUpdate({ estimated_pages: option.value })}
              >
                <div className="font-medium text-text-primary">{option.label}</div>
                <div className="text-sm text-text-muted">{option.description}</div>
              </motion.div>
            ))}
          </div>
        </Card>
      </div>

      {/* Options */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Additional Options</h3>
        <div className="space-y-4">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={data.include_annotations}
              onChange={(e) => onUpdate({ include_annotations: e.target.checked })}
              className="w-4 h-4 text-electric-green bg-dark-400 border-white/20 rounded focus:ring-electric-green"
            />
            <div>
              <div className="font-medium text-text-primary">Include Annotations</div>
              <div className="text-sm text-text-muted">Add detailed annotations to the bibliography</div>
            </div>
          </label>
        </div>
      </Card>

      {/* Info Card */}
      <Card className="p-4 bg-electric-green/5 border-electric-green/20">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-electric-green flex-shrink-0 mt-0.5" />
          <div className="text-sm text-text-secondary">
            <p className="font-medium text-electric-green mb-1">AI Book Generation</p>
            <p>
              Our AI will process your research data, generate a comprehensive outline, 
              create detailed chapters, and compile a proper bibliography. The more 
              detailed your inputs, the better the final book will be.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
}