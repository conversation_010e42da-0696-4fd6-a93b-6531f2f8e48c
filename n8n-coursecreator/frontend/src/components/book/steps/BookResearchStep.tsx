import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useDropzone } from 'react-dropzone';
import { 
  Upload, 
  FileText, 
  File, 
  X, 
  CheckCircle, 
  AlertCircle,
  Search,
  Plus,
  Globe
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ApiService } from '@/services/api';
import { BookData } from '../BookWizard';
import toast from 'react-hot-toast';

interface BookResearchStepProps {
  data: BookData;
  onUpdate: (updates: Partial<BookData>) => void;
}

interface ResearchFile {
  id: string;
  name: string;
  size: number;
  type: string;
  content?: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
}

export function BookResearchStep({ data, onUpdate }: BookResearchStepProps) {
  const [files, setFiles] = useState<ResearchFile[]>([]);
  const [webSources, setWebSources] = useState<string[]>(['']);
  const [textInput, setTextInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: ResearchFile[] = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'pending'
    }));
    
    setFiles(prev => [...prev, ...newFiles]);
    
    // Process files
    processFiles(newFiles, acceptedFiles);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/csv': ['.csv'],
      'application/json': ['.json']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const processFiles = async (newFiles: ResearchFile[], fileObjects: File[]) => {
    for (let i = 0; i < newFiles.length; i++) {
      const file = newFiles[i];
      const fileObject = fileObjects[i];
      
      try {
        // Update status to processing
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'processing' } : f
        ));

        // Read file content
        const content = await readFileContent(fileObject);
        
        // Update with content and completed status
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, content, status: 'completed' } : f
        ));
        
        toast.success(`${file.name} processed successfully`);
      } catch (error) {
        console.error('Error processing file:', error);
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'error' } : f
        ));
        toast.error(`Failed to process ${file.name}`);
      }
    }
  };

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = reject;
      reader.readAsText(file);
    });
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const addWebSource = () => {
    setWebSources(prev => [...prev, '']);
  };

  const updateWebSource = (index: number, value: string) => {
    setWebSources(prev => prev.map((source, i) => i === index ? value : source));
  };

  const removeWebSource = (index: number) => {
    setWebSources(prev => prev.filter((_, i) => i !== index));
  };

  const processResearchData = async () => {
    setIsProcessing(true);
    
    try {
      const researchData = {
        files: files.filter(f => f.status === 'completed').map(f => ({
          name: f.name,
          content: f.content,
          type: f.type
        })),
        webSources: webSources.filter(source => source.trim() !== ''),
        textInput: textInput.trim(),
      };

      if (researchData.files.length === 0 && researchData.webSources.length === 0 && !researchData.textInput) {
        // No research data - that's okay, proceed without it
        onUpdate({ research_data: null });
        toast.success('Proceeding without research data');
        return;
      }

      const response = await ApiService.processBookResearch({
        book_title: data.title,
        research_data: researchData
      });

      if (response.success) {
        onUpdate({ research_data: response.data });
        toast.success('Research data processed successfully!');
      } else {
        throw new Error(response.error || 'Failed to process research data');
      }
    } catch (error) {
      console.error('Error processing research:', error);
      toast.error('Failed to process research data. You can proceed without it.');
      // Allow proceeding even if research processing fails
      onUpdate({ research_data: null });
    } finally {
      setIsProcessing(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* File Upload */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-text-primary mb-4">Research Files</h2>
        
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200 ${
            isDragActive
              ? 'border-electric-green bg-electric-green/5'
              : 'border-white/20 hover:border-electric-green/50'
          }`}
        >
          <input {...getInputProps()} />
          <Upload className="w-12 h-12 text-electric-green mx-auto mb-4" />
          <div>
            <p className="text-lg font-medium text-text-primary mb-2">
              {isDragActive ? 'Drop files here' : 'Upload Research Files'}
            </p>
            <p className="text-text-muted">
              Drop files here or click to browse. Supports PDF, DOC, DOCX, TXT, CSV, JSON (max 10MB)
            </p>
          </div>
        </div>

        {/* Uploaded Files */}
        {files.length > 0 && (
          <div className="mt-6 space-y-3">
            <h3 className="font-medium text-text-primary">Uploaded Files</h3>
            {files.map((file) => (
              <motion.div
                key={file.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center space-x-3 p-3 bg-dark-400 rounded-lg"
              >
                <FileText className="w-5 h-5 text-electric-blue flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-text-primary truncate">{file.name}</div>
                  <div className="text-sm text-text-muted">{formatFileSize(file.size)}</div>
                </div>
                <div className="flex items-center space-x-2">
                  {file.status === 'pending' && (
                    <div className="w-5 h-5 text-text-muted">
                      <File className="w-5 h-5" />
                    </div>
                  )}
                  {file.status === 'processing' && (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                      className="w-5 h-5 border-2 border-electric-blue border-t-transparent rounded-full"
                    />
                  )}
                  {file.status === 'completed' && (
                    <CheckCircle className="w-5 h-5 text-electric-green" />
                  )}
                  {file.status === 'error' && (
                    <AlertCircle className="w-5 h-5 text-electric-coral" />
                  )}
                  <button
                    onClick={() => removeFile(file.id)}
                    className="p-1 hover:bg-white/10 rounded transition-colors"
                  >
                    <X className="w-4 h-4 text-text-muted" />
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </Card>

      {/* Web Sources */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text-primary">Web Sources</h3>
          <Button variant="outline" size="sm" onClick={addWebSource} icon={<Plus className="w-4 h-4" />}>
            Add Source
          </Button>
        </div>
        
        <div className="space-y-3">
          {webSources.map((source, index) => (
            <div key={index} className="flex items-center space-x-3">
              <Globe className="w-5 h-5 text-electric-blue flex-shrink-0" />
              <input
                type="url"
                value={source}
                onChange={(e) => updateWebSource(index, e.target.value)}
                placeholder="https://example.com/research-article"
                className="input flex-1"
              />
              {webSources.length > 1 && (
                <button
                  onClick={() => removeWebSource(index)}
                  className="p-2 hover:bg-white/10 rounded transition-colors"
                >
                  <X className="w-4 h-4 text-text-muted" />
                </button>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Text Input */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Direct Text Input</h3>
        <textarea
          value={textInput}
          onChange={(e) => setTextInput(e.target.value)}
          placeholder="Paste research text, notes, or any relevant content here..."
          className="textarea min-h-[150px]"
          rows={8}
        />
      </Card>

      {/* Process Button */}
      <Card className="p-6">
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold text-text-primary">Process Research Data</h3>
          <p className="text-text-muted">
            Process your research materials to extract key information for book generation.
            This step is optional - you can proceed without research data.
          </p>
          <div className="flex items-center justify-center space-x-4">
            <Button
              onClick={processResearchData}
              loading={isProcessing}
              icon={<Search className="w-4 h-4" />}
            >
              Process Research
            </Button>
            <Button
              variant="outline"
              onClick={() => onUpdate({ research_data: null })}
            >
              Skip Research
            </Button>
          </div>
        </div>
      </Card>

      {/* Research Status */}
      {data.research_data && (
        <Card className="p-4 bg-electric-green/5 border-electric-green/20">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-5 h-5 text-electric-green" />
            <div>
              <div className="font-medium text-electric-green">Research Data Processed</div>
              <div className="text-sm text-text-muted">
                Your research has been processed and is ready for book generation.
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}