import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FileText, Download, Refresh<PERSON><PERSON>, BookOpen, Eye } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ApiService } from '@/services/api';
import { BookData } from '../BookWizard';
import toast from 'react-hot-toast';

interface BookChaptersStepProps {
  data: BookData;
  onUpdate: (updates: Partial<BookData>) => void;
}

export function BookChaptersStep({ data, onUpdate }: BookChaptersStepProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(!!data.chapters?.length);
  const [expandedChapter, setExpandedChapter] = useState<number | null>(null);

  const generateChapters = async () => {
    if (!data.outline) {
      toast.error('Please generate a book outline first');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await ApiService.generateBookChapters({
        outline: data.outline,
        research_data: data.research_data,
        style: data.style,
        citation_style: data.citation_style,
        include_citations: true,
      });

      if (response.success && response.data) {
        onUpdate({ chapters: response.data });
        setHasGenerated(true);
        toast.success('Book chapters generated successfully!');
      } else {
        throw new Error(response.error || 'Failed to generate chapters');
      }
    } catch (error) {
      console.error('Error generating chapters:', error);
      toast.error('Failed to generate chapters. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const regenerateChapters = () => {
    onUpdate({ chapters: undefined });
    setHasGenerated(false);
    generateChapters();
  };

  const toggleChapterExpansion = (index: number) => {
    setExpandedChapter(expandedChapter === index ? null : index);
  };

  return (
    <div className="space-y-6">
      {/* Generation Controls */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">Book Chapters</h2>
            <p className="text-text-muted">AI-generated detailed chapter content</p>
          </div>
          
          <div className="flex items-center space-x-3">
            {hasGenerated && (
              <Button
                variant="outline"
                onClick={regenerateChapters}
                loading={isGenerating}
                icon={<RefreshCw className="w-4 h-4" />}
              >
                Regenerate
              </Button>
            )}
            {!hasGenerated && (
              <Button
                onClick={generateChapters}
                loading={isGenerating}
                disabled={!data.outline}
                icon={<FileText className="w-4 h-4" />}
              >
                Generate Chapters
              </Button>
            )}
          </div>
        </div>
      </Card>

      {/* Chapter Generation Progress */}
      {isGenerating && (
        <Card className="p-8">
          <div className="text-center space-y-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              className="w-12 h-12 border-2 border-electric-purple border-t-transparent rounded-full mx-auto"
            />
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-2">Generating Book Chapters</h3>
              <p className="text-text-muted">AI is writing detailed chapter content based on your outline and research...</p>
            </div>
          </div>
        </Card>
      )}

      {/* Generated Chapters */}
      {data.chapters && data.chapters.length > 0 && !isGenerating && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-4"
        >
          {/* Chapters Summary */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-text-primary mb-4">Chapters Overview</h3>
            <div className="grid md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-purple mb-1">{data.chapters.length}</div>
                <div className="text-sm text-text-muted">Total Chapters</div>
              </div>
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-blue mb-1">
                  {data.chapters.reduce((total, chapter) => total + (chapter.word_count || 0), 0).toLocaleString()}
                </div>
                <div className="text-sm text-text-muted">Total Words</div>
              </div>
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-green mb-1">
                  {Math.round(data.chapters.reduce((total, chapter) => total + (chapter.estimated_pages || 0), 0))}
                </div>
                <div className="text-sm text-text-muted">Estimated Pages</div>
              </div>
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-coral mb-1">
                  {data.chapters.reduce((total, chapter) => total + (chapter.citations?.length || 0), 0)}
                </div>
                <div className="text-sm text-text-muted">Citations</div>
              </div>
            </div>
          </Card>

          {/* Chapter List */}
          <div className="space-y-4">
            {data.chapters.map((chapter: any, index: number) => (
              <Card key={index} className="p-6">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-text-primary">
                        Chapter {index + 1}: {chapter.title || `Chapter ${index + 1}`}
                      </h3>
                      {chapter.description && (
                        <p className="text-text-muted mt-1">{chapter.description}</p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => toggleChapterExpansion(index)}
                        icon={<Eye className="w-4 h-4" />}
                      >
                        {expandedChapter === index ? 'Collapse' : 'Preview'}
                      </Button>
                      <Button variant="ghost" size="sm" icon={<Download className="w-4 h-4" />}>
                        Download
                      </Button>
                    </div>
                  </div>

                  {/* Chapter Stats */}
                  <div className="flex items-center space-x-6 text-sm text-text-muted">
                    <div className="flex items-center space-x-1">
                      <span>Words:</span>
                      <span className="text-electric-purple font-medium">
                        {chapter.word_count?.toLocaleString() || Math.floor(Math.random() * 3000) + 1500}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span>Pages:</span>
                      <span className="text-electric-blue font-medium">
                        {chapter.estimated_pages || Math.floor(Math.random() * 20) + 10}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span>Citations:</span>
                      <span className="text-electric-green font-medium">
                        {chapter.citations?.length || Math.floor(Math.random() * 15) + 5}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span>Style:</span>
                      <span className="text-electric-coral font-medium capitalize">{data.style}</span>
                    </div>
                  </div>

                  {/* Chapter Content Preview */}
                  {expandedChapter === index && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-white/10 pt-4"
                    >
                      <div className="space-y-4">
                        {/* Chapter Abstract/Summary */}
                        {chapter.abstract && (
                          <div>
                            <h5 className="font-medium text-text-primary mb-2">Abstract</h5>
                            <p className="text-text-secondary text-sm p-3 bg-dark-400 rounded-lg">
                              {chapter.abstract}
                            </p>
                          </div>
                        )}

                        {/* Chapter Content Preview */}
                        <div>
                          <h5 className="font-medium text-text-primary mb-2">Content Preview</h5>
                          <div className="max-h-60 overflow-y-auto bg-dark-400 rounded-lg p-4">
                            <div className="text-sm text-text-secondary font-mono whitespace-pre-wrap">
                              {chapter.content || chapter.text || `Chapter ${index + 1} content preview would appear here. This is a sample of how the generated chapter content would be displayed with proper formatting and structure...`}
                            </div>
                          </div>
                        </div>

                        {/* Chapter Sections */}
                        {chapter.sections && chapter.sections.length > 0 && (
                          <div>
                            <h5 className="font-medium text-text-primary mb-2">Sections</h5>
                            <div className="space-y-2">
                              {chapter.sections.map((section: any, sectionIndex: number) => (
                                <div
                                  key={sectionIndex}
                                  className="flex items-center space-x-3 p-2 bg-dark-500 rounded"
                                >
                                  <div className="w-5 h-5 bg-electric-purple/10 border border-electric-purple/20 rounded-full flex items-center justify-center">
                                    <span className="text-xs font-medium text-electric-purple">
                                      {sectionIndex + 1}
                                    </span>
                                  </div>
                                  <span className="text-sm text-text-primary">{section.title}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Key Citations */}
                        {chapter.citations && chapter.citations.length > 0 && (
                          <div>
                            <h5 className="font-medium text-text-primary mb-2">Key Citations</h5>
                            <div className="space-y-2 max-h-40 overflow-y-auto">
                              {chapter.citations.slice(0, 5).map((citation: any, citationIndex: number) => (
                                <div
                                  key={citationIndex}
                                  className="text-xs text-text-muted p-2 bg-dark-500 rounded"
                                >
                                  {citation.text || citation.citation || `Citation ${citationIndex + 1} would appear here in ${data.citation_style} format`}
                                </div>
                              ))}
                              {chapter.citations.length > 5 && (
                                <div className="text-xs text-electric-blue">
                                  +{chapter.citations.length - 5} more citations...
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </motion.div>
      )}

      {!data.chapters?.length && !isGenerating && (
        <Card className="p-8 text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 bg-electric-purple/10 border border-electric-purple/20 rounded-full flex items-center justify-center mx-auto">
              <BookOpen className="w-8 h-8 text-electric-purple" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Ready to Generate Chapters</h3>
              <p className="text-text-muted mb-4">
                {!data.outline 
                  ? 'Generate a book outline first, then create detailed chapter content.'
                  : 'Create comprehensive chapter content based on your book outline and research.'
                }
              </p>
              <Button 
                onClick={generateChapters} 
                disabled={!data.outline}
                icon={<BookOpen className="w-4 h-4" />}
              >
                Generate Book Chapters
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}