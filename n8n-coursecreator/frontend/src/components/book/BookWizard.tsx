import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  Circle,
  BookOpen,
  FileText,
  Search,
  List,
  Sparkles,
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { cn } from '@/utils/cn';
import { BookDetailsStep } from './steps/BookDetailsStep';
import { BookResearchStep } from './steps/BookResearchStep';
import { BookOutlineStep } from './steps/BookOutlineStep';
import { BookChaptersStep } from './steps/BookChaptersStep';
import { BookBibliographyStep } from './steps/BookBibliographyStep';
import { BookReviewStep } from './steps/BookReviewStep';

export interface BookData {
  // Basic details
  title: string;
  subtitle: string;
  description: string;
  style: string;
  citation_style: string;
  target_audience: string;
  estimated_pages: number;
  context: string;
  
  // Generation options
  include_annotations: boolean;
  
  // Research data
  research_data?: any;
  
  // Generated content
  outline?: any;
  chapters?: any[];
  bibliography?: any;
}

const steps = [
  {
    id: 'details',
    title: 'Book Details',
    description: 'Basic information about your book',
    icon: BookOpen,
  },
  {
    id: 'research',
    title: 'Research Data',
    description: 'Upload and process research materials',
    icon: Search,
  },
  {
    id: 'outline',
    title: 'Generate Outline',
    description: 'AI-generated book structure',
    icon: FileText,
  },
  {
    id: 'chapters',
    title: 'Generate Chapters',
    description: 'Detailed chapter content',
    icon: FileText,
  },
  {
    id: 'bibliography',
    title: 'Bibliography',
    description: 'Citations and references',
    icon: List,
  },
  {
    id: 'review',
    title: 'Review & Generate',
    description: 'Final review and generation',
    icon: CheckCircle,
  },
];

interface BookWizardProps {
  onComplete: (data: BookData) => void;
  onCancel: () => void;
}

export function BookWizard({ onComplete, onCancel }: BookWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [bookData, setBookData] = useState<BookData>({
    title: '',
    subtitle: '',
    description: '',
    style: 'academic',
    citation_style: 'APA',
    target_audience: 'general readers',
    estimated_pages: 200,
    context: '',
    include_annotations: false,
  });
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

  const updateBookData = (updates: Partial<BookData>) => {
    setBookData(prev => ({ ...prev, ...updates }));
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setCurrentStep(prev => prev + 1);
    } else {
      // Final step - generate book
      onComplete(bookData);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const isStepCompleted = (stepIndex: number) => {
    return completedSteps.has(stepIndex);
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Details
        return bookData.title.trim() !== '';
      case 1: // Research
        return true; // Research is optional
      case 2: // Outline
        return bookData.outline !== undefined;
      case 3: // Chapters
        return bookData.chapters && bookData.chapters.length > 0;
      case 4: // Bibliography
        return bookData.bibliography !== undefined;
      case 5: // Review
        return true;
      default:
        return false;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <BookDetailsStep
            data={bookData}
            onUpdate={updateBookData}
          />
        );
      case 1:
        return (
          <BookResearchStep
            data={bookData}
            onUpdate={updateBookData}
          />
        );
      case 2:
        return (
          <BookOutlineStep
            data={bookData}
            onUpdate={updateBookData}
          />
        );
      case 3:
        return (
          <BookChaptersStep
            data={bookData}
            onUpdate={updateBookData}
          />
        );
      case 4:
        return (
          <BookBibliographyStep
            data={bookData}
            onUpdate={updateBookData}
          />
        );
      case 5:
        return (
          <BookReviewStep
            data={bookData}
            onUpdate={updateBookData}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="inline-flex items-center space-x-2 px-4 py-2 bg-electric-green/10 border border-electric-green/20 rounded-full">
          <Sparkles className="w-4 h-4 text-electric-green" />
          <span className="text-sm font-medium text-electric-green">AI Book Generator</span>
        </div>
        <h1 className="text-3xl font-bold text-text-primary">Generate Your Book</h1>
        <p className="text-text-secondary">Transform research into a comprehensive book with AI</p>
      </div>

      {/* Progress Steps */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = isStepCompleted(index);
            const isUpcoming = index > currentStep;

            return (
              <React.Fragment key={step.id}>
                <div className="flex flex-col items-center space-y-2 flex-1">
                  {/* Step Icon */}
                  <motion.div
                    className={cn(
                      'w-12 h-12 rounded-full border-2 flex items-center justify-center transition-all duration-300',
                      isActive && 'border-electric-green bg-electric-green/10 scale-110',
                      isCompleted && 'border-electric-blue bg-electric-blue/10',
                      isUpcoming && 'border-white/20 bg-dark-400'
                    )}
                    whileHover={{ scale: 1.05 }}
                  >
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6 text-electric-blue" />
                    ) : (
                      <Icon className={cn(
                        'w-6 h-6',
                        isActive && 'text-electric-green',
                        isUpcoming && 'text-text-muted'
                      )} />
                    )}
                  </motion.div>

                  {/* Step Info */}
                  <div className="text-center">
                    <div className={cn(
                      'text-sm font-medium',
                      isActive && 'text-electric-green',
                      isCompleted && 'text-electric-blue',
                      isUpcoming && 'text-text-muted'
                    )}>
                      {step.title}
                    </div>
                    <div className="text-xs text-text-muted hidden sm:block">
                      {step.description}
                    </div>
                  </div>
                </div>

                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="flex-1 h-px bg-white/10 mx-4 relative">
                    <motion.div
                      className="absolute inset-0 bg-electric-green"
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: isCompleted ? 1 : 0 }}
                      transition={{ duration: 0.5 }}
                      style={{ transformOrigin: 'left' }}
                    />
                  </div>
                )}
              </React.Fragment>
            );
          })}
        </div>
      </Card>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {renderStepContent()}
        </motion.div>
      </AnimatePresence>

      {/* Navigation */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={onCancel}
              icon={<ArrowLeft className="w-4 h-4" />}
            >
              Cancel
            </Button>
            {currentStep > 0 && (
              <Button
                variant="outline"
                onClick={handlePrevious}
                icon={<ArrowLeft className="w-4 h-4" />}
              >
                Previous
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2 text-sm text-text-muted">
            <span>Step {currentStep + 1} of {steps.length}</span>
          </div>

          <Button
            onClick={handleNext}
            disabled={!canProceed()}
            icon={currentStep === steps.length - 1 ? <Sparkles className="w-4 h-4" /> : <ArrowRight className="w-4 h-4" />}
          >
            {currentStep === steps.length - 1 ? 'Generate Book' : 'Next'}
          </Button>
        </div>
      </Card>
    </div>
  );
}