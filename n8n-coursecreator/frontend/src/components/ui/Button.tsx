import React from 'react';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { cn } from '@/utils/cn';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
}

const variants = {
  primary: 'bg-electric-blue text-dark-300 hover:bg-electric-blue/90 shadow-lg hover:shadow-electric-blue/25',
  secondary: 'bg-dark-400 text-text-primary border border-electric-blue/30 hover:border-electric-blue hover:bg-dark-400/80',
  ghost: 'text-text-primary hover:bg-white/5',
  outline: 'border border-white/20 text-text-primary hover:border-electric-blue hover:text-electric-blue hover:bg-electric-blue/5',
};

const sizes = {
  sm: 'px-4 py-2 text-sm',
  md: 'px-6 py-3 text-base',
  lg: 'px-8 py-4 text-lg',
};

export function Button({
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  children,
  className,
  disabled,
  ...props
}: ButtonProps) {
  return (
    <motion.button
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={cn(
        'relative inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-electric-blue/20 disabled:opacity-50 disabled:cursor-not-allowed',
        variants[variant],
        sizes[size],
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      )}
      {!loading && icon && (
        <span className="mr-2">{icon}</span>
      )}
      {children}
      
      {/* Glow effect for primary buttons */}
      {variant === 'primary' && (
        <div className="absolute inset-0 rounded-lg bg-electric-blue opacity-0 hover:opacity-20 transition-opacity duration-300 blur-md -z-10" />
      )}
    </motion.button>
  );
}