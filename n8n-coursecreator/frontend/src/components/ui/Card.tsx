import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  glow?: boolean;
  gradient?: boolean;
  onClick?: () => void;
}

export function Card({ 
  children, 
  className, 
  hover = false, 
  glow = false, 
  gradient = false,
  onClick 
}: CardProps) {
  const Component = onClick ? motion.button : motion.div;
  
  return (
    <Component
      onClick={onClick}
      whileHover={hover ? { scale: 1.02, y: -4 } : {}}
      whileTap={onClick ? { scale: 0.98 } : {}}
      className={cn(
        'relative overflow-hidden rounded-xl border transition-all duration-300',
        gradient 
          ? 'bg-gradient-to-br from-dark-500 to-dark-400 border-white/10' 
          : 'bg-dark-500 border-white/10',
        hover && 'hover:border-electric-blue/30 hover:shadow-[0_0_30px_rgba(0,212,255,0.15)]',
        glow && 'shadow-[0_0_30px_rgba(0,212,255,0.1)]',
        onClick && 'cursor-pointer focus:outline-none focus:ring-2 focus:ring-electric-blue/20',
        className
      )}
    >
      {/* Gradient overlay for special cards */}
      {gradient && (
        <div className="absolute inset-0 bg-gradient-to-br from-electric-blue/5 via-transparent to-electric-purple/5 pointer-events-none" />
      )}
      
      {/* Shimmer effect on hover */}
      {hover && (
        <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-500">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000" />
        </div>
      )}
      
      <div className="relative z-10">
        {children}
      </div>
    </Component>
  );
}