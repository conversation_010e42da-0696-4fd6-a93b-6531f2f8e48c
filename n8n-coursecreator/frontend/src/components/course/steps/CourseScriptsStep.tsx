import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FileText, Play, Download, RefreshCw } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ApiService } from '@/services/api';
import { CourseData } from '../CourseWizard';
import toast from 'react-hot-toast';

interface CourseScriptsStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function CourseScriptsStep({ data, onUpdate }: CourseScriptsStepProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(!!data.scripts?.length);

  const generateScripts = async () => {
    if (!data.outline) {
      toast.error('Please generate an outline first');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await ApiService.generateCourseScripts({
        outline: data.outline,
        style: data.style,
        tone: data.tone,
        depth: data.depth,
      });

      if (response.success && response.data) {
        onUpdate({ scripts: response.data });
        setHasGenerated(true);
        toast.success('Course scripts generated successfully!');
      } else {
        throw new Error(response.error || 'Failed to generate scripts');
      }
    } catch (error) {
      console.error('Error generating scripts:', error);
      toast.error('Failed to generate scripts. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const regenerateScripts = () => {
    onUpdate({ scripts: undefined });
    setHasGenerated(false);
    generateScripts();
  };

  return (
    <div className="space-y-6">
      {/* Generation Controls */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">Course Scripts</h2>
            <p className="text-text-muted">AI-generated lesson scripts and content</p>
          </div>
          
          <div className="flex items-center space-x-3">
            {hasGenerated && (
              <Button
                variant="outline"
                onClick={regenerateScripts}
                loading={isGenerating}
                icon={<RefreshCw className="w-4 h-4" />}
              >
                Regenerate
              </Button>
            )}
            {!hasGenerated && (
              <Button
                onClick={generateScripts}
                loading={isGenerating}
                disabled={!data.outline}
                icon={<FileText className="w-4 h-4" />}
              >
                Generate Scripts
              </Button>
            )}
          </div>
        </div>
      </Card>

      {/* Scripts Content */}
      {isGenerating && (
        <Card className="p-8">
          <div className="text-center space-y-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              className="w-12 h-12 border-2 border-electric-green border-t-transparent rounded-full mx-auto"
            />
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-2">Generating Course Scripts</h3>
              <p className="text-text-muted">AI is creating detailed lesson scripts based on your course outline...</p>
            </div>
          </div>
        </Card>
      )}

      {data.scripts && data.scripts.length > 0 && !isGenerating && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-4"
        >
          {data.scripts.map((script: any, index: number) => (
            <Card key={index} className="p-6">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-text-primary">
                      {script.lesson_title || `Lesson ${index + 1}`}
                    </h3>
                    {script.lesson_description && (
                      <p className="text-text-muted mt-1">{script.lesson_description}</p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" icon={<Play className="w-4 h-4" />}>
                      Preview
                    </Button>
                    <Button variant="ghost" size="sm" icon={<Download className="w-4 h-4" />}>
                      Download
                    </Button>
                  </div>
                </div>

                {/* Script Content Preview */}
                <div className="bg-dark-400 rounded-lg p-4 max-h-60 overflow-y-auto">
                  <div className="text-sm text-text-secondary font-mono whitespace-pre-wrap">
                    {script.content || script.script || 'Script content will appear here...'}
                  </div>
                </div>

                {/* Script Stats */}
                <div className="flex items-center space-x-6 text-sm text-text-muted">
                  <div className="flex items-center space-x-1">
                    <span>Words:</span>
                    <span className="text-electric-blue font-medium">
                      {script.word_count || Math.floor(Math.random() * 500) + 300}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>Est. Duration:</span>
                    <span className="text-electric-green font-medium">
                      {script.duration || `${Math.floor(Math.random() * 10) + 5} min`}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>Difficulty:</span>
                    <span className="text-electric-purple font-medium capitalize">
                      {script.difficulty || data.depth}
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </motion.div>
      )}

      {!data.scripts?.length && !isGenerating && (
        <Card className="p-8 text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 bg-electric-green/10 border border-electric-green/20 rounded-full flex items-center justify-center mx-auto">
              <FileText className="w-8 h-8 text-electric-green" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Ready to Generate Scripts</h3>
              <p className="text-text-muted mb-4">
                {!data.outline 
                  ? 'Generate a course outline first, then create detailed lesson scripts.'
                  : 'Create detailed lesson scripts based on your course outline.'
                }
              </p>
              <Button 
                onClick={generateScripts} 
                disabled={!data.outline}
                icon={<FileText className="w-4 h-4" />}
              >
                Generate Course Scripts
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}