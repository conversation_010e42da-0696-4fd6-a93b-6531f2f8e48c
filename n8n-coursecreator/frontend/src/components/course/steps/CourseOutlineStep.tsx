import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { RefreshCw, CheckCircle, FileText, Clock, Users } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ApiService } from '@/services/api';
import { CourseData } from '../CourseWizard';
import toast from 'react-hot-toast';

interface CourseOutlineStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function CourseOutlineStep({ data, onUpdate }: CourseOutlineStepProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(!!data.outline);

  const generateOutline = async () => {
    if (!data.title.trim()) {
      toast.error('Please provide a course title first');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await ApiService.createCourseOutline({
        course_title: data.title,
        style: data.style,
        tone: data.tone,
        depth: data.depth,
        target_audience: data.target_audience,
        duration: data.duration,
      });

      if (response.success && response.data) {
        onUpdate({ outline: response.data });
        setHasGenerated(true);
        toast.success('Course outline generated successfully!');
      } else {
        throw new Error(response.error || 'Failed to generate outline');
      }
    } catch (error) {
      console.error('Error generating outline:', error);
      toast.error('Failed to generate outline. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const regenerateOutline = () => {
    onUpdate({ outline: undefined });
    setHasGenerated(false);
    generateOutline();
  };

  return (
    <div className="space-y-6">
      {/* Generation Controls */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">Course Outline</h2>
            <p className="text-text-muted">AI-generated course structure and modules</p>
          </div>
          
          <div className="flex items-center space-x-3">
            {hasGenerated && (
              <Button
                variant="outline"
                onClick={regenerateOutline}
                loading={isGenerating}
                icon={<RefreshCw className="w-4 h-4" />}
              >
                Regenerate
              </Button>
            )}
            {!hasGenerated && (
              <Button
                onClick={generateOutline}
                loading={isGenerating}
                icon={<FileText className="w-4 h-4" />}
              >
                Generate Outline
              </Button>
            )}
          </div>
        </div>

        {/* Course Summary */}
        <div className="grid md:grid-cols-4 gap-4 mb-6">
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <FileText className="w-4 h-4 text-electric-blue" />
              <span className="text-sm font-medium text-text-primary">Style</span>
            </div>
            <span className="text-text-muted capitalize">{data.style}</span>
          </div>
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="w-4 h-4 text-electric-green" />
              <span className="text-sm font-medium text-text-primary">Duration</span>
            </div>
            <span className="text-text-muted">{data.duration}</span>
          </div>
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="w-4 h-4 text-electric-purple" />
              <span className="text-sm font-medium text-text-primary">Audience</span>
            </div>
            <span className="text-text-muted">{data.target_audience}</span>
          </div>
          <div className="p-4 bg-dark-400 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="w-4 h-4 text-electric-coral" />
              <span className="text-sm font-medium text-text-primary">Depth</span>
            </div>
            <span className="text-text-muted capitalize">{data.depth}</span>
          </div>
        </div>
      </Card>

      {/* Outline Content */}
      {isGenerating && (
        <Card className="p-8">
          <div className="text-center space-y-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              className="w-12 h-12 border-2 border-electric-blue border-t-transparent rounded-full mx-auto"
            />
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-2">Generating Course Outline</h3>
              <p className="text-text-muted">AI is analyzing your requirements and creating a comprehensive course structure...</p>
            </div>
          </div>
        </Card>
      )}

      {data.outline && !isGenerating && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="p-6">
            <div className="space-y-6">
              {/* Course Header */}
              <div className="border-b border-white/10 pb-6">
                <h3 className="text-2xl font-bold text-text-primary mb-2">
                  {data.outline.title || data.title}
                </h3>
                {data.outline.description && (
                  <p className="text-text-secondary leading-relaxed">
                    {data.outline.description}
                  </p>
                )}
              </div>

              {/* Learning Objectives */}
              {data.outline.learning_objectives && data.outline.learning_objectives.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-text-primary mb-3">Learning Objectives</h4>
                  <div className="grid md:grid-cols-2 gap-3">
                    {data.outline.learning_objectives.map((objective: string, index: number) => (
                      <div key={index} className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-electric-green flex-shrink-0 mt-0.5" />
                        <span className="text-text-secondary">{objective}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Course Modules */}
              {data.outline.modules && data.outline.modules.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-text-primary mb-4">Course Modules</h4>
                  <div className="space-y-4">
                    {data.outline.modules.map((module: any, index: number) => (
                      <Card key={index} className="p-4 bg-dark-400">
                        <div className="space-y-3">
                          <div className="flex items-start justify-between">
                            <div>
                              <h5 className="font-semibold text-text-primary">
                                Module {index + 1}: {module.title}
                              </h5>
                              {module.description && (
                                <p className="text-sm text-text-muted mt-1">
                                  {module.description}
                                </p>
                              )}
                            </div>
                            {module.duration && (
                              <div className="flex items-center space-x-1 text-sm text-electric-blue">
                                <Clock className="w-4 h-4" />
                                <span>{module.duration}</span>
                              </div>
                            )}
                          </div>

                          {/* Module Lessons */}
                          {module.lessons && module.lessons.length > 0 && (
                            <div className="space-y-2">
                              {module.lessons.map((lesson: any, lessonIndex: number) => (
                                <div
                                  key={lessonIndex}
                                  className="flex items-center space-x-3 p-3 bg-dark-500 rounded-lg"
                                >
                                  <div className="w-6 h-6 bg-electric-blue/10 border border-electric-blue/20 rounded-full flex items-center justify-center">
                                    <span className="text-xs font-medium text-electric-blue">
                                      {lessonIndex + 1}
                                    </span>
                                  </div>
                                  <div className="flex-1">
                                    <div className="font-medium text-text-primary text-sm">
                                      {lesson.title}
                                    </div>
                                    {lesson.description && (
                                      <div className="text-xs text-text-muted">
                                        {lesson.description}
                                      </div>
                                    )}
                                  </div>
                                  {lesson.duration && (
                                    <div className="text-xs text-text-muted">
                                      {lesson.duration}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </Card>
        </motion.div>
      )}

      {!data.outline && !isGenerating && (
        <Card className="p-8 text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 bg-electric-blue/10 border border-electric-blue/20 rounded-full flex items-center justify-center mx-auto">
              <FileText className="w-8 h-8 text-electric-blue" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Ready to Generate Outline</h3>
              <p className="text-text-muted mb-4">
                Click the "Generate Outline" button to create an AI-powered course structure based on your preferences.
              </p>
              <Button onClick={generateOutline} icon={<FileText className="w-4 h-4" />}>
                Generate Course Outline
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}