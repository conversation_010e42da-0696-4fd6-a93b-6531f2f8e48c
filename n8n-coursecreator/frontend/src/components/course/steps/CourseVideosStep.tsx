import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Video, Play, Download, Settings, RefreshCw, User, Volume2 } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ApiService } from '@/services/api';
import { CourseData } from '../CourseWizard';
import toast from 'react-hot-toast';

interface CourseVideosStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

const avatarOptions = [
  { id: 'default', name: 'Default Avatar', description: 'Professional presenter' },
  { id: 'casual', name: 'Casual Avatar', description: 'Friendly and approachable' },
  { id: 'academic', name: 'Academic Avatar', description: 'Scholarly presentation' },
  { id: 'tech', name: 'Tech Avatar', description: 'Technical expertise' },
];

const voiceOptions = [
  { id: 'default', name: 'Default Voice', description: 'Clear and professional' },
  { id: 'warm', name: 'Warm Voice', description: 'Friendly and engaging' },
  { id: 'authoritative', name: 'Authoritative Voice', description: 'Confident and expert' },
  { id: 'casual', name: 'Casual Voice', description: 'Relaxed and conversational' },
];

const qualityOptions = [
  { value: '480p', label: '480p', description: 'Standard quality, smaller file size' },
  { value: '720p', label: '720p HD', description: 'High definition, recommended' },
  { value: '1080p', label: '1080p Full HD', description: 'Full HD, larger file size' },
];

export function CourseVideosStep({ data, onUpdate }: CourseVideosStepProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(!!data.videos?.length);
  const [showSettings, setShowSettings] = useState(false);

  const generateVideos = async () => {
    if (!data.scripts?.length) {
      toast.error('Please generate course scripts first');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await ApiService.createCourseVideos({
        scripts: data.scripts,
        avatar_id: data.avatar_id,
        voice_id: data.voice_id,
        video_quality: data.video_quality,
      });

      if (response.success && response.data) {
        onUpdate({ videos: response.data });
        setHasGenerated(true);
        toast.success('Course videos generated successfully!');
      } else {
        throw new Error(response.error || 'Failed to generate videos');
      }
    } catch (error) {
      console.error('Error generating videos:', error);
      toast.error('Failed to generate videos. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const regenerateVideos = () => {
    onUpdate({ videos: undefined });
    setHasGenerated(false);
    generateVideos();
  };

  return (
    <div className="space-y-6">
      {/* Generation Controls */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">Course Videos</h2>
            <p className="text-text-muted">AI-generated video content with avatars and voiceovers</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              onClick={() => setShowSettings(!showSettings)}
              icon={<Settings className="w-4 h-4" />}
            >
              Settings
            </Button>
            {hasGenerated && (
              <Button
                variant="outline"
                onClick={regenerateVideos}
                loading={isGenerating}
                icon={<RefreshCw className="w-4 h-4" />}
              >
                Regenerate
              </Button>
            )}
            {!hasGenerated && (
              <Button
                onClick={generateVideos}
                loading={isGenerating}
                disabled={!data.scripts?.length}
                icon={<Video className="w-4 h-4" />}
              >
                Generate Videos
              </Button>
            )}
          </div>
        </div>
      </Card>

      {/* Video Settings */}
      {showSettings && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <Card className="p-6 space-y-6">
            <h3 className="text-lg font-semibold text-text-primary">Video Generation Settings</h3>
            
            {/* Avatar Selection */}
            <div>
              <h4 className="text-md font-medium text-text-primary mb-3">Avatar Selection</h4>
              <div className="grid md:grid-cols-2 gap-3">
                {avatarOptions.map((avatar) => (
                  <motion.div
                    key={avatar.id}
                    whileHover={{ scale: 1.01 }}
                    className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                      data.avatar_id === avatar.id
                        ? 'border-electric-purple bg-electric-purple/5'
                        : 'border-white/10 hover:border-electric-purple/30'
                    }`}
                    onClick={() => onUpdate({ avatar_id: avatar.id })}
                  >
                    <div className="flex items-center space-x-3">
                      <User className="w-5 h-5 text-electric-purple" />
                      <div>
                        <div className="font-medium text-text-primary">{avatar.name}</div>
                        <div className="text-sm text-text-muted">{avatar.description}</div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Voice Selection */}
            <div>
              <h4 className="text-md font-medium text-text-primary mb-3">Voice Selection</h4>
              <div className="grid md:grid-cols-2 gap-3">
                {voiceOptions.map((voice) => (
                  <motion.div
                    key={voice.id}
                    whileHover={{ scale: 1.01 }}
                    className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                      data.voice_id === voice.id
                        ? 'border-electric-coral bg-electric-coral/5'
                        : 'border-white/10 hover:border-electric-coral/30'
                    }`}
                    onClick={() => onUpdate({ voice_id: voice.id })}
                  >
                    <div className="flex items-center space-x-3">
                      <Volume2 className="w-5 h-5 text-electric-coral" />
                      <div>
                        <div className="font-medium text-text-primary">{voice.name}</div>
                        <div className="text-sm text-text-muted">{voice.description}</div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Quality Selection */}
            <div>
              <h4 className="text-md font-medium text-text-primary mb-3">Video Quality</h4>
              <div className="grid md:grid-cols-3 gap-3">
                {qualityOptions.map((quality) => (
                  <motion.div
                    key={quality.value}
                    whileHover={{ scale: 1.01 }}
                    className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                      data.video_quality === quality.value
                        ? 'border-electric-blue bg-electric-blue/5'
                        : 'border-white/10 hover:border-electric-blue/30'
                    }`}
                    onClick={() => onUpdate({ video_quality: quality.value })}
                  >
                    <div className="text-center">
                      <div className="font-medium text-text-primary">{quality.label}</div>
                      <div className="text-sm text-text-muted">{quality.description}</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </Card>
        </motion.div>
      )}

      {/* Video Generation Progress */}
      {isGenerating && (
        <Card className="p-8">
          <div className="text-center space-y-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              className="w-12 h-12 border-2 border-electric-purple border-t-transparent rounded-full mx-auto"
            />
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-2">Generating Course Videos</h3>
              <p className="text-text-muted">AI is creating videos with avatars and voiceovers. This may take several minutes...</p>
            </div>
          </div>
        </Card>
      )}

      {/* Generated Videos */}
      {data.videos && data.videos.length > 0 && !isGenerating && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-4"
        >
          {data.videos.map((video: any, index: number) => (
            <Card key={index} className="p-6">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-text-primary">
                      {video.title || `Video ${index + 1}`}
                    </h3>
                    {video.description && (
                      <p className="text-text-muted mt-1">{video.description}</p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" icon={<Play className="w-4 h-4" />}>
                      Preview
                    </Button>
                    <Button variant="ghost" size="sm" icon={<Download className="w-4 h-4" />}>
                      Download
                    </Button>
                  </div>
                </div>

                {/* Video Preview */}
                <div className="aspect-video bg-dark-400 rounded-lg flex items-center justify-center border border-white/10">
                  <div className="text-center space-y-2">
                    <Video className="w-12 h-12 text-electric-purple mx-auto" />
                    <p className="text-text-muted">Video preview will appear here</p>
                    <Button variant="outline" size="sm" icon={<Play className="w-4 h-4" />}>
                      Play Video
                    </Button>
                  </div>
                </div>

                {/* Video Stats */}
                <div className="flex items-center space-x-6 text-sm text-text-muted">
                  <div className="flex items-center space-x-1">
                    <span>Duration:</span>
                    <span className="text-electric-purple font-medium">
                      {video.duration || `${Math.floor(Math.random() * 10) + 5}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>Quality:</span>
                    <span className="text-electric-blue font-medium">{data.video_quality}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>Avatar:</span>
                    <span className="text-electric-coral font-medium capitalize">{data.avatar_id}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>Voice:</span>
                    <span className="text-electric-green font-medium capitalize">{data.voice_id}</span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </motion.div>
      )}

      {!data.videos?.length && !isGenerating && (
        <Card className="p-8 text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 bg-electric-purple/10 border border-electric-purple/20 rounded-full flex items-center justify-center mx-auto">
              <Video className="w-8 h-8 text-electric-purple" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Ready to Generate Videos</h3>
              <p className="text-text-muted mb-4">
                {!data.scripts?.length 
                  ? 'Generate course scripts first, then create AI-powered videos.'
                  : 'Create engaging videos with AI avatars and voiceovers based on your scripts.'
                }
              </p>
              <Button 
                onClick={generateVideos} 
                disabled={!data.scripts?.length}
                icon={<Video className="w-4 h-4" />}
              >
                Generate Course Videos
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}