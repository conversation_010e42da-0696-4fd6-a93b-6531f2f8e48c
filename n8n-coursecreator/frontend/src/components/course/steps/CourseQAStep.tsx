import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { HelpCircle, Plus, RefreshCw, Volume2, Video, CheckCircle } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ApiService } from '@/services/api';
import { CourseData } from '../CourseWizard';
import toast from 'react-hot-toast';

interface CourseQAStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function CourseQAStep({ data, onUpdate }: CourseQAStepProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(!!data.qa?.length);

  const generateQA = async () => {
    if (!data.outline) {
      toast.error('Please generate a course outline first');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await ApiService.generateCourseQA({
        outline: data.outline,
        qa_count: data.qa_count,
        include_audio: data.include_audio,
        include_video: data.include_video,
      });

      if (response.success && response.data) {
        onUpdate({ qa: response.data });
        setHasGenerated(true);
        toast.success('Q&A content generated successfully!');
      } else {
        throw new Error(response.error || 'Failed to generate Q&A');
      }
    } catch (error) {
      console.error('Error generating Q&A:', error);
      toast.error('Failed to generate Q&A. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const regenerateQA = () => {
    onUpdate({ qa: undefined });
    setHasGenerated(false);
    generateQA();
  };

  return (
    <div className="space-y-6">
      {/* Generation Controls */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">Q&A Generation</h2>
            <p className="text-text-muted">Create quiz questions and assessments for your course</p>
          </div>
          
          <div className="flex items-center space-x-3">
            {hasGenerated && (
              <Button
                variant="outline"
                onClick={regenerateQA}
                loading={isGenerating}
                icon={<RefreshCw className="w-4 h-4" />}
              >
                Regenerate
              </Button>
            )}
            {!hasGenerated && (
              <Button
                onClick={generateQA}
                loading={isGenerating}
                disabled={!data.outline}
                icon={<HelpCircle className="w-4 h-4" />}
              >
                Generate Q&A
              </Button>
            )}
          </div>
        </div>

        {/* Q&A Settings */}
        <div className="grid md:grid-cols-3 gap-4 mb-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Questions per Module
            </label>
            <input
              type="number"
              min="1"
              max="20"
              value={data.qa_count}
              onChange={(e) => onUpdate({ qa_count: parseInt(e.target.value) || 5 })}
              className="input"
            />
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Include Audio
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={data.include_audio}
                onChange={(e) => onUpdate({ include_audio: e.target.checked })}
                className="w-4 h-4 text-electric-blue bg-dark-400 border-white/20 rounded focus:ring-electric-blue"
              />
              <span className="text-text-secondary">Audio questions</span>
            </label>
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Include Video
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={data.include_video}
                onChange={(e) => onUpdate({ include_video: e.target.checked })}
                className="w-4 h-4 text-electric-blue bg-dark-400 border-white/20 rounded focus:ring-electric-blue"
              />
              <span className="text-text-secondary">Video questions</span>
            </label>
          </div>
        </div>
      </Card>

      {/* Q&A Generation Progress */}
      {isGenerating && (
        <Card className="p-8">
          <div className="text-center space-y-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              className="w-12 h-12 border-2 border-electric-coral border-t-transparent rounded-full mx-auto"
            />
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-2">Generating Q&A Content</h3>
              <p className="text-text-muted">AI is creating quiz questions and assessments based on your course content...</p>
            </div>
          </div>
        </Card>
      )}

      {/* Generated Q&A */}
      {data.qa && data.qa.length > 0 && !isGenerating && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-6"
        >
          {/* Q&A Summary */}
          <Card className="p-6">
            <div className="grid md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-blue mb-1">{data.qa.length}</div>
                <div className="text-sm text-text-muted">Total Questions</div>
              </div>
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-green mb-1">
                  {data.qa.filter((q: any) => q.type === 'multiple_choice').length}
                </div>
                <div className="text-sm text-text-muted">Multiple Choice</div>
              </div>
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-purple mb-1">
                  {data.qa.filter((q: any) => q.type === 'true_false').length}
                </div>
                <div className="text-sm text-text-muted">True/False</div>
              </div>
              <div className="text-center p-4 bg-dark-400 rounded-lg">
                <div className="text-2xl font-bold text-electric-coral mb-1">
                  {data.qa.filter((q: any) => q.type === 'open_ended').length}
                </div>
                <div className="text-sm text-text-muted">Open Ended</div>
              </div>
            </div>
          </Card>

          {/* Q&A Items */}
          <div className="space-y-4">
            {data.qa.map((qaItem: any, index: number) => (
              <Card key={index} className="p-6">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="px-2 py-1 bg-electric-blue/10 text-electric-blue text-xs font-medium rounded-full">
                          {qaItem.type?.replace('_', ' ').toUpperCase() || 'MULTIPLE CHOICE'}
                        </span>
                        <span className="text-sm text-text-muted">
                          Question {index + 1}
                        </span>
                      </div>
                      <h4 className="text-lg font-medium text-text-primary">
                        {qaItem.question || `Sample question ${index + 1} about the course content`}
                      </h4>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {qaItem.has_audio && (
                        <div className="p-2 bg-electric-green/10 border border-electric-green/20 rounded-lg">
                          <Volume2 className="w-4 h-4 text-electric-green" />
                        </div>
                      )}
                      {qaItem.has_video && (
                        <div className="p-2 bg-electric-purple/10 border border-electric-purple/20 rounded-lg">
                          <Video className="w-4 h-4 text-electric-purple" />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Options for multiple choice */}
                  {(qaItem.type === 'multiple_choice' || !qaItem.type) && qaItem.options && (
                    <div className="space-y-2">
                      {qaItem.options.map((option: any, optionIndex: number) => (
                        <div
                          key={optionIndex}
                          className={`p-3 border rounded-lg ${
                            option.is_correct
                              ? 'border-electric-green bg-electric-green/5'
                              : 'border-white/10 bg-dark-400'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                              option.is_correct
                                ? 'border-electric-green bg-electric-green/10'
                                : 'border-white/20'
                            }`}>
                              {option.is_correct && (
                                <CheckCircle className="w-4 h-4 text-electric-green" />
                              )}
                              {!option.is_correct && (
                                <span className="text-xs font-medium text-text-muted">
                                  {String.fromCharCode(65 + optionIndex)}
                                </span>
                              )}
                            </div>
                            <span className="text-text-secondary">{option.text}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Answer for true/false or open ended */}
                  {qaItem.type !== 'multiple_choice' && qaItem.answer && (
                    <div className="p-3 bg-electric-green/5 border border-electric-green/20 rounded-lg">
                      <div className="text-sm font-medium text-electric-green mb-1">Correct Answer:</div>
                      <div className="text-text-secondary">{qaItem.answer}</div>
                    </div>
                  )}

                  {/* Explanation */}
                  {qaItem.explanation && (
                    <div className="p-3 bg-dark-400 rounded-lg">
                      <div className="text-sm font-medium text-text-primary mb-1">Explanation:</div>
                      <div className="text-text-secondary">{qaItem.explanation}</div>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </motion.div>
      )}

      {!data.qa?.length && !isGenerating && (
        <Card className="p-8 text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 bg-electric-coral/10 border border-electric-coral/20 rounded-full flex items-center justify-center mx-auto">
              <HelpCircle className="w-8 h-8 text-electric-coral" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Ready to Generate Q&A</h3>
              <p className="text-text-muted mb-4">
                {!data.outline 
                  ? 'Generate a course outline first, then create quiz questions and assessments.'
                  : 'Create engaging quiz questions and assessments to test student knowledge.'
                }
              </p>
              <Button 
                onClick={generateQA} 
                disabled={!data.outline}
                icon={<HelpCircle className="w-4 h-4" />}
              >
                Generate Q&A Content
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}