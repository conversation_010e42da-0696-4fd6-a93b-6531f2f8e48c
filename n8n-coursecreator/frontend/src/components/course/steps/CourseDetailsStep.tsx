import React from 'react';
import { motion } from 'framer-motion';
import { Info } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { CourseData } from '../CourseWizard';

interface CourseDetailsStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

const styleOptions = [
  { value: 'professional', label: 'Professional', description: 'Business-focused, formal tone' },
  { value: 'academic', label: 'Academic', description: 'Educational, research-based' },
  { value: 'casual', label: 'Casual', description: 'Relaxed, conversational style' },
  { value: 'technical', label: 'Technical', description: 'In-depth, technical content' },
];

const toneOptions = [
  { value: 'engaging', label: 'Engaging', description: 'Interactive and motivating' },
  { value: 'authoritative', label: 'Authoritative', description: 'Expert and confident' },
  { value: 'friendly', label: 'Friendly', description: 'Warm and approachable' },
  { value: 'inspirational', label: 'Inspirational', description: 'Motivating and uplifting' },
];

const depthOptions = [
  { value: 'beginner', label: 'Beginner', description: 'Basic concepts and fundamentals' },
  { value: 'intermediate', label: 'Intermediate', description: 'Moderate complexity and detail' },
  { value: 'advanced', label: 'Advanced', description: 'Complex topics and deep insights' },
  { value: 'expert', label: 'Expert', description: 'Highly specialized content' },
];

const durationOptions = [
  { value: '1-2 hours', label: '1-2 hours', description: 'Quick overview course' },
  { value: '2-4 hours', label: '2-4 hours', description: 'Comprehensive introduction' },
  { value: '4-6 hours', label: '4-6 hours', description: 'Detailed exploration' },
  { value: '6-8 hours', label: '6-8 hours', description: 'In-depth mastery' },
  { value: '8+ hours', label: '8+ hours', description: 'Complete specialization' },
];

export function CourseDetailsStep({ data, onUpdate }: CourseDetailsStepProps) {
  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold text-text-primary mb-4">Course Information</h2>
            
            <div className="grid gap-6">
              {/* Course Title */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Course Title *
                </label>
                <input
                  type="text"
                  value={data.title}
                  onChange={(e) => onUpdate({ title: e.target.value })}
                  placeholder="Enter your course title..."
                  className="input"
                />
              </div>

              {/* Course Description */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Description
                </label>
                <textarea
                  value={data.description}
                  onChange={(e) => onUpdate({ description: e.target.value })}
                  placeholder="Describe what your course will cover..."
                  className="textarea"
                  rows={4}
                />
              </div>

              {/* Target Audience */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Target Audience
                </label>
                <input
                  type="text"
                  value={data.target_audience}
                  onChange={(e) => onUpdate({ target_audience: e.target.value })}
                  placeholder="e.g., beginners, professionals, students..."
                  className="input"
                />
              </div>

              {/* Additional Context */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Additional Context
                </label>
                <textarea
                  value={data.context}
                  onChange={(e) => onUpdate({ context: e.target.value })}
                  placeholder="Any specific requirements, objectives, or additional information..."
                  className="textarea"
                  rows={3}
                />
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Style Options */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Course Style</h3>
        <div className="grid md:grid-cols-2 gap-4">
          {styleOptions.map((option) => (
            <motion.div
              key={option.value}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                data.style === option.value
                  ? 'border-electric-blue bg-electric-blue/5'
                  : 'border-white/10 hover:border-electric-blue/30'
              }`}
              onClick={() => onUpdate({ style: option.value })}
            >
              <div className="font-medium text-text-primary">{option.label}</div>
              <div className="text-sm text-text-muted">{option.description}</div>
            </motion.div>
          ))}
        </div>
      </Card>

      {/* Tone Options */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Course Tone</h3>
        <div className="grid md:grid-cols-2 gap-4">
          {toneOptions.map((option) => (
            <motion.div
              key={option.value}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                data.tone === option.value
                  ? 'border-electric-green bg-electric-green/5'
                  : 'border-white/10 hover:border-electric-green/30'
              }`}
              onClick={() => onUpdate({ tone: option.value })}
            >
              <div className="font-medium text-text-primary">{option.label}</div>
              <div className="text-sm text-text-muted">{option.description}</div>
            </motion.div>
          ))}
        </div>
      </Card>

      {/* Depth & Duration */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Course Depth</h3>
          <div className="space-y-3">
            {depthOptions.map((option) => (
              <motion.div
                key={option.value}
                whileHover={{ scale: 1.01 }}
                className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                  data.depth === option.value
                    ? 'border-electric-purple bg-electric-purple/5'
                    : 'border-white/10 hover:border-electric-purple/30'
                }`}
                onClick={() => onUpdate({ depth: option.value })}
              >
                <div className="font-medium text-text-primary">{option.label}</div>
                <div className="text-sm text-text-muted">{option.description}</div>
              </motion.div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Estimated Duration</h3>
          <div className="space-y-3">
            {durationOptions.map((option) => (
              <motion.div
                key={option.value}
                whileHover={{ scale: 1.01 }}
                className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                  data.duration === option.value
                    ? 'border-electric-coral bg-electric-coral/5'
                    : 'border-white/10 hover:border-electric-coral/30'
                }`}
                onClick={() => onUpdate({ duration: option.value })}
              >
                <div className="font-medium text-text-primary">{option.label}</div>
                <div className="text-sm text-text-muted">{option.description}</div>
              </motion.div>
            ))}
          </div>
        </Card>
      </div>

      {/* Info Card */}
      <Card className="p-4 bg-electric-blue/5 border-electric-blue/20">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-electric-blue flex-shrink-0 mt-0.5" />
          <div className="text-sm text-text-secondary">
            <p className="font-medium text-electric-blue mb-1">AI Course Generation</p>
            <p>
              Based on your inputs, our AI will generate a comprehensive course outline, 
              create lesson scripts, produce videos, and generate Q&A content. The more 
              detailed your information, the better the results will be.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
}