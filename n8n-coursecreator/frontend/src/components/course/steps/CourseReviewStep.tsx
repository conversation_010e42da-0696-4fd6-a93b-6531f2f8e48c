import React from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  FileText, 
  Video, 
  HelpCircle, 
  Download, 
  Share,
  Calendar,
  Clock,
  Users,
  GraduationCap
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { CourseData } from '../CourseWizard';

interface CourseReviewStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function CourseReviewStep({ data }: CourseReviewStepProps) {
  const completionStats = {
    outline: !!data.outline,
    scripts: !!(data.scripts && data.scripts.length > 0),
    videos: !!(data.videos && data.videos.length > 0),
    qa: !!(data.qa && data.qa.length > 0),
  };

  const completedCount = Object.values(completionStats).filter(Boolean).length;
  const totalSteps = Object.keys(completionStats).length;
  const completionPercentage = Math.round((completedCount / totalSteps) * 100);

  const courseStats = {
    modules: data.outline?.modules?.length || 0,
    lessons: data.outline?.modules?.reduce((total: number, module: any) => 
      total + (module.lessons?.length || 0), 0) || 0,
    scripts: data.scripts?.length || 0,
    videos: data.videos?.length || 0,
    questions: data.qa?.length || 0,
  };

  return (
    <div className="space-y-6">
      {/* Course Overview */}
      <Card className="p-6">
        <div className="text-center space-y-4 mb-6">
          <div className="w-16 h-16 bg-electric-blue/10 border border-electric-blue/20 rounded-full flex items-center justify-center mx-auto">
            <GraduationCap className="w-8 h-8 text-electric-blue" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-text-primary mb-2">{data.title}</h2>
            {data.description && (
              <p className="text-text-secondary max-w-2xl mx-auto">{data.description}</p>
            )}
          </div>
        </div>

        {/* Completion Progress */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-lg font-semibold text-text-primary">Course Completion</span>
            <span className="text-2xl font-bold text-electric-blue">{completionPercentage}%</span>
          </div>
          
          <div className="progress-bar">
            <motion.div
              className="progress-fill"
              initial={{ width: 0 }}
              animate={{ width: `${completionPercentage}%` }}
              transition={{ duration: 1, ease: 'easeOut' }}
            />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className={`w-5 h-5 ${completionStats.outline ? 'text-electric-green' : 'text-text-muted'}`} />
              <span className="text-sm text-text-secondary">Outline</span>
            </div>
            <div className="flex items-center space-x-2">
              <FileText className={`w-5 h-5 ${completionStats.scripts ? 'text-electric-green' : 'text-text-muted'}`} />
              <span className="text-sm text-text-secondary">Scripts</span>
            </div>
            <div className="flex items-center space-x-2">
              <Video className={`w-5 h-5 ${completionStats.videos ? 'text-electric-green' : 'text-text-muted'}`} />
              <span className="text-sm text-text-secondary">Videos</span>
            </div>
            <div className="flex items-center space-x-2">
              <HelpCircle className={`w-5 h-5 ${completionStats.qa ? 'text-electric-green' : 'text-text-muted'}`} />
              <span className="text-sm text-text-secondary">Q&A</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Course Statistics */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-text-primary mb-4">Course Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center p-4 bg-dark-400 rounded-lg">
            <div className="text-2xl font-bold text-electric-blue mb-1">{courseStats.modules}</div>
            <div className="text-sm text-text-muted">Modules</div>
          </div>
          <div className="text-center p-4 bg-dark-400 rounded-lg">
            <div className="text-2xl font-bold text-electric-green mb-1">{courseStats.lessons}</div>
            <div className="text-sm text-text-muted">Lessons</div>
          </div>
          <div className="text-center p-4 bg-dark-400 rounded-lg">
            <div className="text-2xl font-bold text-electric-purple mb-1">{courseStats.scripts}</div>
            <div className="text-sm text-text-muted">Scripts</div>
          </div>
          <div className="text-center p-4 bg-dark-400 rounded-lg">
            <div className="text-2xl font-bold text-electric-coral mb-1">{courseStats.videos}</div>
            <div className="text-sm text-text-muted">Videos</div>
          </div>
          <div className="text-center p-4 bg-dark-400 rounded-lg">
            <div className="text-2xl font-bold text-electric-pink mb-1">{courseStats.questions}</div>
            <div className="text-sm text-text-muted">Questions</div>
          </div>
        </div>
      </Card>

      {/* Course Details */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Course Information</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Users className="w-5 h-5 text-electric-blue" />
              <div>
                <div className="text-sm text-text-muted">Target Audience</div>
                <div className="text-text-primary">{data.target_audience}</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Clock className="w-5 h-5 text-electric-green" />
              <div>
                <div className="text-sm text-text-muted">Duration</div>
                <div className="text-text-primary">{data.duration}</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <GraduationCap className="w-5 h-5 text-electric-purple" />
              <div>
                <div className="text-sm text-text-muted">Depth Level</div>
                <div className="text-text-primary capitalize">{data.depth}</div>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Style & Tone</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <FileText className="w-5 h-5 text-electric-coral" />
              <div>
                <div className="text-sm text-text-muted">Course Style</div>
                <div className="text-text-primary capitalize">{data.style}</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-5 h-5 text-electric-pink" />
              <div>
                <div className="text-sm text-text-muted">Course Tone</div>
                <div className="text-text-primary capitalize">{data.tone}</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Calendar className="w-5 h-5 text-electric-blue" />
              <div>
                <div className="text-sm text-text-muted">Created</div>
                <div className="text-text-primary">{new Date().toLocaleDateString()}</div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Course Content Preview */}
      {data.outline && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Course Content</h3>
          <div className="space-y-3 max-h-60 overflow-y-auto">
            {data.outline.modules?.map((module: any, index: number) => (
              <div key={index} className="p-3 bg-dark-400 rounded-lg">
                <div className="font-medium text-text-primary">
                  Module {index + 1}: {module.title}
                </div>
                {module.lessons && (
                  <div className="text-sm text-text-muted mt-1">
                    {module.lessons.length} lesson{module.lessons.length > 1 ? 's' : ''}
                    {module.duration && ` • ${module.duration}`}
                  </div>
                )}
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Additional Context */}
      {data.context && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Additional Context</h3>
          <p className="text-text-secondary">{data.context}</p>
        </Card>
      )}

      {/* Action Buttons */}
      <Card className="p-6">
        <div className="flex items-center justify-center space-x-4">
          <Button variant="outline" icon={<Download className="w-4 h-4" />}>
            Export Course
          </Button>
          <Button variant="outline" icon={<Share className="w-4 h-4" />}>
            Share Course
          </Button>
        </div>
      </Card>

      {/* Completion Status */}
      {completionPercentage < 100 && (
        <Card className="p-6 bg-electric-blue/5 border-electric-blue/20">
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-electric-blue">Complete Your Course</h3>
            <p className="text-text-secondary">
              Your course is {completionPercentage}% complete. Generate the remaining components to create a full course package.
            </p>
            <div className="space-y-2">
              {!completionStats.outline && (
                <div className="flex items-center space-x-2 text-sm text-text-muted">
                  <CheckCircle className="w-4 h-4" />
                  <span>Generate course outline</span>
                </div>
              )}
              {!completionStats.scripts && (
                <div className="flex items-center space-x-2 text-sm text-text-muted">
                  <FileText className="w-4 h-4" />
                  <span>Create lesson scripts</span>
                </div>
              )}
              {!completionStats.videos && (
                <div className="flex items-center space-x-2 text-sm text-text-muted">
                  <Video className="w-4 h-4" />
                  <span>Generate course videos</span>
                </div>
              )}
              {!completionStats.qa && (
                <div className="flex items-center space-x-2 text-sm text-text-muted">
                  <HelpCircle className="w-4 h-4" />
                  <span>Create Q&A content</span>
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {completionPercentage === 100 && (
        <Card className="p-6 bg-electric-green/5 border-electric-green/20">
          <div className="text-center space-y-3">
            <CheckCircle className="w-12 h-12 text-electric-green mx-auto" />
            <h3 className="text-lg font-semibold text-electric-green">Course Ready!</h3>
            <p className="text-text-secondary">
              Your course is complete and ready to be created. Click "Create Course" to generate the final package.
            </p>
          </div>
        </Card>
      )}
    </div>
  );
}