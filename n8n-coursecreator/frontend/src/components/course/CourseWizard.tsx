import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  Circle,
  GraduationCap,
  FileText,
  Video,
  HelpCircle,
  Loader2,
  Sparkles,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { cn } from '@/utils/cn';
import { CourseDetailsStep } from './steps/CourseDetailsStep';
import { CourseOutlineStep } from './steps/CourseOutlineStep';
import { CourseScriptsStep } from './steps/CourseScriptsStep';
import { CourseVideosStep } from './steps/CourseVideosStep';
import { CourseQAStep } from './steps/CourseQAStep';
import { CourseReviewStep } from './steps/CourseReviewStep';

export interface CourseData {
  // Basic details
  title: string;
  description: string;
  style: string;
  tone: string;
  depth: string;
  target_audience: string;
  duration: string;
  context: string;
  
  // Generation options
  avatar_id: string;
  voice_id: string;
  video_quality: string;
  qa_count: number;
  include_audio: boolean;
  include_video: boolean;
  
  // Generated content
  outline?: any;
  scripts?: any[];
  videos?: any[];
  qa?: any[];
}

const steps = [
  {
    id: 'details',
    title: 'Course Details',
    description: 'Basic information about your course',
    icon: GraduationCap,
  },
  {
    id: 'outline',
    title: 'Generate Outline',
    description: 'AI-generated course structure',
    icon: FileText,
  },
  {
    id: 'scripts',
    title: 'Create Scripts',
    description: 'Lesson scripts and content',
    icon: FileText,
  },
  {
    id: 'videos',
    title: 'Generate Videos',
    description: 'Video content creation',
    icon: Video,
  },
  {
    id: 'qa',
    title: 'Q&A Generation',
    description: 'Quiz and assessment content',
    icon: HelpCircle,
  },
  {
    id: 'review',
    title: 'Review & Create',
    description: 'Final review and creation',
    icon: CheckCircle,
  },
];

interface CourseWizardProps {
  onComplete: (data: CourseData) => void;
  onCancel: () => void;
}

export function CourseWizard({ onComplete, onCancel }: CourseWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [courseData, setCourseData] = useState<CourseData>({
    title: '',
    description: '',
    style: 'professional',
    tone: 'engaging',
    depth: 'intermediate',
    target_audience: 'general audience',
    duration: '4-6 hours',
    context: '',
    avatar_id: 'default',
    voice_id: 'default',
    video_quality: '720p',
    qa_count: 5,
    include_audio: true,
    include_video: false,
  });
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

  const updateCourseData = (updates: Partial<CourseData>) => {
    setCourseData(prev => ({ ...prev, ...updates }));
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setCurrentStep(prev => prev + 1);
    } else {
      // Final step - create course
      onComplete(courseData);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const isStepCompleted = (stepIndex: number) => {
    return completedSteps.has(stepIndex);
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Details
        return courseData.title.trim() !== '';
      case 1: // Outline
        return courseData.outline !== undefined;
      case 2: // Scripts
        return courseData.scripts && courseData.scripts.length > 0;
      case 3: // Videos
        return courseData.videos && courseData.videos.length > 0;
      case 4: // Q&A
        return courseData.qa && courseData.qa.length > 0;
      case 5: // Review
        return true;
      default:
        return false;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <CourseDetailsStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 1:
        return (
          <CourseOutlineStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 2:
        return (
          <CourseScriptsStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 3:
        return (
          <CourseVideosStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 4:
        return (
          <CourseQAStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 5:
        return (
          <CourseReviewStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="inline-flex items-center space-x-2 px-4 py-2 bg-electric-blue/10 border border-electric-blue/20 rounded-full">
          <Sparkles className="w-4 h-4 text-electric-blue" />
          <span className="text-sm font-medium text-electric-blue">AI Course Creator</span>
        </div>
        <h1 className="text-3xl font-bold text-text-primary">Create Your Course</h1>
        <p className="text-text-secondary">Follow the steps to generate a complete course with AI</p>
      </div>

      {/* Progress Steps */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = isStepCompleted(index);
            const isUpcoming = index > currentStep;

            return (
              <React.Fragment key={step.id}>
                <div className="flex flex-col items-center space-y-2 flex-1">
                  {/* Step Icon */}
                  <motion.div
                    className={cn(
                      'w-12 h-12 rounded-full border-2 flex items-center justify-center transition-all duration-300',
                      isActive && 'border-electric-blue bg-electric-blue/10 scale-110',
                      isCompleted && 'border-electric-green bg-electric-green/10',
                      isUpcoming && 'border-white/20 bg-dark-400'
                    )}
                    whileHover={{ scale: 1.05 }}
                  >
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6 text-electric-green" />
                    ) : (
                      <Icon className={cn(
                        'w-6 h-6',
                        isActive && 'text-electric-blue',
                        isUpcoming && 'text-text-muted'
                      )} />
                    )}
                  </motion.div>

                  {/* Step Info */}
                  <div className="text-center">
                    <div className={cn(
                      'text-sm font-medium',
                      isActive && 'text-electric-blue',
                      isCompleted && 'text-electric-green',
                      isUpcoming && 'text-text-muted'
                    )}>
                      {step.title}
                    </div>
                    <div className="text-xs text-text-muted hidden sm:block">
                      {step.description}
                    </div>
                  </div>
                </div>

                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="flex-1 h-px bg-white/10 mx-4 relative">
                    <motion.div
                      className="absolute inset-0 bg-electric-blue"
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: isCompleted ? 1 : 0 }}
                      transition={{ duration: 0.5 }}
                      style={{ transformOrigin: 'left' }}
                    />
                  </div>
                )}
              </React.Fragment>
            );
          })}
        </div>
      </Card>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {renderStepContent()}
        </motion.div>
      </AnimatePresence>

      {/* Navigation */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={onCancel}
              icon={<ArrowLeft className="w-4 h-4" />}
            >
              Cancel
            </Button>
            {currentStep > 0 && (
              <Button
                variant="outline"
                onClick={handlePrevious}
                icon={<ArrowLeft className="w-4 h-4" />}
              >
                Previous
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2 text-sm text-text-muted">
            <span>Step {currentStep + 1} of {steps.length}</span>
          </div>

          <Button
            onClick={handleNext}
            disabled={!canProceed()}
            icon={currentStep === steps.length - 1 ? <Sparkles className="w-4 h-4" /> : <ArrowRight className="w-4 h-4" />}
          >
            {currentStep === steps.length - 1 ? 'Create Course' : 'Next'}
          </Button>
        </div>
      </Card>
    </div>
  );
}