import React from 'react';
import { Outlet } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { useAppStore } from '@/store/useAppStore';
import { cn } from '@/utils/cn';

export function Layout() {
  const { sidebarOpen } = useAppStore();

  return (
    <div className="min-h-screen bg-dark-300 text-text-primary">
      {/* Background gradient */}
      <div className="fixed inset-0 bg-gradient-to-br from-dark-300 via-dark-200 to-dark-300 pointer-events-none"></div>
      
      {/* Grid pattern overlay */}
      <div 
        className="fixed inset-0 pointer-events-none opacity-5"
        style={{
          backgroundImage: `
            linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
      ></div>

      <div className="relative flex h-screen">
        {/* Sidebar */}
        <Sidebar />

        {/* Main content area */}
        <div className={cn(
          'flex-1 flex flex-col min-w-0 transition-all duration-300',
          sidebarOpen ? 'lg:ml-0' : 'ml-0'
        )}>
          {/* Header */}
          <Header />

          {/* Page content */}
          <main className="flex-1 overflow-auto">
            <div className="container mx-auto px-6 py-8">
              <Outlet />
            </div>
          </main>
        </div>
      </div>

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#1f1f1f',
            color: '#ffffff',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '12px',
          },
          success: {
            iconTheme: {
              primary: '#00ff88',
              secondary: '#1f1f1f',
            },
          },
          error: {
            iconTheme: {
              primary: '#ff6b6b',
              secondary: '#1f1f1f',
            },
          },
        }}
      />
    </div>
  );
}