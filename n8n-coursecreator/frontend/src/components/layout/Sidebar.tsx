import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  BookOpen,
  GraduationCap,
  Activity,
  Settings,
  X,
  Menu,
  Zap,
} from 'lucide-react';
import { useAppStore } from '@/store/useAppStore';
import { cn } from '@/utils/cn';

const navigationItems = [
  {
    name: 'Dashboard',
    href: '/',
    icon: Home,
    description: 'Overview and quick actions',
  },
  {
    name: 'Course Creator',
    href: '/course',
    icon: GraduationCap,
    description: 'Create engaging courses',
  },
  {
    name: 'Book Generator',
    href: '/book',
    icon: BookOpen,
    description: 'Generate comprehensive books',
  },
  {
    name: 'Task Monitor',
    href: '/tasks',
    icon: Activity,
    description: 'Track progress',
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
    description: 'Configure preferences',
  },
];

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const { sidebarOpen, setSidebarOpen, activeTasks } = useAppStore();
  const location = useLocation();

  return (
    <>
      {/* Mobile overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <AnimatePresence mode="wait">
        {sidebarOpen && (
          <motion.aside
            initial={{ x: -320 }}
            animate={{ x: 0 }}
            exit={{ x: -320 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className={cn(
              'fixed left-0 top-0 z-50 h-full w-80 bg-dark-500 border-r border-white/10',
              'lg:relative lg:translate-x-0',
              className
            )}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-8 h-8 bg-gradient-to-br from-electric-blue to-electric-purple rounded-lg flex items-center justify-center">
                    <Zap className="w-5 h-5 text-white" />
                  </div>
                  <div className="absolute -inset-1 bg-gradient-to-br from-electric-blue to-electric-purple rounded-lg blur opacity-30"></div>
                </div>
                <div>
                  <h1 className="text-lg font-bold text-text-primary">n8n Creator</h1>
                  <p className="text-xs text-text-muted">Course & Book AI</p>
                </div>
              </div>
              <button
                onClick={() => setSidebarOpen(false)}
                className="p-2 rounded-lg hover:bg-white/5 transition-colors lg:hidden"
              >
                <X className="w-5 h-5 text-text-muted" />
              </button>
            </div>

            {/* Navigation */}
            <nav className="p-4 space-y-2">
              {navigationItems.map((item) => {
                const isActive = location.pathname === item.href;
                const Icon = item.icon;
                
                return (
                  <NavLink
                    key={item.name}
                    to={item.href}
                    className={({ isActive }) =>
                      cn(
                        'relative group flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200',
                        isActive
                          ? 'bg-electric-blue/10 text-electric-blue border border-electric-blue/20'
                          : 'text-text-secondary hover:text-text-primary hover:bg-white/5'
                      )
                    }
                  >
                    {({ isActive }) => (
                      <>
                        {/* Active indicator */}
                        {isActive && (
                          <motion.div
                            layoutId="activeTab"
                            className="absolute inset-0 bg-electric-blue/5 rounded-xl border border-electric-blue/20"
                            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                          />
                        )}
                        
                        <div className="relative z-10 flex items-center space-x-3 w-full">
                          <Icon className="w-5 h-5 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium">{item.name}</div>
                            <div className="text-xs text-text-muted group-hover:text-text-secondary transition-colors">
                              {item.description}
                            </div>
                          </div>
                          
                          {/* Active tasks indicator */}
                          {(item.name === 'Task Monitor' && activeTasks.length > 0) && (
                            <div className="flex-shrink-0">
                              <div className="w-6 h-6 bg-electric-green rounded-full flex items-center justify-center">
                                <span className="text-xs font-bold text-dark-300">
                                  {activeTasks.length}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      </>
                    )}
                  </NavLink>
                );
              })}
            </nav>

            {/* Active Tasks Summary */}
            {activeTasks.length > 0 && (
              <div className="mx-4 mt-6 p-4 bg-dark-400 rounded-xl border border-electric-green/20">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-electric-green rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-electric-green">Active Tasks</span>
                </div>
                <p className="text-xs text-text-muted">
                  {activeTasks.length} task{activeTasks.length > 1 ? 's' : ''} running in background
                </p>
              </div>
            )}

            {/* Footer */}
            <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-white/10">
              <div className="flex items-center space-x-3 text-xs text-text-muted">
                <div className="w-2 h-2 bg-electric-green rounded-full"></div>
                <span>API Connected</span>
              </div>
            </div>
          </motion.aside>
        )}
      </AnimatePresence>

      {/* Mobile menu button */}
      <button
        onClick={() => setSidebarOpen(true)}
        className="fixed top-4 left-4 z-30 p-3 bg-dark-500 border border-white/10 rounded-xl lg:hidden"
      >
        <Menu className="w-5 h-5 text-text-primary" />
      </button>
    </>
  );
}