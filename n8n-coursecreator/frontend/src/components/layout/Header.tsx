import React from 'react';
import { motion } from 'framer-motion';
import { Bell, Search, User, Zap } from 'lucide-react';
import { useAppStore } from '@/store/useAppStore';

export function Header() {
  const { activeTasks, statistics } = useAppStore();

  return (
    <header className="sticky top-0 z-30 h-16 bg-dark-500/80 backdrop-blur-xl border-b border-white/10">
      <div className="flex items-center justify-between h-full px-6">
        {/* Left side - Search */}
        <div className="flex items-center space-x-4 flex-1 max-w-md">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-muted" />
            <input
              type="text"
              placeholder="Search projects, tasks..."
              className="w-full pl-10 pr-4 py-2 bg-dark-400 border border-white/10 rounded-lg text-text-primary placeholder-text-muted focus:border-electric-blue focus:outline-none focus:ring-2 focus:ring-electric-blue/20 transition-all duration-200"
            />
          </div>
        </div>

        {/* Center - Stats */}
        <div className="hidden md:flex items-center space-x-6">
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-sm font-bold text-electric-blue">{statistics.coursesCreated}</div>
              <div className="text-xs text-text-muted">Courses</div>
            </div>
            <div className="w-px h-8 bg-white/10"></div>
            <div className="text-center">
              <div className="text-sm font-bold text-electric-green">{statistics.booksGenerated}</div>
              <div className="text-xs text-text-muted">Books</div>
            </div>
            <div className="w-px h-8 bg-white/10"></div>
            <div className="text-center">
              <div className="text-sm font-bold text-electric-purple">{activeTasks.length}</div>
              <div className="text-xs text-text-muted">Active</div>
            </div>
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <button className="relative p-2 rounded-lg hover:bg-white/5 transition-colors">
            <Bell className="w-5 h-5 text-text-muted" />
            {activeTasks.length > 0 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute -top-1 -right-1 w-4 h-4 bg-electric-coral rounded-full flex items-center justify-center"
              >
                <span className="text-xs font-bold text-white">{activeTasks.length}</span>
              </motion.div>
            )}
          </button>

          {/* AI Status Indicator */}
          <div className="flex items-center space-x-2 px-3 py-1.5 bg-electric-blue/10 border border-electric-blue/20 rounded-lg">
            <Zap className="w-4 h-4 text-electric-blue" />
            <span className="text-sm font-medium text-electric-blue">AI Ready</span>
          </div>

          {/* User Menu */}
          <button className="flex items-center space-x-2 p-2 rounded-lg hover:bg-white/5 transition-colors">
            <div className="w-8 h-8 bg-gradient-to-br from-electric-purple to-electric-pink rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
          </button>
        </div>
      </div>
    </header>
  );
}