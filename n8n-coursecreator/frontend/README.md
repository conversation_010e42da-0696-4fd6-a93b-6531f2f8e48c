# n8n Course Creator & Book Generator - Frontend

A vibrant, dark-themed React frontend for the n8n Course Creator and Book Generator application.

## Features

- **Dark Theme**: Sleek dark UI with vibrant accent colors
- **Course Creation**: Multi-step wizard for creating comprehensive courses
- **Book Generation**: Research-driven book generation with citations
- **Task Monitoring**: Real-time progress tracking for background tasks
- **Responsive Design**: Works seamlessly on desktop and mobile
- **AI-Powered**: Integration with FastAPI backend for AI content generation

## Tech Stack

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Router** for navigation
- **Zustand** for state management
- **Axios** for API communication

## Getting Started

### Prerequisites

- Node.js 16+ 
- npm or yarn
- Running FastAPI backend at `http://localhost:8000`

### Installation

1. Install dependencies:
```bash
npm install
```

2. Copy environment variables:
```bash
cp .env.example .env
```

3. Update the `.env` file with your API settings if needed.

### Development

Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:3000`.

### Building for Production

Build the application:
```bash
npm run build
```

Preview the production build:
```bash
npm run preview
```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components (Button, Card, etc.)
│   ├── layout/         # Layout components (Header, Sidebar, etc.)
│   ├── course/         # Course creation components
│   └── book/           # Book generation components
├── pages/              # Page components
├── services/           # API services
├── store/              # Zustand state management
├── utils/              # Utility functions
└── main.tsx           # Application entry point
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint errors
- `npm run format` - Format code with Prettier
- `npm run typecheck` - Run TypeScript type checking

## Design System

### Colors

- **Backgrounds**: Deep dark colors (#0a0a0a, #121212, #1a1a1a)
- **Accent Colors**: 
  - Electric Blue: #00d4ff
  - Neon Green: #00ff88
  - Purple: #a855f7
  - Coral: #ff6b6b
  - Pink: #ff6b9d

### Components

- **Cards**: Hover effects with glow animations
- **Buttons**: Multiple variants with smooth transitions
- **Progress Bars**: Gradient fills with animations
- **Forms**: Dark-themed inputs with focus states

## API Integration

The frontend communicates with the FastAPI backend through:

- **Course Creation**: Multi-step API calls for outline, scripts, videos, Q&A
- **Book Generation**: Research processing, outline, chapters, bibliography
- **Task Monitoring**: Real-time status updates via polling
- **Error Handling**: Comprehensive error states and user feedback

## Features Overview

### Dashboard
- Overview statistics
- Quick action cards
- Recent activity
- Background task indicators

### Course Creator
- Multi-step wizard interface
- Real-time outline generation
- Script and video creation
- Q&A generation with customization

### Book Generator  
- Research file upload with drag & drop
- Web source integration
- Chapter generation with citations
- Bibliography compilation

### Task Monitor
- Live progress tracking
- Task history
- Status indicators
- Download/export options

## Contributing

1. Follow the existing code style
2. Use TypeScript for type safety
3. Implement responsive design
4. Add proper error handling
5. Include loading states
6. Test across different screen sizes

## License

This project is part of the n8n Course Creator & Book Generator system.