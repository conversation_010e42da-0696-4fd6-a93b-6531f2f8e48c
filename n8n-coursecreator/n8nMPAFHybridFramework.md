Mastering Automation: The n8n-MPAF Hybrid Approach for Scalable Applications
Automation is revolutionizing productivity, and combining n8n, a no-code workflow automation platform, with the Modular Python Agent Framework (MPAF), a modular Python-based processing framework, creates a powerful hybrid system. As highlighted in a Reddit post from the r/n8n community, n8n excels as a “conductor” orchestrating workflows, while external tools like MPAF handle “heavy lifting” tasks such as AI content generation or data analysis. This article presents three production-ready applications—Social Media Content Automator, Personal Finance Tracker, and Startup Idea Generator—demonstrating how n8n and MPAF collaborate seamlessly in each. We’ll provide complete code, a clear architectural overview, deployment guidance, and practical steps to eliminate any confusion about their integrated roles.
Clarifying the n8n-MPAF Relationship
A common question is whether n8n or MPAF can be used independently. The answer is unequivocal: both n8n and MPAF are used together in every application. They are not alternatives but complementary components of a hybrid architecture:
* n8n: Acts as the workflow orchestrator, managing triggers (e.g., cron, webhooks), fetching data from APIs (e.g., Google Trends, Plaid, Reddit), routing data, and integrating with output services (e.g., Buffer, Google Sheets, Notion). Its no-code interface and 400+ integrations make it ideal for non-technical users and rapid prototyping (Reddit: “amazing for orchestration and simple integrations”). However, it’s not suited for computationally intensive tasks like AI processing or complex logic (Reddit: “terrible for heavy file processing”).
* MPAF: Handles specialized, resource-intensive tasks (e.g., generating AI content, categorizing transactions, scraping data) using modular, asynchronous Python tools. It leverages APIs like Gemini, OpenAI, or Replicate for processing and is designed for scalability and extensibility (MPAF: “Modular Architecture”).
* Why Both: n8n alone would struggle with tasks requiring custom logic or heavy computation, leading to performance issues. MPAF alone lacks n8n’s no-code integration ecosystem and visual workflow builder, making it less accessible for coordinating multiple services. Together, n8n orchestrates the workflow, while MPAF processes complex tasks, creating a scalable, user-friendly system.
Analogy: n8n is like a project manager who plans and coordinates tasks across tools, while MPAF is a team of expert developers executing specialized tasks. n8n assigns work to MPAF via HTTP requests, MPAF processes and returns results, and n8n integrates them into the final output.
Architectural Overview and Diagram
The n8n-MPAF interaction follows a client-server model where n8n sends HTTP requests to MPAF’s serverless endpoints, and MPAF returns processed data. The architecture is consistent across all three applications.
Refined ASCII Diagram
This diagram illustrates the n8n-MPAF interaction for all apps:
+------------------+       +------------------+
|   n8n Workflow   |       |  MPAF (Python)   |
|                  |       |                  |
| [Trigger]        |----->| [AgentRunner]    |
| (Cron/Webhook)   |       |                  |
|                  |       |                  |
| [Data Fetch]     |----->| [Tool 1]         |
| (e.g., Trends,   |<-----| (e.g., Content)  |
|  Plaid, Reddit)  |       |                  |
|                  |       |                  |
| [Process/Route]  |----->| [Tool 2]         |
| (Function Node)  |<-----| (e.g., Image,    |
|                  |       |  Budget)         |
|                  |       |                  |
| [Output]         |       |                  |
| (Buffer, Sheets, |       |                  |
|  Notion, Email)  |       |                  |
+------------------+       +------------------+
       |                            |
       v                            v
+------------------+       +------------------+
| External Services|       | External APIs    |
| (Buffer, Sheets, |       | (Gemini, OpenAI, |
|  Notion, Email)  |       |  Replicate, etc) |
+------------------+       +------------------+
Visualization in draw.io:
1. Create rectangles for n8n nodes: Trigger, Data Fetch, Process/Route, Output.
2. Create ovals for MPAF components: AgentRunner, Tool 1, Tool 2.
3. Create clouds for External Services and APIs.
4. Use solid arrows for n8n internal flow, dashed arrows for n8n-MPAF HTTP requests, and dotted arrows for MPAF-API calls.
5. Label arrows with data types (e.g., {"topic": "AI trends"}, {"content": "..."}).
6. Color-code: blue for n8n, green for MPAF, gray for external services/APIs.
Interaction Mechanism
1. Trigger: n8n initiates the workflow with a Cron or Webhook node (e.g., daily for Social Media Automator, transaction updates for Personal Finance Tracker).
2. Data Fetch: n8n’s HTTP Request nodes fetch data from external APIs (e.g., Google Trends, Plaid, Reddit).
3. Process/Route: Function nodes filter or transform data for MPAF.
4. MPAF Call: n8n sends HTTP POST requests to MPAF’s serverless endpoints (e.g., /content, /categorize), passing JSON payloads.
5. MPAF Processing: MPAF’s AgentRunner initializes AgentState, executes tools (e.g., ContentGeneratorTool, TransactionCategorizerTool), and calls external APIs (e.g., Gemini, OpenAI).
6. Return: MPAF returns JSON responses to n8n (e.g., {"content": "AI post"}, {"category": "Food"}).
7. Output: n8n integrates results with services like Buffer, Google Sheets, or Notion and sends notifications via Email.
Benefits
* Scalability: n8n handles lightweight orchestration; MPAF’s serverless tools manage heavy processing (Reddit: “Delegate Heavy Processing to External Services”).
* Modularity: n8n’s workflows and MPAF’s tools are easily extended (MPAF: “Extensible Design”).
* Cost-Effectiveness: n8n can run on low-power devices (Reddit: “0.4W on Android phone”); MPAF uses serverless platforms.
* Ease of Use: n8n’s no-code interface suits non-technical users; MPAF requires coding for tool development but is modular.
The Applications: Complete Code and Details
Below, we detail each application, providing complete n8n workflows, MPAF code, interaction flows, and deployment steps. Each app uses both n8n and MPAF, ensuring clarity that they are not either/or.
1. Social Media Content Automator
Overview: Automates content creation, image generation, and scheduling for social media platforms (X, LinkedIn, Instagram). Ideal for marketers or small businesses.
n8n Workflow:
* Trigger: Cron node runs daily at 8 AM.
* Data Fetch: Fetches trending topics from Google Trends.
* Process/Route: Filters topics for relevance (e.g., technology niche).
* MPAF Calls: Triggers MPAF for content and image generation.
* Output: Schedules posts via Buffer, logs analytics in Google Sheets.
n8n Workflow JSON:
{
  "name": "Social Media Content Automator",
  "nodes": [
    {
      "parameters": { "cronExpression": "0 8 * * *" },
      "name": "Cron",
      "type": "n8n-nodes-base.cron",
      "typeVersion": 1,
      "position": [100, 300]
    },
    {
      "parameters": {
        "url": "https://trends.google.com/trends/api/dailytrends?geo=US",
        "options": {}
      },
      "name": "Get Trends",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [300, 300]
    },
    {
      "parameters": {
        "functionCode": "const niche = 'technology'; return items.filter(item => item.json.trendingSearches.some(search => search.title.query.includes(niche))).map(item => ({ json: { topic: item.json.trendingSearches[0].title.query } }));"
      },
      "name": "Filter Topics",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [500, 300]
    },
    {
      "parameters": {
        "url": "{{$node['Filter Topics'].json['topic'] ? 'http://python-agent-endpoint/content' : ''}}",
        "options": {
          "bodyContentType": "json",
          "body": { "platform": "X", "topic": "{{$node['Filter Topics'].json['topic']}}" }
        }
      },
      "name": "Generate Content",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [700, 300]
    },
    {
      "parameters": {
        "url": "{{$node['Filter Topics'].json['topic'] ? 'http://python-agent-endpoint/image' : ''}}",
        "options": {
          "bodyContentType": "json",
          "body": { "topic": "{{$node['Filter Topics'].json['topic']}}" }
        }
      },
      "name": "Create Image",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [900, 300]
    },
    {
      "parameters": {
        "operation": "create",
        "post": {
          "text": "{{$node['Generate Content'].json['content']}}",
          "media": "{{$node['Create Image'].json['image_url']}}",
          "networks": ["twitter", "linkedin", "instagram"]
        }
      },
      "name": "Schedule Post",
      "type": "n8n-nodes-base.buffer",
      "typeVersion": 1,
      "position": [1100, 300],
      "credentials": { "bufferApi": "Buffer Credentials" }
    },
    {
      "parameters": {
        "operation": "append",
        "sheetId": "your-sheet-id",
        "range": "Sheet1!A:C",
        "values": [
          "{{$node['Generate Content'].json['content']}}",
          "{{$node['Create Image'].json['image_url']}}",
          "{{new Date().toISOString()}}"
        ]
      },
      "name": "Log Metrics",
      "type": "n8n-nodes-base.googleSheets",
      "typeVersion": 1,
      "position": [1300, 300],
      "credentials": { "googleSheetsApi": "Google Sheets Credentials" }
    }
  ],
  "connections": {
    "Cron": { "main": [[{"node": "Get Trends", "type": "main", "index": 0}]] },
    "Get Trends": { "main": [[{"node": "Filter Topics", "type": "main", "index": 0}]] },
    "Filter Topics": { "main": [[{"node": "Generate Content", "type": "main", "index": 0}]] },
    "Generate Content": { "main": [[{"node": "Create Image", "type": "main", "index": 0}]] },
    "Create Image": { "main": [[{"node": "Schedule Post", "type": "main", "index": 0}]] },
    "Schedule Post": { "main": [[{"node": "Log Metrics", "type": "main", "index": 0}]] }
  }
}
MPAF Code:
tools/content_generator_tool.py:
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class ContentGeneratorTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="content_generator",
            description="Generates platform-specific social media posts using Gemini API."
        )

    async def execute(self, state: AgentState, platform: str, topic: str) -> ToolResult:
        error = self.validate_required_params(["platform", "topic"], platform=platform, topic=topic)
        if error:
            return ToolResult.error_result(error)
        self.logger.info(f"Generating content for {platform} on topic: {topic}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {settings.GEMINI_API_KEY}"}
                payload = {
                    "model": settings.AGENT_MODEL,
                    "prompt": f"Generate a {platform} post about {topic}. Keep it engaging and under 280 characters."
                }
                async with session.post("https://api.google.com/gemini/v1/generate", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ToolResult.success_result({"content": data["text"]})
                    return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

content_generator_tool = ContentGeneratorTool()
tools/image_creator_tool.py:
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class ImageCreatorTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="image_creator",
            description="Creates visuals for social media posts using Replicate API."
        )

    async def execute(self, state: AgentState, topic: str) -> ToolResult:
        error = self.validate_required_params(["topic"], topic=topic)
        if error:
            return ToolResult.error_result(error)
        self.logger.info(f"Creating image for topic: {topic}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Token {settings.REPLICATE_API_TOKEN}"}
                payload = {
                    "prompt": f"A vibrant illustration of {topic} for social media",
                    "model": "stable-diffusion"
                }
                async with session.post("https://api.replicate.com/v1/predictions", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ToolResult.success_result({"image_url": data["output_url"]})
                    return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

image_creator_tool = ImageCreatorTool()
agent.py:
#!/usr/bin/env python3
import asyncio
import argparse
import sys
import logging
from config.settings import settings
from src.models import AgentState, AgentStatus
from tools.content_generator_tool import content_generator_tool
from tools.image_creator_tool import image_creator_tool

class AgentRunner:
    def __init__(self, topic, context="", publish=False, verbose=False):
        self.state = AgentState(topic=topic, context=context)
        self.publish = publish
        self.verbose = verbose
        logging.basicConfig(level=logging.INFO if self.verbose else logging.WARNING)

    async def run_pipeline(self):
        self.state.status = AgentStatus.RUNNING
        logging.info(f"Starting agent run for topic: {self.state.topic}")
        try:
            content_result = await content_generator_tool.execute(state=self.state, platform="X", topic=self.state.topic)
            if not content_result.success:
                raise Exception(f"Content generation failed: {content_result.error}")
            self.state.research_data["x_content"] = content_result.data["content"]
            logging.info("Step 1: X content generated.")

            image_result = await image_creator_tool.execute(state=self.state, topic=self.state.topic)
            if not image_result.success:
                raise Exception(f"Image creation failed: {image_result.error}")
            self.state.research_data["image_url"] = image_result.data["image_url"]
            logging.info("Step 2: Image created.")

            self.state.final_output = {"content": self.state.research_data["x_content"], "image": self.state.research_data["image_url"]}
            self.state.status = AgentStatus.COMPLETED
            logging.info("Agent pipeline completed successfully.")
        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.error_log.append(str(e))
            logging.error(f"Agent pipeline failed: {e}")
        finally:
            self.present_output()

    def present_output(self):
        print("\n" + "="*50)
        print("AGENT RUN COMPLETE")
        print("="*50)
        print(f"Status: {self.state.status.value}")
        print(f"Topic: {self.state.topic}")
        if self.state.status == AgentStatus.COMPLETED:
            print("\n--- FINAL OUTPUT ---")
            print(self.state.final_output)
        else:
            print("\n--- ERRORS ---")
            for error in self.state.error_log:
                print(error)

def create_parser():
    parser = argparse.ArgumentParser(description="Modular Python Agent Framework")
    parser.add_argument("topic", help="The main topic for the agent to process.")
    parser.add_argument("--context", "-c", default="", help="Additional context for the agent.")
    parser.add_argument("--publish", "-p", action="store_true", help="Enable publishing actions.")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable detailed logging.")
    return parser

async def main():
    parser = create_parser()
    args = parser.parse_args()
    agent = AgentRunner(topic=args.topic, context=args.context, publish=args.publish, verbose=args.verbose)
    await agent.run_pipeline()

if __name__ == "__main__":
    asyncio.run(main())
Interaction:
1. n8n’s Cron node triggers at 8 AM, fetching trends from Google Trends.
2. Function node filters topics (e.g., “AI trends”).
3. HTTP Request nodes call MPAF endpoints (/content, /image), sending {"platform": "X", "topic": "AI trends"} and {"topic": "AI trends"}.
4. MPAF’s AgentRunner executes ContentGeneratorTool (Gemini API) and ImageCreatorTool (Replicate API), returning {"content": "AI post"} and {"image_url": "..."}.
5. n8n schedules posts via Buffer and logs data in Google Sheets.
Data Flow:
n8n: {"topic": "AI trends"} → MPAF: {"platform": "X", "topic": "AI trends"} → Gemini → MPAF: {"content": "AI post"} → n8n
n8n: {"topic": "AI trends"} → MPAF: {"topic": "AI trends"} → Replicate → MPAF: {"image_url": "..."} → n8n
n8n → Buffer: {"text": "AI post", "media": "..."} → Google Sheets: ["AI post", "...", "2025-08-01"]
Deployment:
* n8n: Self-host on an Android phone with Termux (Reddit: “0.4W power consumption”) or DigitalOcean. Configure Buffer and Google Sheets credentials.
* MPAF: Deploy as an AWS Lambda function with endpoints /content and /image. Set API keys in .env: GEMINI_API_KEY=your-key
* REPLICATE_API_TOKEN=your-token
* AGENT_MODEL=gemini-1.5-flash
* 
2. Personal Finance Tracker
Overview: Automates expense tracking, categorization, and budgeting using Plaid for transactions. Ideal for individuals or freelancers.
n8n Workflow:
* Trigger: Webhook for Plaid transaction updates.
* Data Fetch: Fetches transaction details from Plaid.
* MPAF Calls: Categorizes transactions and analyzes budget.
* Output: Updates Google Sheets, sends email alerts for overspending.
n8n Workflow JSON:
{
  "name": "Personal Finance Tracker",
  "nodes": [
    {
      "parameters": { "path": "plaid-webhook", "httpMethod": "POST" },
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [100, 300]
    },
    {
      "parameters": {
        "url": "https://api.plaid.com/transactions/get",
        "options": {
          "bodyContentType": "json",
          "body": {
            "access_token": "{{$node['Webhook'].json['access_token'] || 'your-plaid-token'}}",
            "start_date": "{{new Date().toISOString().split('T')[0]}}",
            "end_date": "{{new Date().toISOString().split('T')[0]}}"
          },
          "headers": { "Authorization": "Bearer your-plaid-secret" }
        }
      },
      "name": "Get Transactions",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [300, 300]
    },
    {
      "parameters": {
        "url": "{{$node['Get Transactions'].json['transactions'] ? 'http://python-agent-endpoint/categorize' : ''}}",
        "options": {
          "bodyContentType": "json",
          "body": { "transaction": "{{$node['Get Transactions'].json['transactions'][0]}}" }
        }
      },
      "name": "Categorize Transaction",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [500, 300]
    },
    {
      "parameters": {
        "operation": "append",
        "sheetId": "your-sheet-id",
        "range": "Expenses!A:D",
        "values": [
          "{{$node['Categorize Transaction'].json['transaction']['description']}}",
          "{{$node['Categorize Transaction'].json['category']}}",
          "{{$node['Categorize Transaction'].json['transaction']['amount']}}",
          "{{new Date().toISOString()}}"
        ]
      },
      "name": "Update Expenses",
      "type": "n8n-nodes-base.googleSheets",
      "typeVersion": 1,
      "position": [700, 300],
      "credentials": { "googleSheetsApi": "Google Sheets Credentials" }
    },
    {
      "parameters": {
        "url": "http://python-agent-endpoint/budget",
        "options": {
          "bodyContentType": "json",
          "body": {
            "expenses": "{{$node['Categorize Transaction'].json}}",
            "budget": {"Food": 500, "Transport": 200, "Entertainment": 100, "Bills": 1000, "Other": 300}
          }
        }
      },
      "name": "Analyze Budget",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [900, 300]
    },
    {
      "parameters": {
        "sendTo": "<EMAIL>",
        "subject": "Budget Alert",
        "message": "{{$node['Analyze Budget'].json['alerts'].length > 0 ? $node['Analyze Budget'].json['alerts'].join('\n') : 'No budget alerts.'}}",
        "condition": "{{$node['Analyze Budget'].json['alerts'].length > 0}}"
      },
      "name": "Send Alert",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 1,
      "position": [1100, 300],
      "credentials": { "smtp": "SMTP Credentials" }
    }
  ],
  "connections": {
    "Webhook": { "main": [[{"node": "Get Transactions", "type": "main", "index": 0}]] },
    "Get Transactions": { "main": [[{"node": "Categorize Transaction", "type": "main", "index": 0}]] },
    "Categorize Transaction": { "main": [[{"node": "Update Expenses", "type": "main", "index": 0}]] },
    "Update Expenses": { "main": [[{"node": "Analyze Budget", "type": "main", "index": 0}]] },
    "Analyze Budget": { "main": [[{"node": "Send Alert", "type": "main", "index": 0}]] }
  }
}
MPAF Code:
tools/transaction_categorizer_tool.py:
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class TransactionCategorizerTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="transaction_categorizer",
            description="Categorizes bank transactions using OpenAI."
        )

    async def execute(self, state: AgentState, transaction: dict) -> ToolResult:
        error = self.validate_required_params(["transaction"], transaction=transaction)
        if error:
            return ToolResult.error_result(error)
        self.logger.info(f"Categorizing transaction: {transaction['description']}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {settings.OPENAI_API_KEY}"}
                payload = {
                    "model": "gpt-4o-mini",
                    "messages": [
                        {"role": "system", "content": "Categorize this transaction into one of: Food, Transport, Entertainment, Bills, Other."},
                        {"role": "user", "content": f"Description: {transaction['description']}, Amount: {transaction['amount']}"}
                    ]
                }
                async with session.post("https://api.openai.com/v1/chat/completions", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        category = data["choices"][0]["message"]["content"]
                        return ToolResult.success_result({"category": category, "transaction": transaction})
                    return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

transaction_categorizer_tool = TransactionCategorizerTool()
tools/budget_analyzer_tool.py:
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings

class BudgetAnalyzerTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="budget_analyzer",
            description="Analyzes expenses against budget and suggests adjustments."
        )

    async def execute(self, state: AgentState, expenses: list, budget: dict) -> ToolResult:
        error = self.validate_required_params(["expenses", "budget"], expenses=expenses, budget=budget)
        if error:
            return ToolResult.error_result(error)
        self.logger.info("Analyzing budget.")
        try:
            category_totals = {}
            for expense in expenses:
                category = expense["category"]
                amount = expense["transaction"]["amount"]
                category_totals[category] = category_totals.get(category, 0) + amount

            alerts = []
            for category, total in category_totals.items():
                if total > budget.get(category, 0):
                    alerts.append(f"Overspent in {category}: ${total - budget[category]:.2f}")

            return ToolResult.success_result({"totals": category_totals, "alerts": alerts})
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

budget_analyzer_tool = BudgetAnalyzerTool()
agent.py:
#!/usr/bin/env python3
import asyncio
import argparse
import sys
import logging
from config.settings import settings
from src.models import AgentState, AgentStatus
from tools.transaction_categorizer_tool import transaction_categorizer_tool
from tools.budget_analyzer_tool import budget_analyzer_tool

class AgentRunner:
    def __init__(self, topic, context="", publish=False, verbose=False):
        self.state = AgentState(topic=topic, context=context)
        self.publish = publish
        self.verbose = verbose
        logging.basicConfig(level=logging.INFO if self.verbose else logging.WARNING)

    async def run_pipeline(self):
        self.state.status = AgentStatus.RUNNING
        logging.info(f"Starting agent run for topic: {self.state.topic}")
        try:
            transactions = self.state.research_data.get("transactions", [])
            categorized = []
            for transaction in transactions:
                result = await transaction_categorizer_tool.execute(state=self.state, transaction=transaction)
                if not result.success:
                    raise Exception(f"Categorization failed: {result.error}")
                categorized.append(result.data)
            self.state.research_data["categorized"] = categorized
            logging.info("Step 1: Transactions categorized.")

            budget = {"Food": 500, "Transport": 200, "Entertainment": 100, "Bills": 1000, "Other": 300}
            analysis_result = await budget_analyzer_tool.execute(state=self.state, expenses=categorized, budget=budget)
            if not analysis_result.success:
                raise Exception(f"Budget analysis failed: {analysis_result.error}")
            self.state.final_output = analysis_result.data
            logging.info("Step 2: Budget analyzed.")

            self.state.status = AgentStatus.COMPLETED
            logging.info("Agent pipeline completed successfully.")
        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.error_log.append(str(e))
            logging.error(f"Agent pipeline failed: {e}")
        finally:
            self.present_output()

    def present_output(self):
        print("\n" + "="*50)
        print("AGENT RUN COMPLETE")
        print("="*50)
        print(f"Status: {self.state.status.value}")
        print(f"Topic: {self.state.topic}")
        if self.state.status == AgentStatus.COMPLETED:
            print("\n--- FINAL OUTPUT ---")
            print(self.state.final_output)
        else:
            print("\n--- ERRORS ---")
            for error in self.state.error_log:
                print(error)

def create_parser():
    parser = argparse.ArgumentParser(description="Modular Python Agent Framework")
    parser.add_argument("topic", help="The main topic for the agent to process.")
    parser.add_argument("--context", "-c", default="", help="Additional context for the agent.")
    parser.add_argument("--publish", "-p", action="store_true", help="Enable publishing actions.")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable detailed logging.")
    return parser

async def main():
    parser = create_parser()
    args = parser.parse_args()
    agent = AgentRunner(topic=args.topic, context=args.context, publish=args.publish, verbose=args.verbose)
    await agent.run_pipeline()

if __name__ == "__main__":
    asyncio.run(main())
Interaction:
1. n8n’s Webhook node receives Plaid transaction updates.
2. HTTP Request node fetches transaction details.
3. HTTP Request node sends transactions to MPAF’s /categorize endpoint.
4. MPAF’s TransactionCategorizerTool categorizes transactions using OpenAI.
5. n8n updates Google Sheets with categorized data.
6. HTTP Request node sends data to MPAF’s /budget endpoint.
7. MPAF’s BudgetAnalyzerTool returns totals and alerts.
8. n8n sends email alerts if overspending occurs.
Data Flow:
n8n: {"transaction": {"description": "Starbucks $5.00", "amount": 5.00}} → MPAF: {"transaction": {...}} → OpenAI → MPAF: {"category": "Food", "transaction": {...}} → n8n
n8n: {"expenses": [...]} → MPAF: {"expenses": [...], "budget": {...}} → MPAF: {"totals": {...}, "alerts": [...]} → n8n
n8n → Google Sheets: ["Starbucks $5.00", "Food", 5.00, "2025-08-01"] → Email: "Overspent in Food: $50"
Deployment:
* n8n: Host on DigitalOcean or Android phone with Termux. Configure Plaid, Google Sheets, and SMTP credentials.
* MPAF: Deploy on Render with endpoints /categorize and /budget. Set API keys in .env: OPENAI_API_KEY=your-key
* PLAID_TOKEN=your-token
* PLAID_SECRET=your-secret
* 
3. Startup Idea Generator
Overview: Generates startup ideas by scraping Reddit posts and analyzing them with AI. Runs weekly for entrepreneurs.
n8n Workflow:
* Trigger: Cron node runs weekly (Sunday, 9 AM).
* Data Fetch: Fetches posts from r/Entrepreneur.
* MPAF Calls: Scrapes posts and generates ideas.
* Output: Stores ideas in Notion, sends email notifications.
n8n Workflow JSON:
{
  "name": "Startup Idea Generator",
  "nodes": [
    {
      "parameters": { "cronExpression": "0 9 * * 0" },
      "name": "Cron",
      "type": "n8n-nodes-base.cron",
      "typeVersion": 1,
      "position": [100, 300]
    },
    {
      "parameters": {
        "url": "https://www.reddit.com/r/Entrepreneur/new.json?limit=10",
        "options": { "headers": { "User-Agent": "n8n-agent/1.0" } }
      },
      "name": "Get Reddit Posts",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [300, 300]
    },
    {
      "parameters": {
        "url": "{{$node['Get Reddit Posts'].json['data']['children'] ? 'http://python-agent-endpoint/scrape' : ''}}",
        "options": {
          "bodyContentType": "json",
          "body": { "subreddit": "Entrepreneur", "limit": 10 }
        }
      },
      "name": "Scrape Posts",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [500, 300]
    },
    {
      "parameters": {
        "url": "{{$node['Scrape Posts'].json['posts'] ? 'http://python-agent-endpoint/ideas' : ''}}",
        "options": {
          "bodyContentType": "json",
          "body": { "posts": "{{$node['Scrape Posts'].json['posts']}}" }
        }
      },
      "name": "Generate Ideas",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [700, 300]
    },
    {
      "parameters": {
        "operation": "create",
        "databaseId": "your-notion-database-id",
        "properties": {
          "Name": "{{$node['Generate Ideas'].json['text']}}",
          "Date": "{{new Date().toISOString()}}"
        }
      },
      "name": "Store Ideas",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [900, 300],
      "credentials": { "notionApi": "Notion Credentials" }
    },
    {
      "parameters": {
        "sendTo": "<EMAIL>",
        "subject": "New Startup Ideas",
        "message": "New ideas have been added to Notion: {{$node['Generate Ideas'].json['text']}}"
      },
      "name": "Notify User",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 1,
      "position": [1100, 300],
      "credentials": { "smtp": "SMTP Credentials" }
    }
  ],
  "connections": {
    "Cron": { "main": [[{"node": "Get Reddit Posts", "type": "main", "index": 0}]] },
    "Get Reddit Posts": { "main": [[{"node": "Scrape Posts", "type": "main", "index": 0}]] },
    "Scrape Posts": { "main": [[{"node": "Generate Ideas", "type": "main", "index": 0}]] },
    "Generate Ideas": { "main": [[{"node": "Store Ideas", "type": "main", "index": 0}]] },
    "Store Ideas": { "main": [[{"node": "Notify User", "type": "main", "index": 0}]] }
  }
}
MPAF Code:
tools/reddit_scraper_tool.py:
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
import aiohttp

class RedditScraperTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="reddit_scraper",
            description="Scrapes posts from specified Reddit subreddits."
        )

    async def execute(self, state: AgentState, subreddit: str, limit: int = 10) -> ToolResult:
        error = self.validate_required_params(["subreddit"], subreddit=subreddit)
        if error:
            return ToolResult.error_result(error)
        self.logger.info(f"Scraping r/{subreddit}")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"User-Agent": "n8n-agent/1.0"}
                url = f"https://www.reddit.com/r/{subreddit}/new.json?limit={limit}"
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        posts = [post["data"]["title"] for post in data["data"]["children"]]
                        return ToolResult.success_result({"posts": posts})
                    return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

reddit_scraper_tool = RedditScraperTool()
tools/idea_generator_tool.py:
from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState
from config.settings import settings
import aiohttp

class IdeaGeneratorTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="idea_generator",
            description="Generates startup ideas based on Reddit posts using Gemini."
        )

    async def execute(self, state: AgentState, posts: list) -> ToolResult:
        error = self.validate_required_params(["posts"], posts=posts)
        if error:
            return ToolResult.error_result(error)
        self.logger.info("Generating startup ideas.")
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {settings.GEMINI_API_KEY}"}
                prompt = f"Based on these Reddit posts: {', '.join(posts[:5])}, suggest 3 startup ideas addressing unmet needs."
                payload = {
                    "model": settings.AGENT_MODEL,
                    "prompt": prompt
                }
                async with session.post("https://api.google.com/gemini/v1/generate", json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ToolResult.success_result({"text": data["text"]})
                    return ToolResult.error_result(f"API error: {response.status}")
        except Exception as e:
            self.logger.error(f"Error: {e}")
            return ToolResult.error_result(str(e))

idea_generator_tool = IdeaGeneratorTool()
agent.py:
#!/usr/bin/env python3
import asyncio
import argparse
import sys
import logging
from config.settings import settings
from src.models import AgentState, AgentStatus
from tools.reddit_scraper_tool import reddit_scraper_tool
from tools.idea_generator_tool import idea_generator_tool

class AgentRunner:
    def __init__(self, topic, context="", publish=False, verbose=False):
        self.state = AgentState(topic=topic, context=context)
        self.publish = publish
        self.verbose = verbose
        logging.basicConfig(level=logging.INFO if self.verbose else logging.WARNING)

    async def run_pipeline(self):
        self.state.status = AgentStatus.RUNNING
        logging.info(f"Starting agent run for topic: {self.state.topic}")
        try:
            scrape_result = await reddit_scraper_tool.execute(state=self.state, subreddit=self.state.topic, limit=10)
            if not scrape_result.success:
                raise Exception(f"Scraping failed: {scrape_result.error}")
            self.state.research_data["posts"] = scrape_result.data["posts"]
            logging.info("Step 1: Reddit posts scraped.")

            idea_result = await idea_generator_tool.execute(state=self.state, posts=self.state.research_data["posts"])
            if not idea_result.success:
                raise Exception(f"Idea generation failed: {idea_result.error}")
            self.state.final_output = idea_result.data["text"]
            logging.info("Step 2: Startup ideas generated.")

            self.state.status = AgentStatus.COMPLETED
            logging.info("Agent pipeline completed successfully.")
        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.error_log.append(str(e))
            logging.error(f"Agent pipeline failed: {e}")
        finally:
            self.present_output()

    def present_output(self):
        print("\n" + "="*50)
        print("AGENT RUN COMPLETE")
        print("="*50)
        print(f"Status: {self.state.status.value}")
        print(f"Topic: {self.state.topic}")
        if self.state.status == AgentStatus.COMPLETED:
            print("\n--- FINAL OUTPUT ---")
            print(self.state.final_output)
        else:
            print("\n--- ERRORS ---")
            for error in self.state.error_log:
                print(error)

def create_parser():
    parser = argparse.ArgumentParser(description="Modular Python Agent Framework")
    parser.add_argument("topic", help="The main topic for the agent to process.")
    parser.add_argument("--context", "-c", default="", help="Additional context for the agent.")
    parser.add_argument("--publish", "-p", action="store_true", help="Enable publishing actions.")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable detailed logging.")
    return parser

async def main():
    parser = create_parser()
    args = parser.parse_args()
    agent = AgentRunner(topic=args.topic, context=args.context, publish=args.publish, verbose=args.verbose)
    await agent.run_pipeline()

if __name__ == "__main__":
    asyncio.run(main())
Interaction:
1. n8n’s Cron node triggers weekly, fetching Reddit posts.
2. HTTP Request node sends subreddit data to MPAF’s /scrape endpoint.
3. MPAF’s RedditScraperTool scrapes posts.
4. HTTP Request node sends posts to MPAF’s /ideas endpoint.
5. MPAF’s IdeaGeneratorTool generates ideas using Gemini.
6. n8n stores ideas in Notion and sends email notifications.
Data Flow:
n8n: {"subreddit": "Entrepreneur"} → MPAF: {"subreddit": "Entrepreneur", "limit": 10} → Reddit API → MPAF: {"posts": [...]} → n8n
n8n: {"posts": [...]} → MPAF: {"posts": [...]} → Gemini → MPAF: {"text": "Idea: App for..."} → n8n
n8n → Notion: {"Name": "Idea: App for...", "Date": "2025-08-01"} → Email: "New ideas added"
Deployment:
* n8n: Host on Google Cloud Run or Android phone. Configure Reddit, Notion, and SMTP credentials.
* MPAF: Deploy on Google Cloud Functions with endpoints /scrape and /ideas. Set API keys in .env: GEMINI_API_KEY=your-key
* AGENT_MODEL=gemini-1.5-flash
* 
Shared MPAF Files
These files provide the core MPAF functionality, reused across all applications.
config/settings.py:
import os
from dotenv import load_dotenv

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
load_dotenv(os.path.join(project_root, '.env'))

class Settings:
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    AGENT_MODEL = os.getenv("AGENT_MODEL", "gemini-1.5-flash")
    REPLICATE_API_TOKEN = os.getenv("REPLICATE_API_TOKEN")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    PLAID_TOKEN = os.getenv("PLAID_TOKEN")
    PLAID_SECRET = os.getenv("PLAID_SECRET")

    def validate_required_settings(self):
        required = [var for var in ["GEMINI_API_KEY", "OPENAI_API_KEY", "REPLICATE_API_TOKEN", "PLAID_TOKEN", "PLAID_SECRET"] if not getattr(self, var, None)]
        if required:
            print(f"ERROR: Missing required environment variables: {', '.join(required)}")
            return False
        return True

settings = Settings()
src/models.py:
from dataclasses import dataclass, field
from typing import List, Dict, Any
from enum import Enum
from datetime import datetime

class AgentStatus(Enum):
    INITIALIZED = "initialized"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class AgentState:
    topic: str
    context: str = ""
    status: AgentStatus = AgentStatus.INITIALIZED
    created_at: datetime = field(default_factory=datetime.now)
    error_log: List[str] = field(default_factory=list)
    research_data: Dict[str, Any] = field(default_factory=dict)
    final_output: Any = None

@dataclass
class ToolResult:
    success: bool
    data: Any = None
    error: str = ""

    @classmethod
    def success_result(cls, data: Any) -> 'ToolResult':
        return cls(success=True, data=data)

    @classmethod
    def error_result(cls, error: str) -> 'ToolResult':
        return cls(success=False, error=error)
tools/base_tool.py:
import logging
import sys

class BaseTool:
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.logger = self._setup_logging()

    def _setup_logging(self):
        logger = logging.getLogger(f"tools.{self.name}")
        if not logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter('%(levelname)s:%(name)s:%(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def validate_required_params(self, required_params: list, **kwargs) -> str | None:
        missing = [p for p in required_params if kwargs.get(p) is None]
        if missing:
            error_msg = f"Missing required parameters for tool '{self.name}': {', '.join(missing)}"
            self.logger.error(error_msg)
            return error_msg
        return None

    async def execute(self, **kwargs) -> 'ToolResult':
        raise NotImplementedError("Each tool must implement the execute method.")
.env.example:
GEMINI_API_KEY=your-google-gemini-api-key
REPLICATE_API_TOKEN=your-replicate-api-token
OPENAI_API_KEY=your-openai-api-key
AGENT_MODEL=gemini-1.5-flash
PLAID_TOKEN=your-plaid-token
PLAID_SECRET=your-plaid-secret
requirements.txt:
python-dotenv>=1.0.0
aiohttp>=3.9.0
PyYAML>=6.0
Implementation and Deployment Guide
1. Setup n8n:
    * Installation: Install n8n locally or on a cloud provider (e.g., DigitalOcean, Render) or an Android phone with Termux (Reddit: “0.4W power consumption”).
    * Workflow Import: Copy the JSON workflows into n8n’s editor.
    * Credentials: Configure Buffer, Google Sheets, Plaid, Notion, and SMTP credentials in n8n’s UI.
    * Endpoints: Replace http://python-agent-endpoint with MPAF’s serverless URLs (e.g., AWS Lambda API Gateway).
2. Setup MPAF:
    * Directory Structure: /your-agent-project/
    * ├── config/
    * │   └── settings.py
    * ├── src/
    * │   └── models.py
    * ├── tools/
    * │   ├── base_tool.py
    * │   ├── content_generator_tool.py
    * │   ├── image_creator_tool.py
    * │   ├── transaction_categorizer_tool.py
    * │   ├── budget_analyzer_tool.py
    * │   ├── reddit_scraper_tool.py
    * │   └── idea_generator_tool.py
    * ├── agent.py
    * ├── .env
    * ├── .env.example
    * └── requirements.txt
    * 
    * Dependencies: Run pip install -r requirements.txt.
    * Environment: Copy .env.example to .env, add API keys.
    * Serverless Deployment: Deploy agent.py and tools to AWS Lambda or Google Cloud Functions. Expose endpoints (e.g., /content, /categorize, /scrape) via API Gateway. Ensure CORS is enabled.
3. Testing:
    * Run n8n workflows in test mode to verify MPAF calls.
    * Test MPAF endpoints using tools like Postman with sample JSON payloads.
    * Monitor logs in n8n and MPAF for errors.
4. Extending:
    * n8n: Add IF nodes for error handling or new integrations (e.g., Slack notifications).
    * MPAF: Create new tools by copying tools/base_tool.py and modifying logic.
Tips for Success
* Start Simple: Begin with the Social Media Automator to understand n8n-MPAF interaction.
* Use n8n Templates: Explore n8n’s template library for similar workflows.
* Learn JavaScript: Basic JavaScript knowledge enhances n8n’s Function node capabilities.
* Engage Community: Join r/n8n or MPAF forums for support (Reddit: “Engage the Community”).
* Monitor Costs: Use free tiers for cloud providers or self-host n8n on low-cost devices.
Conclusion
The n8n-MPAF hybrid approach delivers scalable, user-friendly automation by combining n8n’s orchestration with MPAF’s processing power. Each application—Social Media Content Automator, Personal Finance Tracker, and Startup Idea Generator—relies on both n8n and MPAF working together: n8n manages workflows and integrations, while MPAF handles complex tasks like AI generation and data analysis. Complete code, clear interaction flows, and deployment guidance ensure you can implement these solutions confidently. By leveraging n8n’s no-code interface and MPAF’s modular tools, you can build transformative automation systems tailored to your needs.
Current Date and Time: August 1, 2025, 12:06 PM EDT
