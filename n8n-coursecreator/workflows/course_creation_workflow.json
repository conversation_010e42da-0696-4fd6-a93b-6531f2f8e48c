{"name": "Course Creation Workflow", "nodes": [{"parameters": {"path": "course-creation", "httpMethod": "POST", "responseMode": "responseNode"}, "name": "Course Creation Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 300], "id": "course-trigger"}, {"parameters": {"values": {"string": [{"name": "course_title", "value": "={{$json.course_title || 'Untitled Course'}}"}, {"name": "style", "value": "={{$json.style || 'professional'}}"}, {"name": "tone", "value": "={{$json.tone || 'engaging'}}"}, {"name": "depth", "value": "={{$json.depth || 'intermediate'}}"}, {"name": "target_audience", "value": "={{$json.target_audience || 'general audience'}}"}, {"name": "duration", "value": "={{$json.duration || '4-6 hours'}}"}]}}, "name": "Extract Course Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [300, 300], "id": "extract-params"}, {"parameters": {"url": "={{$env.MPAF_ENDPOINT}}/course/outline", "options": {"bodyContentType": "json", "body": {"course_title": "={{$node['Extract Course Parameters'].json['course_title']}}", "style": "={{$node['Extract Course Parameters'].json['style']}}", "tone": "={{$node['Extract Course Parameters'].json['tone']}}", "depth": "={{$node['Extract Course Parameters'].json['depth']}}", "target_audience": "={{$node['Extract Course Parameters'].json['target_audience']}}", "duration": "={{$node['Extract Course Parameters'].json['duration']}}"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{$env.MPAF_API_KEY}}"}}}, "name": "Generate Course Outline", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [500, 300], "id": "generate-outline"}, {"parameters": {"operation": "append", "sheetId": "{{$env.GOOGLE_SHEET_ID}}", "range": "Course_Outlines!A:F", "values": ["={{$node['Generate Course Outline'].json['outline']['title']}}", "={{$node['Generate Course Outline'].json['outline']['description']}}", "={{$node['Generate Course Outline'].json['summary']['modules']}}", "={{$node['Generate Course Outline'].json['summary']['total_lessons']}}", "={{$node['Generate Course Outline'].json['summary']['estimated_duration']}}", "={{new Date().toISOString()}}"]}, "name": "Log Outline to Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [700, 200], "credentials": {"googleSheetsApi": "Google Sheets Credentials"}, "id": "log-outline"}, {"parameters": {"sendTo": "={{$json.user_email || $env.DEFAULT_USER_EMAIL}}", "subject": "Course Outline Ready for Review: {{$node['Generate Course Outline'].json['outline']['title']}}", "message": "Your course outline has been generated and is ready for review.\n\nCourse: {{$node['Generate Course Outline'].json['outline']['title']}}\nModules: {{$node['Generate Course Outline'].json['summary']['modules']}}\nLessons: {{$node['Generate Course Outline'].json['summary']['total_lessons']}}\n\nPlease review the outline and reply with 'APPROVED' to continue with script generation, or provide feedback for revisions.\n\nOutline Details:\n{{JSON.stringify($node['Generate Course Outline'].json['outline'], null, 2)}}"}, "name": "Send Outline for Review", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [700, 300], "credentials": {"smtp": "SMTP Credentials"}, "id": "send-review"}, {"parameters": {"amount": 1, "unit": "hours"}, "name": "Wait for Approval", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [900, 300], "id": "wait-approval"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.approval_status || 'pending'}}", "operation": "equal", "value2": "approved"}]}}, "name": "<PERSON> App<PERSON>al", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1100, 300], "id": "check-approval"}, {"parameters": {"url": "={{$env.MPAF_ENDPOINT}}/course/scripts", "options": {"bodyContentType": "json", "body": {"outline": "={{$node['Generate Course Outline'].json['outline']}}", "style": "={{$node['Extract Course Parameters'].json['style']}}", "tone": "={{$node['Extract Course Parameters'].json['tone']}}", "depth": "={{$node['Extract Course Parameters'].json['depth']}}"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{$env.MPAF_API_KEY}}"}}}, "name": "Generate Scripts", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1300, 250], "id": "generate-scripts"}, {"parameters": {"url": "={{$env.MPAF_ENDPOINT}}/course/videos", "options": {"bodyContentType": "json", "body": {"scripts": "={{$node['Generate Scripts'].json['scripts']}}", "avatar_id": "={{$json.avatar_id || $env.DEFAULT_AVATAR_ID}}", "voice_id": "={{$json.voice_id || $env.DEFAULT_VOICE_ID}}", "video_quality": "={{$json.video_quality || '720p'}}"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{$env.MPAF_API_KEY}}"}}}, "name": "Create Videos", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1500, 250], "id": "create-videos"}, {"parameters": {"url": "={{$env.MPAF_ENDPOINT}}/course/qa", "options": {"bodyContentType": "json", "body": {"outline": "={{$node['Generate Course Outline'].json['outline']}}", "scripts": "={{$node['Generate Scripts'].json['scripts']}}", "qa_count": "={{$json.qa_count || 5}}", "include_audio": "={{$json.include_audio || true}}", "include_video": "={{$json.include_video || false}}"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{$env.MPAF_API_KEY}}"}}}, "name": "Generate Q&A", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1700, 250], "id": "generate-qa"}, {"parameters": {"values": {"string": [{"name": "course_package", "value": "={{JSON.stringify({\n  course_title: $node['Generate Course Outline'].json['outline']['title'],\n  outline: $node['Generate Course Outline'].json['outline'],\n  scripts: $node['Generate Scripts'].json['scripts'],\n  videos: $node['Create Videos'].json['videos'],\n  qa_content: $node['Generate Q&A'].json['qa_content'],\n  created_at: new Date().toISOString()\n}, null, 2)}}"}]}}, "name": "Package Course Materials", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1900, 250], "id": "package-materials"}, {"parameters": {"operation": "upload", "fileName": "={{$node['Generate Course Outline'].json['outline']['title'].replace(/[^a-zA-Z0-9]/g, '_')}}_course_package.json", "fileContent": "={{$node['Package Course Materials'].json['course_package']}}", "folderId": "={{$env.GOOGLE_DRIVE_FOLDER_ID}}"}, "name": "Upload to Google Drive", "type": "n8n-nodes-base.googleDrive", "typeVersion": 1, "position": [2100, 250], "credentials": {"googleDriveApi": "Google Drive Credentials"}, "id": "upload-drive"}, {"parameters": {"sendTo": "={{$json.user_email || $env.DEFAULT_USER_EMAIL}}", "subject": "Course Creation Complete: {{$node['Generate Course Outline'].json['outline']['title']}}", "message": "Your course has been successfully created!\n\nCourse: {{$node['Generate Course Outline'].json['outline']['title']}}\nModules: {{$node['Generate Course Outline'].json['summary']['modules']}}\nScripts Generated: {{$node['Generate Scripts'].json['summary']['total_scripts']}}\nVideos Created: {{$node['Create Videos'].json['summary']['videos_created']}}\nQ&A Pairs: {{$node['Generate Q&A'].json['summary']['total_qa_pairs']}}\n\nDownload Link: {{$node['Upload to Google Drive'].json['webViewLink']}}\n\nAll course materials have been packaged and are ready for use."}, "name": "Send Completion Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [2300, 250], "credentials": {"smtp": "SMTP Credentials"}, "id": "send-completion"}, {"parameters": {"sendTo": "={{$json.user_email || $env.DEFAULT_USER_EMAIL}}", "subject": "Course Outline Needs Revision: {{$node['Generate Course Outline'].json['outline']['title']}}", "message": "The course outline requires revision based on your feedback. Please provide specific feedback and we'll generate a new outline.\n\nOriginal Course: {{$node['Generate Course Outline'].json['outline']['title']}}\n\nTo request revisions, please reply with your feedback or contact support."}, "name": "Request Revision", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [1300, 350], "credentials": {"smtp": "SMTP Credentials"}, "id": "request-revision"}, {"parameters": {"respondWith": "json", "responseBody": "={{JSON.stringify({\n  status: 'success',\n  course_title: $node['Generate Course Outline'].json['outline']['title'],\n  download_link: $node['Upload to Google Drive'].json['webViewLink'],\n  summary: {\n    modules: $node['Generate Course Outline'].json['summary']['modules'],\n    scripts: $node['Generate Scripts'].json['summary']['total_scripts'],\n    videos: $node['Create Videos'].json['summary']['videos_created'],\n    qa_pairs: $node['Generate Q&A'].json['summary']['total_qa_pairs']\n  }\n}, null, 2)}}"}, "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2500, 250], "id": "success-response"}], "connections": {"Course Creation Trigger": {"main": [[{"node": "Extract Course Parameters", "type": "main", "index": 0}]]}, "Extract Course Parameters": {"main": [[{"node": "Generate Course Outline", "type": "main", "index": 0}]]}, "Generate Course Outline": {"main": [[{"node": "Log Outline to Sheets", "type": "main", "index": 0}, {"node": "Send Outline for Review", "type": "main", "index": 0}]]}, "Send Outline for Review": {"main": [[{"node": "Wait for Approval", "type": "main", "index": 0}]]}, "Wait for Approval": {"main": [[{"node": "<PERSON> App<PERSON>al", "type": "main", "index": 0}]]}, "Check Approval": {"main": [[{"node": "Generate Scripts", "type": "main", "index": 0}], [{"node": "Request Revision", "type": "main", "index": 0}]]}, "Generate Scripts": {"main": [[{"node": "Create Videos", "type": "main", "index": 0}]]}, "Create Videos": {"main": [[{"node": "Generate Q&A", "type": "main", "index": 0}]]}, "Generate Q&A": {"main": [[{"node": "Package Course Materials", "type": "main", "index": 0}]]}, "Package Course Materials": {"main": [[{"node": "Upload to Google Drive", "type": "main", "index": 0}]]}, "Upload to Google Drive": {"main": [[{"node": "Send Completion Notification", "type": "main", "index": 0}]]}, "Send Completion Notification": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-04T08:39:00.000Z", "updatedAt": "2025-01-04T08:39:00.000Z", "id": "course-creation", "name": "Course Creation"}], "triggerCount": 1, "updatedAt": "2025-01-04T08:39:00.000Z", "versionId": "1"}