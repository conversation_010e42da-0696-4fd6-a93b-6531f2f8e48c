{"name": "Book Generation Workflow", "nodes": [{"parameters": {"path": "book-generation", "httpMethod": "POST", "responseMode": "responseNode"}, "name": "Book Generation Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 300], "id": "book-trigger"}, {"parameters": {"values": {"string": [{"name": "book_title", "value": "={{$json.book_title || 'Untitled Book'}}"}, {"name": "style", "value": "={{$json.style || 'academic'}}"}, {"name": "citation_style", "value": "={{$json.citation_style || 'APA'}}"}, {"name": "target_audience", "value": "={{$json.target_audience || 'general readers'}}"}, {"name": "estimated_pages", "value": "={{$json.estimated_pages || 200}}"}, {"name": "research_data_path", "value": "={{$json.research_data_path || ''}}"}]}}, "name": "Extract Book Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [300, 300], "id": "extract-params"}, {"parameters": {"url": "={{$env.MPAF_ENDPOINT}}/book/research", "options": {"bodyContentType": "json", "body": {"research_data": "={{$node['Extract Book Parameters'].json['research_data_path'] || $json.research_data}}", "book_title": "={{$node['Extract Book Parameters'].json['book_title']}}"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{$env.MPAF_API_KEY}}"}}}, "name": "Process Research Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [500, 300], "id": "process-research"}, {"parameters": {"url": "={{$env.MPAF_ENDPOINT}}/book/outline", "options": {"bodyContentType": "json", "body": {"book_title": "={{$node['Extract Book Parameters'].json['book_title']}}", "style": "={{$node['Extract Book Parameters'].json['style']}}", "citation_style": "={{$node['Extract Book Parameters'].json['citation_style']}}", "target_audience": "={{$node['Extract Book Parameters'].json['target_audience']}}", "estimated_pages": "={{$node['Extract Book Parameters'].json['estimated_pages']}}", "research_analysis": "={{$node['Process Research Data'].json['analysis']}}", "processed_research": "={{$node['Process Research Data'].json['processed_data']}}"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{$env.MPAF_API_KEY}}"}}}, "name": "Generate Book Outline", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [700, 300], "id": "generate-outline"}, {"parameters": {"operation": "append", "sheetId": "{{$env.GOOGLE_SHEET_ID}}", "range": "Book_Outlines!A:G", "values": ["={{$node['Generate Book Outline'].json['outline']['title']}}", "={{$node['Generate Book Outline'].json['outline']['subtitle']}}", "={{$node['Generate Book Outline'].json['outline']['description']}}", "={{$node['Generate Book Outline'].json['summary']['chapters']}}", "={{$node['Generate Book Outline'].json['summary']['estimated_pages']}}", "={{$node['Process Research Data'].json['summary']['sources']}}", "={{new Date().toISOString()}}"]}, "name": "Log Outline to Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [900, 200], "credentials": {"googleSheetsApi": "Google Sheets Credentials"}, "id": "log-outline"}, {"parameters": {"sendTo": "={{$json.user_email || $env.DEFAULT_USER_EMAIL}}", "subject": "Book Outline Ready for Review: {{$node['Generate Book Outline'].json['outline']['title']}}", "message": "Your book outline has been generated based on the research data and is ready for review.\n\nBook: {{$node['Generate Book Outline'].json['outline']['title']}}\nSubtitle: {{$node['Generate Book Outline'].json['outline']['subtitle']}}\nChapters: {{$node['Generate Book Outline'].json['summary']['chapters']}}\nEstimated Pages: {{$node['Generate Book Outline'].json['summary']['estimated_pages']}}\nResearch Sources: {{$node['Process Research Data'].json['summary']['sources']}}\n\nMain Argument: {{$node['Generate Book Outline'].json['outline']['main_argument']}}\n\nPlease review the outline and reply with 'APPROVED' to continue with chapter generation, or provide feedback for revisions.\n\nOutline Details:\n{{JSON.stringify($node['Generate Book Outline'].json['outline'], null, 2)}}"}, "name": "Send Outline for Review", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 300], "credentials": {"smtp": "SMTP Credentials"}, "id": "send-review"}, {"parameters": {"amount": 2, "unit": "hours"}, "name": "Wait for Approval", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1100, 300], "id": "wait-approval"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.approval_status || 'pending'}}", "operation": "equal", "value2": "approved"}]}}, "name": "<PERSON> App<PERSON>al", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1300, 300], "id": "check-approval"}, {"parameters": {"url": "={{$env.MPAF_ENDPOINT}}/book/chapters", "options": {"bodyContentType": "json", "body": {"outline": "={{$node['Generate Book Outline'].json['outline']}}", "style": "={{$node['Extract Book Parameters'].json['style']}}", "citation_style": "={{$node['Extract Book Parameters'].json['citation_style']}}", "include_citations": true, "research_data": "={{$node['Process Research Data'].json}}"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{$env.MPAF_API_KEY}}"}, "timeout": 1800}}, "name": "Generate Chapters", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1500, 250], "id": "generate-chapters"}, {"parameters": {"url": "={{$env.MPAF_ENDPOINT}}/book/bibliography", "options": {"bodyContentType": "json", "body": {"citation_style": "={{$node['Extract Book Parameters'].json['citation_style']}}", "include_annotations": "={{$json.include_annotations || false}}", "research_data": "={{$node['Process Research Data'].json}}", "chapters": "={{$node['Generate Chapters'].json['chapters']}}"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{$env.MPAF_API_KEY}}"}}}, "name": "Compile Bibliography", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1700, 250], "id": "compile-bibliography"}, {"parameters": {"values": {"string": [{"name": "manuscript_content", "value": "={{[\n  '# ' + $node['Generate Book Outline'].json['outline']['title'],\n  $node['Generate Book Outline'].json['outline']['subtitle'] ? '## ' + $node['Generate Book Outline'].json['outline']['subtitle'] : '',\n  '',\n  '## Table of Contents',\n  $node['Generate Book Outline'].json['outline']['chapters'].map((ch, i) => `${i+1}. ${ch.title}`).join('\\n'),\n  '',\n  '## About This Book',\n  $node['Generate Book Outline'].json['outline']['description'],\n  '',\n  '---',\n  '',\n  $node['Generate Chapters'].json['chapters'].map(ch => ch.content).join('\\n\\n---\\n\\n'),\n  '',\n  '## Bibliography',\n  $node['Compile Bibliography'].json['bibliography']['formatted_bibliography']\n].filter(Boolean).join('\\n')}}"}, {"name": "book_metadata", "value": "={{JSON.stringify({\n  title: $node['Generate Book Outline'].json['outline']['title'],\n  subtitle: $node['Generate Book Outline'].json['outline']['subtitle'],\n  author: $json.author || 'Generated Author',\n  chapters: $node['Generate Book Outline'].json['summary']['chapters'],\n  estimated_pages: $node['Generate Book Outline'].json['summary']['estimated_pages'],\n  word_count: $node['Generate Chapters'].json['summary']['total_word_count'],\n  citations: $node['Compile Bibliography'].json['summary']['total_citations'],\n  research_sources: $node['Process Research Data'].json['summary']['sources'],\n  created_at: new Date().toISOString()\n}, null, 2)}}"}]}}, "name": "Assemble Manuscript", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1900, 250], "id": "assemble-manuscript"}, {"parameters": {"operation": "upload", "fileName": "={{$node['Generate Book Outline'].json['outline']['title'].replace(/[^a-zA-Z0-9]/g, '_')}}_manuscript.md", "fileContent": "={{$node['Assemble Manuscript'].json['manuscript_content']}}", "folderId": "={{$env.GOOGLE_DRIVE_FOLDER_ID}}"}, "name": "Upload Manuscript", "type": "n8n-nodes-base.googleDrive", "typeVersion": 1, "position": [2100, 200], "credentials": {"googleDriveApi": "Google Drive Credentials"}, "id": "upload-manuscript"}, {"parameters": {"operation": "upload", "fileName": "={{$node['Generate Book Outline'].json['outline']['title'].replace(/[^a-zA-Z0-9]/g, '_')}}_metadata.json", "fileContent": "={{$node['Assemble Manuscript'].json['book_metadata']}}", "folderId": "={{$env.GOOGLE_DRIVE_FOLDER_ID}}"}, "name": "Upload Metadata", "type": "n8n-nodes-base.googleDrive", "typeVersion": 1, "position": [2100, 300], "credentials": {"googleDriveApi": "Google Drive Credentials"}, "id": "upload-metadata"}, {"parameters": {"sendTo": "={{$json.user_email || $env.DEFAULT_USER_EMAIL}}", "subject": "Book Generation Complete: {{$node['Generate Book Outline'].json['outline']['title']}}", "message": "Your book has been successfully generated!\n\nBook: {{$node['Generate Book Outline'].json['outline']['title']}}\nSubtitle: {{$node['Generate Book Outline'].json['outline']['subtitle']}}\nChapters: {{$node['Generate Book Outline'].json['summary']['chapters']}}\nTotal Words: {{$node['Generate Chapters'].json['summary']['total_word_count']}}\nEstimated Pages: {{$node['Generate Chapters'].json['summary']['total_estimated_pages']}}\nCitations: {{$node['Compile Bibliography'].json['summary']['total_citations']}}\n\nManuscript Download: {{$node['Upload Manuscript'].json['webViewLink']}}\nMetadata Download: {{$node['Upload Metadata'].json['webViewLink']}}\n\nYour complete book manuscript with bibliography is ready for review and publication."}, "name": "Send Completion Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [2300, 250], "credentials": {"smtp": "SMTP Credentials"}, "id": "send-completion"}, {"parameters": {"sendTo": "={{$json.user_email || $env.DEFAULT_USER_EMAIL}}", "subject": "Book Outline Needs Revision: {{$node['Generate Book Outline'].json['outline']['title']}}", "message": "The book outline requires revision based on your feedback. Please provide specific feedback and we'll generate a new outline.\n\nOriginal Book: {{$node['Generate Book Outline'].json['outline']['title']}}\nMain Argument: {{$node['Generate Book Outline'].json['outline']['main_argument']}}\n\nTo request revisions, please reply with your feedback or contact support."}, "name": "Request Revision", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [1500, 350], "credentials": {"smtp": "SMTP Credentials"}, "id": "request-revision"}, {"parameters": {"respondWith": "json", "responseBody": "={{JSON.stringify({\n  status: 'success',\n  book_title: $node['Generate Book Outline'].json['outline']['title'],\n  manuscript_link: $node['Upload Manuscript'].json['webViewLink'],\n  metadata_link: $node['Upload Metadata'].json['webViewLink'],\n  summary: {\n    chapters: $node['Generate Book Outline'].json['summary']['chapters'],\n    word_count: $node['Generate Chapters'].json['summary']['total_word_count'],\n    estimated_pages: $node['Generate Chapters'].json['summary']['total_estimated_pages'],\n    citations: $node['Compile Bibliography'].json['summary']['total_citations'],\n    research_sources: $node['Process Research Data'].json['summary']['sources']\n  }\n}, null, 2)}}"}, "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2500, 250], "id": "success-response"}], "connections": {"Book Generation Trigger": {"main": [[{"node": "Extract Book Parameters", "type": "main", "index": 0}]]}, "Extract Book Parameters": {"main": [[{"node": "Process Research Data", "type": "main", "index": 0}]]}, "Process Research Data": {"main": [[{"node": "Generate Book Outline", "type": "main", "index": 0}]]}, "Generate Book Outline": {"main": [[{"node": "Log Outline to Sheets", "type": "main", "index": 0}, {"node": "Send Outline for Review", "type": "main", "index": 0}]]}, "Send Outline for Review": {"main": [[{"node": "Wait for Approval", "type": "main", "index": 0}]]}, "Wait for Approval": {"main": [[{"node": "<PERSON> App<PERSON>al", "type": "main", "index": 0}]]}, "Check Approval": {"main": [[{"node": "Generate Chapters", "type": "main", "index": 0}], [{"node": "Request Revision", "type": "main", "index": 0}]]}, "Generate Chapters": {"main": [[{"node": "Compile Bibliography", "type": "main", "index": 0}]]}, "Compile Bibliography": {"main": [[{"node": "Assemble Manuscript", "type": "main", "index": 0}]]}, "Assemble Manuscript": {"main": [[{"node": "Upload Manuscript", "type": "main", "index": 0}, {"node": "Upload Metadata", "type": "main", "index": 0}]]}, "Upload Manuscript": {"main": [[{"node": "Send Completion Notification", "type": "main", "index": 0}]]}, "Send Completion Notification": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-04T08:39:00.000Z", "updatedAt": "2025-01-04T08:39:00.000Z", "id": "book-generation", "name": "Book Generation"}], "triggerCount": 1, "updatedAt": "2025-01-04T08:39:00.000Z", "versionId": "1"}