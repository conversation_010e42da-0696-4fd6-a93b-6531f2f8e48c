# Deployment Guide

This guide covers deploying the n8n-MPAF Course Creator & Book Generator in various environments.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   n8n Workflows │    │  MPAF API Server │    │  External APIs  │
│                 │    │                 │    │                 │
│ • Course Flow   │◄──►│ • FastAPI       │◄──►│ • Claude/OpenAI │
│ • Book Flow     │    │ • Background    │    │ • HeyGen        │
│ • Triggers      │    │   Tasks         │    │ • ElevenLabs    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Google Services │    │  File Storage   │    │   Monitoring    │
│                 │    │                 │    │                 │
│ • Drive         │    │ • Local Files   │    │ • Logs          │
│ • Sheets        │    │ • AWS S3        │    │ • Metrics       │
│ • Gmail         │    │ • Google Drive  │    │ • Health Checks │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Deployment Options

### Option 1: Local Development
```bash
# Clone and setup
git clone <repository-url>
cd n8n-coursecreator
pip install -r requirements.txt
cp .env.example .env
# Edit .env with your API keys
python api_server.py
```

### Option 2: Docker Deployment
```bash
# Build and run
docker build -t n8n-mpaf-app .
docker run -p 8000:8000 --env-file .env n8n-mpaf-app
```

### Option 3: Cloud Deployment
- Deploy to AWS, GCP, or Azure
- Use container services or serverless functions
- Configure environment variables
- Set up monitoring and logging

## 📋 Prerequisites Checklist

### Required
- [ ] Python 3.8+ environment
- [ ] At least one AI API key (Claude, OpenAI, or Gemini)
- [ ] Network access to AI APIs

### Optional (for full functionality)
- [ ] HeyGen API key (video generation)
- [ ] ElevenLabs API key (audio generation)
- [ ] Google Cloud credentials (Drive/Sheets integration)
- [ ] AWS credentials (S3 storage)
- [ ] SMTP server (email notifications)

### For n8n Integration
- [ ] n8n instance (cloud or self-hosted)
- [ ] Webhook endpoints accessible from n8n
- [ ] Google Workspace credentials (for Sheets/Drive)

## 🔧 Environment Configuration

### 1. API Keys Setup

#### Claude API (Recommended)
1. Sign up at https://console.anthropic.com/
2. Create an API key
3. Add to `.env`: `CLAUDE_API_KEY=your_key`

#### OpenAI API
1. Sign up at https://platform.openai.com/
2. Create an API key
3. Add to `.env`: `OPENAI_API_KEY=your_key`

#### Google Gemini API
1. Go to https://makersuite.google.com/
2. Create an API key
3. Add to `.env`: `GEMINI_API_KEY=your_key`

### 2. Video Generation Setup (Optional)

#### HeyGen API
1. Sign up at https://heygen.com/
2. Get API key from dashboard
3. Add to `.env`: `HEYGEN_API_KEY=your_key`
4. Note available avatar and voice IDs

### 3. Audio Generation Setup (Optional)

#### ElevenLabs API
1. Sign up at https://elevenlabs.io/
2. Get API key from profile
3. Add to `.env`: `ELEVENLABS_API_KEY=your_key`

### 4. Google Services Setup (Optional)

#### Service Account Creation
1. Go to Google Cloud Console
2. Create a new project or select existing
3. Enable APIs: Drive API, Sheets API, Gmail API
4. Create service account
5. Download JSON credentials
6. Add to `.env`: `GOOGLE_CREDENTIALS_PATH=path/to/credentials.json`

#### Sharing Permissions
- Share Google Sheets with service account email
- Share Google Drive folders with service account email

## 🐳 Docker Deployment

### Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["uvicorn", "api_server:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose
```yaml
version: '3.8'

services:
  n8n-mpaf-app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - CLAUDE_API_KEY=${CLAUDE_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - HEYGEN_API_KEY=${HEYGEN_API_KEY}
      - DEBUG=false
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
```

## ☁️ Cloud Deployment

### AWS Deployment

#### Using AWS ECS
1. Create ECR repository
2. Build and push Docker image
3. Create ECS cluster and service
4. Configure environment variables
5. Set up Application Load Balancer

#### Using AWS Lambda (Serverless)
```python
# lambda_handler.py
from mangum import Mangum
from api_server import app

handler = Mangum(app)
```

### Google Cloud Deployment

#### Using Cloud Run
```bash
# Build and deploy
gcloud builds submit --tag gcr.io/PROJECT_ID/n8n-mpaf-app
gcloud run deploy --image gcr.io/PROJECT_ID/n8n-mpaf-app --platform managed
```

### Azure Deployment

#### Using Container Instances
```bash
# Deploy to Azure
az container create \
  --resource-group myResourceGroup \
  --name n8n-mpaf-app \
  --image myregistry.azurecr.io/n8n-mpaf-app:latest \
  --ports 8000 \
  --environment-variables CLAUDE_API_KEY=your_key
```

## 🔗 n8n Integration Setup

### 1. n8n Installation

#### Self-hosted n8n
```bash
# Using Docker
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -v ~/.n8n:/home/<USER>/.n8n \
  n8nio/n8n
```

#### n8n Cloud
- Sign up at https://n8n.cloud/
- Create new workflow
- Import provided JSON files

### 2. Workflow Import

1. Copy workflow JSON files:
   - `workflows/course_creation_workflow.json`
   - `workflows/book_generation_workflow.json`

2. In n8n interface:
   - Go to Workflows
   - Click "Import from File"
   - Select JSON file
   - Configure credentials

### 3. Environment Variables in n8n

Set these in n8n settings:
```
MPAF_ENDPOINT=https://your-api-server.com
MPAF_API_KEY=your_api_key
GOOGLE_SHEET_ID=your_sheet_id
GOOGLE_DRIVE_FOLDER_ID=your_folder_id
DEFAULT_USER_EMAIL=<EMAIL>
DEFAULT_AVATAR_ID=heygen_avatar_id
DEFAULT_VOICE_ID=heygen_voice_id
```

### 4. Credentials Setup

#### SMTP Credentials
- Host: your SMTP server
- Port: 587 (TLS) or 465 (SSL)
- Username: your email
- Password: your email password

#### Google Sheets API
- Upload service account JSON
- Or use OAuth2 flow

#### Google Drive API
- Same as Sheets API
- Ensure proper folder permissions

## 📊 Monitoring and Logging

### Application Monitoring

#### Health Checks
```bash
# Basic health check
curl http://your-server:8000/health

# Detailed status
curl http://your-server:8000/
```

#### Logging Configuration
```python
# In production, use structured logging
import logging
import json

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            'timestamp': self.formatTime(record),
            'level': record.levelname,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName
        }
        return json.dumps(log_entry)
```

### External Monitoring

#### Prometheus Metrics
```python
# Add to api_server.py
from prometheus_client import Counter, Histogram, generate_latest

REQUEST_COUNT = Counter('requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('request_duration_seconds', 'Request duration')

@app.middleware("http")
async def metrics_middleware(request, call_next):
    start_time = time.time()
    response = await call_next(request)
    REQUEST_COUNT.labels(request.method, request.url.path).inc()
    REQUEST_DURATION.observe(time.time() - start_time)
    return response

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

## 🔒 Security Considerations

### API Security
1. Use HTTPS in production
2. Implement API key authentication
3. Rate limiting
4. Input validation
5. CORS configuration

### Environment Security
1. Never commit `.env` files
2. Use secrets management (AWS Secrets Manager, etc.)
3. Rotate API keys regularly
4. Monitor API usage

### Network Security
1. Use VPC/private networks
2. Firewall rules
3. Load balancer security groups
4. SSL/TLS certificates

## 🚨 Troubleshooting

### Common Issues

#### API Key Errors
```bash
# Test API keys
python -c "
from config.settings import settings
print('AI API configured:', settings.validate_required_settings())
"
```

#### Connection Issues
```bash
# Test connectivity
curl -v http://your-server:8000/health
```

#### Memory Issues
- Increase container memory limits
- Optimize batch processing
- Implement request queuing

#### Rate Limiting
- Implement exponential backoff
- Monitor API usage
- Use multiple API keys if needed

### Debug Mode
```bash
# Enable debug logging
export DEBUG=true
python api_server.py
```

### Log Analysis
```bash
# View recent logs
tail -f logs/application.log

# Search for errors
grep ERROR logs/application.log
```

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancer
- Stateless application design
- Shared storage for files
- Redis for task queue

### Vertical Scaling
- Increase CPU/memory
- Optimize AI model selection
- Batch processing
- Async operations

### Performance Optimization
- Cache frequently used data
- Optimize AI prompts
- Parallel processing
- Connection pooling

## 🔄 Maintenance

### Regular Tasks
1. Monitor API usage and costs
2. Update dependencies
3. Rotate API keys
4. Clean up temporary files
5. Review logs for errors

### Backup Strategy
1. Environment configuration
2. Generated content
3. User data
4. Application logs

### Updates
1. Test in staging environment
2. Backup current deployment
3. Deploy with zero downtime
4. Monitor for issues
5. Rollback if needed

## 📞 Support

For deployment issues:
1. Check logs for error details
2. Verify environment configuration
3. Test API endpoints individually
4. Review network connectivity
5. Check API key permissions and quotas
