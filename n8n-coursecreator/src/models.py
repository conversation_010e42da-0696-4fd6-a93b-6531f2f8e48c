from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from enum import Enum
from datetime import datetime
import json

class AgentStatus(Enum):
    INITIALIZED = "initialized"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"

class ContentType(Enum):
    COURSE = "course"
    BOOK = "book"
    OUTLINE = "outline"
    SCRIPT = "script"
    CHAPTER = "chapter"
    QA = "qa"
    VIDEO = "video"
    AUDIO = "audio"
    REFERENCE = "reference"

class ProcessingStage(Enum):
    INITIALIZATION = "initialization"
    OUTLINE_GENERATION = "outline_generation"
    OUTLINE_REVIEW = "outline_review"
    CONTENT_GENERATION = "content_generation"
    CONTENT_REVIEW = "content_review"
    MULTIMEDIA_GENERATION = "multimedia_generation"
    COMPILATION = "compilation"
    FINALIZATION = "finalization"

@dataclass
class ToolResult:
    success: bool
    data: Any = None
    error: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def success_result(cls, data: Any, metadata: Dict[str, Any] = None) -> 'ToolResult':
        return cls(success=True, data=data, metadata=metadata or {})
    
    @classmethod
    def error_result(cls, error: str, metadata: Dict[str, Any] = None) -> 'ToolResult':
        return cls(success=False, error=error, metadata=metadata or {})

@dataclass
class ContentItem:
    id: str
    type: ContentType
    title: str
    content: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "type": self.type.value,
            "title": self.title,
            "content": self.content,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

@dataclass
class CourseOutline:
    title: str
    description: str
    learning_objectives: List[str]
    target_audience: str
    modules: List[Dict[str, Any]]
    estimated_duration: str = ""
    prerequisites: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "title": self.title,
            "description": self.description,
            "learning_objectives": self.learning_objectives,
            "target_audience": self.target_audience,
            "modules": self.modules,
            "estimated_duration": self.estimated_duration,
            "prerequisites": self.prerequisites
        }

@dataclass
class BookOutline:
    title: str
    subtitle: str = ""
    description: str = ""
    main_argument: str = ""
    target_audience: str = ""
    chapters: List[Dict[str, Any]] = field(default_factory=list)
    estimated_pages: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "title": self.title,
            "subtitle": self.subtitle,
            "description": self.description,
            "main_argument": self.main_argument,
            "target_audience": self.target_audience,
            "chapters": self.chapters,
            "estimated_pages": self.estimated_pages
        }

@dataclass
class AgentState:
    topic: str
    content_type: ContentType
    context: str = ""
    status: AgentStatus = AgentStatus.INITIALIZED
    stage: ProcessingStage = ProcessingStage.INITIALIZATION
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    error_log: List[str] = field(default_factory=list)
    research_data: Dict[str, Any] = field(default_factory=dict)
    content_items: List[ContentItem] = field(default_factory=list)
    outline: Optional[Any] = None  # CourseOutline or BookOutline
    final_output: Any = None
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    
    def add_content_item(self, item: ContentItem):
        """Add a content item to the state"""
        self.content_items.append(item)
        self.updated_at = datetime.now()
    
    def get_content_by_type(self, content_type: ContentType) -> List[ContentItem]:
        """Get all content items of a specific type"""
        return [item for item in self.content_items if item.type == content_type]
    
    def update_stage(self, stage: ProcessingStage):
        """Update the current processing stage"""
        self.stage = stage
        self.updated_at = datetime.now()
    
    def add_error(self, error: str):
        """Add an error to the error log"""
        self.error_log.append(f"{datetime.now().isoformat()}: {error}")
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary for serialization"""
        return {
            "topic": self.topic,
            "content_type": self.content_type.value,
            "context": self.context,
            "status": self.status.value,
            "stage": self.stage.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "error_log": self.error_log,
            "research_data": self.research_data,
            "content_items": [item.to_dict() for item in self.content_items],
            "outline": self.outline.to_dict() if self.outline else None,
            "final_output": self.final_output,
            "user_preferences": self.user_preferences
        }

@dataclass
class UserFeedback:
    feedback_type: str  # "approval", "revision", "rejection"
    content: str
    specific_items: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "feedback_type": self.feedback_type,
            "content": self.content,
            "specific_items": self.specific_items,
            "timestamp": self.timestamp.isoformat()
        }
