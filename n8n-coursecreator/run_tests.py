#!/usr/bin/env python3
"""
Test runner for the n8n-MPAF Course Creator & Book Generator
Runs all test suites and provides comprehensive validation
"""

import asyncio
import sys
import os
import time
import subprocess
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import settings

class TestRunner:
    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.skipped_tests = 0
        self.start_time = time.time()
    
    def print_header(self, title):
        """Print a formatted header"""
        print("\n" + "="*80)
        print(f" {title}")
        print("="*80)
    
    def print_section(self, title):
        """Print a section header"""
        print(f"\n--- {title} ---")
    
    def print_result(self, test_name, status, message=""):
        """Print test result"""
        status_symbols = {
            "PASS": "✓",
            "FAIL": "✗", 
            "SKIP": "⚠"
        }
        
        symbol = status_symbols.get(status, "?")
        print(f"{symbol} {test_name}: {status}")
        if message:
            print(f"   {message}")
        
        if status == "PASS":
            self.passed_tests += 1
        elif status == "FAIL":
            self.failed_tests += 1
        elif status == "SKIP":
            self.skipped_tests += 1
    
    def check_prerequisites(self):
        """Check if prerequisites are met"""
        self.print_section("Prerequisites Check")
        
        # Check Python version
        python_version = sys.version_info
        if python_version >= (3, 8):
            self.print_result("Python Version", "PASS", f"Python {python_version.major}.{python_version.minor}")
        else:
            self.print_result("Python Version", "FAIL", f"Python {python_version.major}.{python_version.minor} < 3.8")
            return False
        
        # Check required packages
        required_packages = [
            "fastapi", "uvicorn", "pydantic", "aiohttp", "pytest"
        ]
        
        for package in required_packages:
            try:
                __import__(package)
                self.print_result(f"Package: {package}", "PASS")
            except ImportError:
                self.print_result(f"Package: {package}", "FAIL", "Not installed")
                return False
        
        # Check environment configuration
        try:
            has_ai_key = any([
                settings.GEMINI_API_KEY,
                settings.OPENAI_API_KEY,
                settings.CLAUDE_API_KEY
            ])
            
            if has_ai_key:
                self.print_result("AI API Keys", "PASS", "At least one AI API key configured")
            else:
                self.print_result("AI API Keys", "SKIP", "No AI API keys - some tests will be skipped")
            
            # Check optional services
            if settings.HEYGEN_API_KEY:
                self.print_result("HeyGen API", "PASS", "Video generation available")
            else:
                self.print_result("HeyGen API", "SKIP", "Video generation not available")
            
            if settings.ELEVENLABS_API_KEY:
                self.print_result("ElevenLabs API", "PASS", "Audio generation available")
            else:
                self.print_result("ElevenLabs API", "SKIP", "Audio generation not available")
            
        except Exception as e:
            self.print_result("Environment Configuration", "FAIL", str(e))
            return False
        
        return True
    
    async def run_course_tools_tests(self):
        """Run course tools tests"""
        self.print_section("Course Tools Tests")
        
        try:
            from tests.test_course_tools import run_manual_tests
            await run_manual_tests()
            self.print_result("Course Tools", "PASS", "All course tools working correctly")
        except Exception as e:
            self.print_result("Course Tools", "FAIL", str(e))
    
    async def run_book_tools_tests(self):
        """Run book tools tests"""
        self.print_section("Book Tools Tests")
        
        try:
            from tests.test_book_tools import run_manual_tests
            await run_manual_tests()
            self.print_result("Book Tools", "PASS", "All book tools working correctly")
        except Exception as e:
            self.print_result("Book Tools", "FAIL", str(e))
    
    def run_api_tests(self):
        """Run API endpoint tests"""
        self.print_section("API Endpoint Tests")
        
        try:
            from tests.test_api_endpoints import run_manual_api_tests
            run_manual_api_tests()
            self.print_result("API Endpoints", "PASS", "All API endpoints working correctly")
        except Exception as e:
            self.print_result("API Endpoints", "FAIL", str(e))
    
    def run_integration_tests(self):
        """Run integration tests"""
        self.print_section("Integration Tests")
        
        # Test agent runners
        try:
            # Test course agent import
            from course_agent import CourseCreationAgent
            self.print_result("Course Agent Import", "PASS")
            
            # Test book agent import
            from book_agent import BookGenerationAgent
            self.print_result("Book Agent Import", "PASS")
            
            # Test API server import
            from api_server import app
            self.print_result("API Server Import", "PASS")
            
        except Exception as e:
            self.print_result("Integration Tests", "FAIL", str(e))
    
    def run_workflow_validation(self):
        """Validate n8n workflow files"""
        self.print_section("Workflow Validation")
        
        workflow_files = [
            "workflows/course_creation_workflow.json",
            "workflows/book_generation_workflow.json"
        ]
        
        for workflow_file in workflow_files:
            try:
                import json
                with open(workflow_file, 'r') as f:
                    workflow_data = json.load(f)
                
                # Basic validation
                required_fields = ["name", "nodes", "connections"]
                for field in required_fields:
                    if field not in workflow_data:
                        raise ValueError(f"Missing required field: {field}")
                
                # Check nodes
                if len(workflow_data["nodes"]) == 0:
                    raise ValueError("No nodes defined in workflow")
                
                self.print_result(f"Workflow: {os.path.basename(workflow_file)}", "PASS", 
                                f"{len(workflow_data['nodes'])} nodes")
                
            except FileNotFoundError:
                self.print_result(f"Workflow: {os.path.basename(workflow_file)}", "FAIL", "File not found")
            except Exception as e:
                self.print_result(f"Workflow: {os.path.basename(workflow_file)}", "FAIL", str(e))
    
    def run_pytest_suite(self):
        """Run pytest test suite if available"""
        self.print_section("Pytest Suite")
        
        try:
            # Check if pytest is available and tests exist
            test_files = list(Path("tests").glob("test_*.py"))
            if not test_files:
                self.print_result("Pytest", "SKIP", "No pytest test files found")
                return
            
            # Run pytest
            result = subprocess.run([
                sys.executable, "-m", "pytest", "tests/", "-v", "--tb=short"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.print_result("Pytest Suite", "PASS", "All pytest tests passed")
            else:
                self.print_result("Pytest Suite", "FAIL", "Some pytest tests failed")
                print("Pytest output:")
                print(result.stdout)
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            self.print_result("Pytest Suite", "FAIL", "Tests timed out")
        except Exception as e:
            self.print_result("Pytest Suite", "FAIL", str(e))
    
    def print_summary(self):
        """Print test summary"""
        total_time = time.time() - self.start_time
        total_tests = self.passed_tests + self.failed_tests + self.skipped_tests
        
        self.print_header("TEST SUMMARY")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {self.passed_tests} ✓")
        print(f"Failed: {self.failed_tests} ✗")
        print(f"Skipped: {self.skipped_tests} ⚠")
        print(f"Execution Time: {total_time:.2f} seconds")
        
        if self.failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED! The application is ready for use.")
            return True
        else:
            print(f"\n❌ {self.failed_tests} TESTS FAILED. Please review the errors above.")
            return False
    
    async def run_all_tests(self):
        """Run all test suites"""
        self.print_header("n8n-MPAF Course Creator & Book Generator - Test Suite")
        
        # Check prerequisites
        if not self.check_prerequisites():
            print("\n❌ Prerequisites not met. Please install required dependencies.")
            return False
        
        # Run test suites
        await self.run_course_tools_tests()
        await self.run_book_tools_tests()
        self.run_api_tests()
        self.run_integration_tests()
        self.run_workflow_validation()
        
        # Run pytest if available
        self.run_pytest_suite()
        
        # Print summary
        return self.print_summary()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run test suite for n8n-MPAF application")
    parser.add_argument("--quick", action="store_true", help="Run quick tests only")
    parser.add_argument("--course-only", action="store_true", help="Run course tools tests only")
    parser.add_argument("--book-only", action="store_true", help="Run book tools tests only")
    parser.add_argument("--api-only", action="store_true", help="Run API tests only")
    parser.add_argument("--pytest-only", action="store_true", help="Run pytest suite only")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    async def run_selected_tests():
        runner.print_header("n8n-MPAF Test Runner")
        
        if not runner.check_prerequisites():
            return False
        
        if args.course_only:
            await runner.run_course_tools_tests()
        elif args.book_only:
            await runner.run_book_tools_tests()
        elif args.api_only:
            runner.run_api_tests()
        elif args.pytest_only:
            runner.run_pytest_suite()
        elif args.quick:
            runner.run_integration_tests()
            runner.run_workflow_validation()
        else:
            return await runner.run_all_tests()
        
        return runner.print_summary()
    
    # Run tests
    success = asyncio.run(run_selected_tests())
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
