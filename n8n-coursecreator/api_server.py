#!/usr/bin/env python3
"""
API Server for n8n-MPAF Course Creator and Book Generator
Provides HTTP endpoints for n8n workflows to interact with MPAF tools.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

from config.settings import settings
from course_agent import CourseCreationAgent
from book_agent import BookGenerationAgent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="n8n-MPAF Course Creator & Book Generator API",
    description="API endpoints for course creation and book generation workflows",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request models
class CourseRequest(BaseModel):
    course_title: str = Field(..., description="Title of the course to create")
    style: str = Field("professional", description="Course style")
    tone: str = Field("engaging", description="Course tone")
    depth: str = Field("intermediate", description="Course depth level")
    target_audience: str = Field("general audience", description="Target audience")
    duration: str = Field("4-6 hours", description="Estimated course duration")
    context: str = Field("", description="Additional context")
    avatar_id: str = Field("default", description="Avatar ID for video generation")
    voice_id: str = Field("default", description="Voice ID for video generation")
    video_quality: str = Field("720p", description="Video quality")
    qa_count: int = Field(5, description="Number of Q&A pairs per module")
    include_audio: bool = Field(True, description="Include audio for Q&A")
    include_video: bool = Field(False, description="Include video for Q&A")

class BookRequest(BaseModel):
    book_title: str = Field(..., description="Title of the book to generate")
    research_data: Any = Field(None, description="Research data (file path, text, or structured data)")
    style: str = Field("academic", description="Book writing style")
    citation_style: str = Field("APA", description="Citation style")
    target_audience: str = Field("general readers", description="Target audience")
    estimated_pages: int = Field(200, description="Estimated book length in pages")
    context: str = Field("", description="Additional context")
    include_annotations: bool = Field(False, description="Include annotations in bibliography")

class OutlineRequest(BaseModel):
    course_title: str = Field(..., description="Course title")
    style: str = Field("professional", description="Course style")
    tone: str = Field("engaging", description="Course tone")
    depth: str = Field("intermediate", description="Course depth")
    target_audience: str = Field("general audience", description="Target audience")
    duration: str = Field("4-6 hours", description="Estimated duration")

class BookOutlineRequest(BaseModel):
    book_title: str = Field(..., description="Book title")
    research_data: Any = Field(None, description="Research data")
    style: str = Field("academic", description="Book style")
    citation_style: str = Field("APA", description="Citation style")
    target_audience: str = Field("general readers", description="Target audience")
    estimated_pages: int = Field(200, description="Estimated pages")

# Response models
class APIResponse(BaseModel):
    success: bool
    data: Any = None
    error: str = ""
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

# Global storage for background tasks (in production, use a proper database)
task_storage = {}

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "n8n-MPAF Course Creator & Book Generator API",
        "version": "1.0.0",
        "endpoints": {
            "course": {
                "outline": "/course/outline",
                "scripts": "/course/scripts", 
                "videos": "/course/videos",
                "qa": "/course/qa",
                "full": "/course/create"
            },
            "book": {
                "research": "/book/research",
                "outline": "/book/outline",
                "chapters": "/book/chapters",
                "bibliography": "/book/bibliography",
                "full": "/book/generate"
            }
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# Course Creation Endpoints

@app.post("/course/outline")
async def generate_course_outline(request: OutlineRequest):
    """Generate course outline"""
    try:
        from tools.course_outline_generator import course_outline_generator
        from src.models import AgentState, ContentType
        
        # Create temporary state
        state = AgentState(
            topic=request.course_title,
            content_type=ContentType.COURSE,
            user_preferences={
                "style": request.style,
                "tone": request.tone,
                "depth": request.depth,
                "target_audience": request.target_audience,
                "duration": request.duration
            }
        )
        
        result = await course_outline_generator.execute(
            state,
            course_title=request.course_title,
            style=request.style,
            tone=request.tone,
            depth=request.depth,
            target_audience=request.target_audience,
            duration=request.duration
        )
        
        if result.success:
            return APIResponse(success=True, data=result.data)
        else:
            raise HTTPException(status_code=500, detail=result.error)
            
    except Exception as e:
        logger.error(f"Error generating course outline: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/course/scripts")
async def generate_course_scripts(request: dict):
    """Generate course scripts from outline"""
    try:
        from tools.script_generator import script_generator
        from src.models import AgentState, ContentType, CourseOutline
        
        # Create state with outline
        state = AgentState(
            topic=request.get("outline", {}).get("title", "Course"),
            content_type=ContentType.COURSE,
            user_preferences={
                "style": request.get("style", "professional"),
                "tone": request.get("tone", "engaging"),
                "depth": request.get("depth", "intermediate")
            }
        )
        
        # Set outline
        outline_data = request.get("outline", {})
        if outline_data:
            state.outline = CourseOutline(
                title=outline_data.get("title", ""),
                description=outline_data.get("description", ""),
                learning_objectives=outline_data.get("learning_objectives", []),
                target_audience=outline_data.get("target_audience", ""),
                modules=outline_data.get("modules", [])
            )
        
        result = await script_generator.execute(state)
        
        if result.success:
            return APIResponse(success=True, data=result.data)
        else:
            raise HTTPException(status_code=500, detail=result.error)
            
    except Exception as e:
        logger.error(f"Error generating scripts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/course/videos")
async def create_course_videos(request: dict):
    """Create videos from scripts"""
    try:
        from tools.video_creator import video_creator
        from src.models import AgentState, ContentType, ContentItem
        
        # Create state with scripts
        state = AgentState(
            topic="Course Videos",
            content_type=ContentType.COURSE
        )
        
        # Add script items to state
        scripts = request.get("scripts", [])
        for script in scripts:
            if isinstance(script, dict) and "content" in script:
                content_item = ContentItem(
                    id=script.get("content_item_id", "script"),
                    type=ContentType.SCRIPT,
                    title=script.get("lesson_title", "Script"),
                    content=json.dumps(script) if isinstance(script, dict) else str(script),
                    metadata=script.get("metadata", {})
                )
                state.add_content_item(content_item)
        
        result = await video_creator.execute(
            state,
            avatar_id=request.get("avatar_id", "default"),
            voice_id=request.get("voice_id", "default"),
            video_quality=request.get("video_quality", "720p")
        )
        
        if result.success:
            return APIResponse(success=True, data=result.data)
        else:
            raise HTTPException(status_code=500, detail=result.error)
            
    except Exception as e:
        logger.error(f"Error creating videos: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/course/qa")
async def generate_course_qa(request: dict):
    """Generate Q&A content for course"""
    try:
        from tools.qa_generator import qa_generator
        from src.models import AgentState, ContentType, CourseOutline
        
        # Create state with outline
        state = AgentState(
            topic=request.get("outline", {}).get("title", "Course"),
            content_type=ContentType.COURSE
        )
        
        # Set outline
        outline_data = request.get("outline", {})
        if outline_data:
            state.outline = CourseOutline(
                title=outline_data.get("title", ""),
                description=outline_data.get("description", ""),
                learning_objectives=outline_data.get("learning_objectives", []),
                target_audience=outline_data.get("target_audience", ""),
                modules=outline_data.get("modules", [])
            )
        
        result = await qa_generator.execute(
            state,
            qa_count=request.get("qa_count", 5),
            include_audio=request.get("include_audio", True),
            include_video=request.get("include_video", False)
        )
        
        if result.success:
            return APIResponse(success=True, data=result.data)
        else:
            raise HTTPException(status_code=500, detail=result.error)
            
    except Exception as e:
        logger.error(f"Error generating Q&A: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/course/create")
async def create_full_course(request: CourseRequest, background_tasks: BackgroundTasks):
    """Create complete course (background task)"""
    try:
        task_id = f"course_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Store initial task status
        task_storage[task_id] = {
            "status": "started",
            "progress": 0,
            "message": "Course creation started",
            "created_at": datetime.now().isoformat()
        }
        
        # Start background task
        background_tasks.add_task(run_course_creation, task_id, request)
        
        return APIResponse(
            success=True,
            data={
                "task_id": task_id,
                "status": "started",
                "message": "Course creation started in background"
            }
        )
        
    except Exception as e:
        logger.error(f"Error starting course creation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Book Generation Endpoints

@app.post("/book/research")
async def process_book_research(request: dict):
    """Process research data for book generation"""
    try:
        from tools.research_processor import research_processor
        from src.models import AgentState, ContentType
        
        state = AgentState(
            topic=request.get("book_title", "Book"),
            content_type=ContentType.BOOK
        )
        
        result = await research_processor.execute(
            state,
            research_data=request.get("research_data")
        )
        
        if result.success:
            return APIResponse(success=True, data=result.data)
        else:
            raise HTTPException(status_code=500, detail=result.error)
            
    except Exception as e:
        logger.error(f"Error processing research: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/book/outline")
async def generate_book_outline(request: BookOutlineRequest):
    """Generate book outline"""
    try:
        from tools.book_outline_generator import book_outline_generator
        from src.models import AgentState, ContentType
        
        # Create state
        state = AgentState(
            topic=request.book_title,
            content_type=ContentType.BOOK,
            user_preferences={
                "style": request.style,
                "citation_style": request.citation_style,
                "target_audience": request.target_audience,
                "estimated_pages": request.estimated_pages
            }
        )
        
        # Process research data if provided
        if request.research_data:
            from tools.research_processor import research_processor
            research_result = await research_processor.execute(state, research_data=request.research_data)
            if not research_result.success:
                logger.warning(f"Research processing failed: {research_result.error}")
        
        result = await book_outline_generator.execute(
            state,
            book_title=request.book_title,
            style=request.style,
            citation_style=request.citation_style,
            target_audience=request.target_audience,
            estimated_pages=request.estimated_pages
        )
        
        if result.success:
            return APIResponse(success=True, data=result.data)
        else:
            raise HTTPException(status_code=500, detail=result.error)
            
    except Exception as e:
        logger.error(f"Error generating book outline: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/book/chapters")
async def generate_book_chapters(request: dict):
    """Generate book chapters from outline"""
    try:
        from tools.chapter_generator import chapter_generator
        from src.models import AgentState, ContentType, BookOutline
        
        # Create state with outline
        state = AgentState(
            topic=request.get("outline", {}).get("title", "Book"),
            content_type=ContentType.BOOK,
            user_preferences={
                "style": request.get("style", "academic"),
                "citation_style": request.get("citation_style", "APA")
            }
        )
        
        # Set outline
        outline_data = request.get("outline", {})
        if outline_data:
            state.outline = BookOutline(
                title=outline_data.get("title", ""),
                subtitle=outline_data.get("subtitle", ""),
                description=outline_data.get("description", ""),
                main_argument=outline_data.get("main_argument", ""),
                target_audience=outline_data.get("target_audience", ""),
                chapters=outline_data.get("chapters", []),
                estimated_pages=outline_data.get("estimated_pages", 200)
            )
        
        # Add research data if provided
        research_data = request.get("research_data", {})
        if research_data:
            state.research_data.update(research_data)
        
        result = await chapter_generator.execute(
            state,
            style=request.get("style", "academic"),
            citation_style=request.get("citation_style", "APA"),
            include_citations=request.get("include_citations", True)
        )
        
        if result.success:
            return APIResponse(success=True, data=result.data)
        else:
            raise HTTPException(status_code=500, detail=result.error)
            
    except Exception as e:
        logger.error(f"Error generating chapters: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/book/bibliography")
async def compile_book_bibliography(request: dict):
    """Compile bibliography for book"""
    try:
        from tools.bibliography_compiler import bibliography_compiler
        from src.models import AgentState, ContentType, ContentItem
        
        # Create state
        state = AgentState(
            topic="Bibliography",
            content_type=ContentType.BOOK
        )
        
        # Add research data
        research_data = request.get("research_data", {})
        if research_data:
            state.research_data.update(research_data)
        
        # Add chapter items
        chapters = request.get("chapters", [])
        for chapter in chapters:
            if isinstance(chapter, dict):
                content_item = ContentItem(
                    id=chapter.get("content_item_id", "chapter"),
                    type=ContentType.CHAPTER,
                    title=chapter.get("chapter_title", "Chapter"),
                    content=json.dumps(chapter) if isinstance(chapter, dict) else str(chapter),
                    metadata=chapter.get("metadata", {})
                )
                state.add_content_item(content_item)
        
        result = await bibliography_compiler.execute(
            state,
            citation_style=request.get("citation_style", "APA"),
            include_annotations=request.get("include_annotations", False)
        )
        
        if result.success:
            return APIResponse(success=True, data=result.data)
        else:
            raise HTTPException(status_code=500, detail=result.error)
            
    except Exception as e:
        logger.error(f"Error compiling bibliography: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/book/generate")
async def generate_full_book(request: BookRequest, background_tasks: BackgroundTasks):
    """Generate complete book (background task)"""
    try:
        task_id = f"book_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Store initial task status
        task_storage[task_id] = {
            "status": "started",
            "progress": 0,
            "message": "Book generation started",
            "created_at": datetime.now().isoformat()
        }
        
        # Start background task
        background_tasks.add_task(run_book_generation, task_id, request)
        
        return APIResponse(
            success=True,
            data={
                "task_id": task_id,
                "status": "started",
                "message": "Book generation started in background"
            }
        )
        
    except Exception as e:
        logger.error(f"Error starting book generation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Task status endpoint
@app.get("/task/{task_id}")
async def get_task_status(task_id: str):
    """Get status of background task"""
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return APIResponse(success=True, data=task_storage[task_id])

# Background task functions
async def run_course_creation(task_id: str, request: CourseRequest):
    """Background task for course creation"""
    try:
        task_storage[task_id]["status"] = "running"
        task_storage[task_id]["progress"] = 10
        task_storage[task_id]["message"] = "Creating course agent"
        
        user_preferences = {
            "style": request.style,
            "tone": request.tone,
            "depth": request.depth,
            "target_audience": request.target_audience,
            "duration": request.duration,
            "avatar_id": request.avatar_id,
            "voice_id": request.voice_id,
            "video_quality": request.video_quality,
            "qa_count": request.qa_count,
            "include_audio": request.include_audio,
            "include_video": request.include_video
        }
        
        agent = CourseCreationAgent(
            course_title=request.course_title,
            context=request.context,
            user_preferences=user_preferences,
            verbose=True
        )
        
        task_storage[task_id]["progress"] = 20
        task_storage[task_id]["message"] = "Running course creation pipeline"
        
        result = await agent.run_pipeline()
        
        task_storage[task_id]["status"] = "completed" if result["status"] == "completed" else "failed"
        task_storage[task_id]["progress"] = 100
        task_storage[task_id]["message"] = "Course creation completed"
        task_storage[task_id]["result"] = result
        task_storage[task_id]["completed_at"] = datetime.now().isoformat()
        
    except Exception as e:
        logger.error(f"Course creation task failed: {e}")
        task_storage[task_id]["status"] = "failed"
        task_storage[task_id]["error"] = str(e)
        task_storage[task_id]["completed_at"] = datetime.now().isoformat()

async def run_book_generation(task_id: str, request: BookRequest):
    """Background task for book generation"""
    try:
        task_storage[task_id]["status"] = "running"
        task_storage[task_id]["progress"] = 10
        task_storage[task_id]["message"] = "Creating book agent"
        
        user_preferences = {
            "style": request.style,
            "citation_style": request.citation_style,
            "target_audience": request.target_audience,
            "estimated_pages": request.estimated_pages,
            "include_annotations": request.include_annotations
        }
        
        agent = BookGenerationAgent(
            book_title=request.book_title,
            research_data=request.research_data,
            context=request.context,
            user_preferences=user_preferences,
            verbose=True
        )
        
        task_storage[task_id]["progress"] = 20
        task_storage[task_id]["message"] = "Running book generation pipeline"
        
        result = await agent.run_pipeline()
        
        task_storage[task_id]["status"] = "completed" if result["status"] == "completed" else "failed"
        task_storage[task_id]["progress"] = 100
        task_storage[task_id]["message"] = "Book generation completed"
        task_storage[task_id]["result"] = result
        task_storage[task_id]["completed_at"] = datetime.now().isoformat()
        
    except Exception as e:
        logger.error(f"Book generation task failed: {e}")
        task_storage[task_id]["status"] = "failed"
        task_storage[task_id]["error"] = str(e)
        task_storage[task_id]["completed_at"] = datetime.now().isoformat()

if __name__ == "__main__":
    # Validate settings
    if not settings.validate_required_settings():
        logger.error("Required settings validation failed")
        sys.exit(1)
    
    # Run server
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
