#!/usr/bin/env python3
"""
Book Generation Agent Runner
Handles book generation workflow including research processing, outline generation, chapter creation, and bibliography compilation.
"""

import asyncio
import argparse
import sys
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

from config.settings import settings
from src.models import AgentState, AgentStatus, ContentType, ProcessingStage
from tools.research_processor import research_processor
from tools.book_outline_generator import book_outline_generator
from tools.chapter_generator import chapter_generator
from tools.bibliography_compiler import bibliography_compiler

class BookGenerationAgent:
    def __init__(self, book_title: str, research_data: Any = None, context: str = "", user_preferences: Dict[str, Any] = None, verbose: bool = False):
        self.state = AgentState(
            topic=book_title,
            content_type=ContentType.BOOK,
            context=context,
            user_preferences=user_preferences or {}
        )
        self.research_data = research_data
        self.verbose = verbose
        self.logger = self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging for the agent"""
        logging.basicConfig(
            level=logging.INFO if self.verbose else logging.WARNING,
            format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
        )
        return logging.getLogger("BookGenerationAgent")
    
    async def run_pipeline(self) -> Dict[str, Any]:
        """Run the complete book generation pipeline"""
        self.state.status = AgentStatus.RUNNING
        self.logger.info(f"Starting book generation for: {self.state.topic}")
        
        try:
            # Stage 1: Process Research Data
            if self.research_data:
                await self._run_research_processing()
            else:
                self.logger.warning("No research data provided - proceeding without research analysis")
            
            # Stage 2: Generate Book Outline
            await self._run_outline_generation()
            
            # Stage 3: Generate Chapters
            await self._run_chapter_generation()
            
            # Stage 4: Compile Bibliography
            await self._run_bibliography_compilation()
            
            # Stage 5: Finalize
            await self._finalize_book()
            
            self.state.status = AgentStatus.COMPLETED
            self.logger.info("Book generation pipeline completed successfully")
            
        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.add_error(str(e))
            self.logger.error(f"Book generation pipeline failed: {e}")
        
        return self._prepare_output()
    
    async def _run_research_processing(self):
        """Process research data"""
        self.state.update_stage(ProcessingStage.INITIALIZATION)
        self.logger.info("Processing research data...")
        
        result = await research_processor.execute(
            self.state,
            research_data=self.research_data
        )
        
        if not result.success:
            raise Exception(f"Research processing failed: {result.error}")
        
        self.logger.info(f"Processed {result.data['summary']['sources']} research sources")
    
    async def _run_outline_generation(self):
        """Generate book outline"""
        self.state.update_stage(ProcessingStage.OUTLINE_GENERATION)
        self.logger.info("Generating book outline...")
        
        result = await book_outline_generator.execute(
            self.state,
            book_title=self.state.topic,
            **self.state.user_preferences
        )
        
        if not result.success:
            raise Exception(f"Outline generation failed: {result.error}")
        
        self.logger.info(f"Generated outline with {result.data['summary']['chapters']} chapters")
    
    async def _run_chapter_generation(self):
        """Generate book chapters"""
        self.state.update_stage(ProcessingStage.CONTENT_GENERATION)
        self.logger.info("Generating book chapters...")
        
        result = await chapter_generator.execute(self.state, **self.state.user_preferences)
        
        if not result.success:
            raise Exception(f"Chapter generation failed: {result.error}")
        
        self.logger.info(f"Generated {result.data['summary']['generated_chapters']} chapters")
    
    async def _run_bibliography_compilation(self):
        """Compile bibliography and references"""
        self.state.update_stage(ProcessingStage.COMPILATION)
        self.logger.info("Compiling bibliography...")
        
        result = await bibliography_compiler.execute(self.state, **self.state.user_preferences)
        
        if not result.success:
            self.logger.warning(f"Bibliography compilation failed: {result.error}")
            return
        
        self.logger.info(f"Compiled bibliography with {result.data['summary']['total_citations']} citations")
    
    async def _finalize_book(self):
        """Finalize book generation"""
        self.state.update_stage(ProcessingStage.FINALIZATION)
        self.logger.info("Finalizing book...")
        
        # Compile final output
        outline_items = self.state.get_content_by_type(ContentType.OUTLINE)
        chapter_items = self.state.get_content_by_type(ContentType.CHAPTER)
        reference_items = self.state.get_content_by_type(ContentType.REFERENCE)
        
        # Create manuscript content
        manuscript_parts = []
        
        # Title and metadata
        if self.state.outline:
            manuscript_parts.append(f"# {self.state.outline.title}")
            if self.state.outline.subtitle:
                manuscript_parts.append(f"## {self.state.outline.subtitle}")
            manuscript_parts.append("")
            manuscript_parts.append("## About This Book")
            manuscript_parts.append(self.state.outline.description)
            manuscript_parts.append("")
            
            # Table of contents
            manuscript_parts.append("## Table of Contents")
            for i, chapter in enumerate(self.state.outline.chapters, 1):
                manuscript_parts.append(f"{i}. {chapter['title']}")
            manuscript_parts.append("")
            manuscript_parts.append("---")
            manuscript_parts.append("")
        
        # Chapters
        chapter_content_items = [item for item in chapter_items if item.metadata.get("type") != "summary"]
        chapter_content_items.sort(key=lambda x: x.metadata.get("chapter_number", 0))
        
        for chapter_item in chapter_content_items:
            try:
                if chapter_item.content.startswith('{'):
                    chapter_data = json.loads(chapter_item.content)
                    manuscript_parts.append(chapter_data.get("content", chapter_item.content))
                else:
                    manuscript_parts.append(chapter_item.content)
                manuscript_parts.append("")
                manuscript_parts.append("---")
                manuscript_parts.append("")
            except:
                manuscript_parts.append(chapter_item.content)
                manuscript_parts.append("")
                manuscript_parts.append("---")
                manuscript_parts.append("")
        
        # Bibliography
        bibliography_items = [item for item in reference_items if item.title == "Bibliography"]
        if bibliography_items:
            manuscript_parts.append("## Bibliography")
            manuscript_parts.append(bibliography_items[0].content)
        
        # Additional reference materials
        ref_material_items = [item for item in reference_items if item.title == "Additional Reference Materials"]
        if ref_material_items:
            manuscript_parts.append("")
            manuscript_parts.append(ref_material_items[0].content)
        
        manuscript_content = "\n".join(manuscript_parts)
        
        self.state.final_output = {
            "book_title": self.state.outline.title if self.state.outline else self.state.topic,
            "outline": self.state.outline.to_dict() if self.state.outline else None,
            "manuscript": manuscript_content,
            "content_summary": {
                "outlines": len(outline_items),
                "chapters": len(chapter_content_items),
                "references": len(reference_items),
                "total_word_count": sum(item.metadata.get("word_count", 0) for item in chapter_content_items),
                "estimated_pages": sum(item.metadata.get("estimated_pages", 0) for item in chapter_content_items)
            },
            "content_items": [item.to_dict() for item in self.state.content_items],
            "created_at": self.state.created_at.isoformat(),
            "completed_at": datetime.now().isoformat()
        }
    
    def _prepare_output(self) -> Dict[str, Any]:
        """Prepare final output for API response"""
        return {
            "status": self.state.status.value,
            "book_title": self.state.topic,
            "outline": self.state.outline.to_dict() if self.state.outline else None,
            "final_output": self.state.final_output,
            "summary": {
                "chapters": len(self.state.outline.chapters) if self.state.outline else 0,
                "total_word_count": self.state.final_output.get("content_summary", {}).get("total_word_count", 0) if self.state.final_output else 0,
                "estimated_pages": self.state.final_output.get("content_summary", {}).get("estimated_pages", 0) if self.state.final_output else 0,
                "research_sources": len(self.state.research_data.get("processed_research", {}).get("sources", [])),
                "total_content_items": len(self.state.content_items)
            },
            "errors": self.state.error_log,
            "processing_time": (datetime.now() - self.state.created_at).total_seconds(),
            "state_data": self.state.to_dict()
        }
    
    def present_output(self):
        """Present output to console"""
        print("\n" + "="*60)
        print("BOOK GENERATION AGENT - RUN COMPLETE")
        print("="*60)
        print(f"Status: {self.state.status.value}")
        print(f"Book: {self.state.topic}")
        print(f"Processing Stage: {self.state.stage.value}")
        
        if self.state.status == AgentStatus.COMPLETED:
            print("\n--- BOOK SUMMARY ---")
            if self.state.outline:
                print(f"Title: {self.state.outline.title}")
                if self.state.outline.subtitle:
                    print(f"Subtitle: {self.state.outline.subtitle}")
                print(f"Chapters: {len(self.state.outline.chapters)}")
                print(f"Target Audience: {self.state.outline.target_audience}")
                print(f"Estimated Pages: {self.state.outline.estimated_pages}")
            
            if self.state.final_output:
                content_summary = self.state.final_output.get("content_summary", {})
                print(f"\n--- CONTENT GENERATED ---")
                print(f"Chapters Written: {content_summary.get('chapters', 0)}")
                print(f"Total Word Count: {content_summary.get('total_word_count', 0):,}")
                print(f"Estimated Pages: {content_summary.get('estimated_pages', 0)}")
                print(f"Reference Items: {content_summary.get('references', 0)}")
                print(f"Research Sources: {len(self.state.research_data.get('processed_research', {}).get('sources', []))}")
            
        else:
            print("\n--- ERRORS ---")
            for error in self.state.error_log:
                print(f"  {error}")
        
        print(f"\nProcessing Time: {(datetime.now() - self.state.created_at).total_seconds():.2f} seconds")
        print("="*60)

def create_parser():
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(description="Book Generation Agent")
    parser.add_argument("book_title", help="The title of the book to generate")
    parser.add_argument("--research-data", "-r", help="Path to research data file or directory")
    parser.add_argument("--context", "-c", default="", help="Additional context for book generation")
    parser.add_argument("--style", default="academic", help="Book style (academic, popular, technical)")
    parser.add_argument("--citation-style", default="APA", help="Citation style (APA, MLA, Chicago)")
    parser.add_argument("--target-audience", default="general readers", help="Target audience description")
    parser.add_argument("--estimated-pages", type=int, default=200, help="Estimated book length in pages")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable detailed logging")
    return parser

async def main():
    """Main entry point"""
    # Validate settings
    if not settings.validate_required_settings():
        sys.exit(1)
    
    parser = create_parser()
    args = parser.parse_args()
    
    # Prepare user preferences
    user_preferences = {
        "style": args.style,
        "citation_style": args.citation_style,
        "target_audience": args.target_audience,
        "estimated_pages": args.estimated_pages
    }
    
    # Create and run agent
    agent = BookGenerationAgent(
        book_title=args.book_title,
        research_data=args.research_data,
        context=args.context,
        user_preferences=user_preferences,
        verbose=args.verbose
    )
    
    result = await agent.run_pipeline()
    agent.present_output()
    
    # Return appropriate exit code
    sys.exit(0 if result["status"] == "completed" else 1)

if __name__ == "__main__":
    asyncio.run(main())
