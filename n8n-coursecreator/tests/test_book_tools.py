#!/usr/bin/env python3
"""
Test suite for book generation tools
"""

import asyncio
import pytest
import json
import sys
import os
import tempfile

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.models import AgentState, ContentType, BookOutline
from tools.research_processor import research_processor
from tools.book_outline_generator import book_outline_generator
from tools.chapter_generator import chapter_generator
from tools.bibliography_compiler import bibliography_compiler
from config.settings import settings

class TestBookTools:
    """Test suite for book generation tools"""
    
    @pytest.fixture
    def sample_state(self):
        """Create a sample agent state for testing"""
        return AgentState(
            topic="The Future of Artificial Intelligence",
            content_type=ContentType.BOOK,
            context="A comprehensive analysis of AI trends and implications",
            user_preferences={
                "style": "academic",
                "citation_style": "APA",
                "target_audience": "general readers",
                "estimated_pages": 150
            }
        )
    
    @pytest.fixture
    def sample_research_data(self):
        """Create sample research data"""
        return {
            "sources": [
                {
                    "filename": "ai_trends_2024.txt",
                    "content": "Artificial intelligence is rapidly evolving with new breakthroughs in machine learning, natural language processing, and computer vision. Recent developments include large language models, generative AI, and autonomous systems.",
                    "word_count": 30,
                    "file_type": ".txt"
                },
                {
                    "filename": "ai_ethics.txt", 
                    "content": "The ethical implications of AI development include concerns about bias, privacy, job displacement, and the need for responsible AI governance. Researchers emphasize the importance of developing AI systems that are fair, transparent, and beneficial to society.",
                    "word_count": 35,
                    "file_type": ".txt"
                }
            ],
            "total_words": 65,
            "processing_method": "structured_data",
            "source_count": 2
        }
    
    @pytest.fixture
    def sample_outline(self):
        """Create a sample book outline"""
        return BookOutline(
            title="The Future of Artificial Intelligence",
            subtitle="Trends, Challenges, and Opportunities",
            description="An exploration of AI's impact on society and future developments",
            main_argument="AI will transform society in profound ways, requiring careful consideration of ethical and practical implications",
            target_audience="General readers interested in technology",
            chapters=[
                {
                    "chapter_number": 1,
                    "title": "Introduction to AI",
                    "description": "Overview of artificial intelligence and its current state",
                    "main_points": ["Definition of AI", "Historical context", "Current applications"],
                    "estimated_pages": 15,
                    "sections": [
                        {
                            "section_number": 1,
                            "title": "What is AI?",
                            "description": "Defining artificial intelligence",
                            "key_concepts": ["machine learning", "neural networks"]
                        }
                    ]
                }
            ],
            estimated_pages=150
        )
    
    @pytest.mark.asyncio
    async def test_research_processor(self, sample_state, sample_research_data):
        """Test research data processing"""
        result = await research_processor.execute(
            sample_state,
            research_data=sample_research_data
        )
        
        assert result.success, f"Research processing failed: {result.error}"
        assert "processed_data" in result.data
        assert "analysis" in result.data
        
        processed_data = result.data["processed_data"]
        assert processed_data["source_count"] == 2
        assert processed_data["total_words"] == 65
        
        analysis = result.data["analysis"]
        assert "key_themes" in analysis
        assert "main_concepts" in analysis
        
        # Check that state was updated
        assert "processed_research" in sample_state.research_data
        assert "research_analysis" in sample_state.research_data
        
        print(f"✓ Processed {processed_data['source_count']} research sources")
    
    @pytest.mark.asyncio
    async def test_book_outline_generator(self, sample_state):
        """Test book outline generation"""
        result = await book_outline_generator.execute(
            sample_state,
            book_title="Test Book on AI",
            style="academic",
            target_audience="general readers"
        )
        
        assert result.success, f"Outline generation failed: {result.error}"
        assert "outline" in result.data
        assert "summary" in result.data
        
        outline = result.data["outline"]
        assert outline["title"] == "Test Book on AI"
        assert len(outline["chapters"]) > 0
        assert outline["main_argument"] != ""
        
        # Check that state was updated
        assert sample_state.outline is not None
        assert len(sample_state.content_items) > 0
        
        print(f"✓ Generated outline with {len(outline['chapters'])} chapters")
    
    @pytest.mark.asyncio
    async def test_chapter_generator(self, sample_state, sample_outline):
        """Test chapter generation"""
        # Set up state with outline and research data
        sample_state.outline = sample_outline
        sample_state.research_data = {
            "research_analysis": {
                "key_themes": ["AI development", "machine learning", "ethics"],
                "main_concepts": ["neural networks", "algorithms", "automation"],
                "important_facts": ["AI is growing rapidly", "Ethics are important"]
            },
            "processed_research": {
                "sources": [
                    {
                        "filename": "test.txt",
                        "content": "AI research shows promising developments in various fields.",
                        "word_count": 10
                    }
                ]
            }
        }
        
        result = await chapter_generator.execute(
            sample_state,
            style="academic",
            citation_style="APA"
        )
        
        assert result.success, f"Chapter generation failed: {result.error}"
        assert "chapters" in result.data
        assert "summary" in result.data
        
        chapters = result.data["chapters"]
        assert len(chapters) > 0
        
        # Check chapter structure
        chapter = chapters[0]
        assert "content" in chapter
        assert "word_count" in chapter
        assert "chapter_number" in chapter
        
        # Check that content items were created
        chapter_items = sample_state.get_content_by_type(ContentType.CHAPTER)
        assert len(chapter_items) > 0
        
        print(f"✓ Generated {len(chapters)} chapters")
    
    @pytest.mark.asyncio
    async def test_bibliography_compiler(self, sample_state, sample_research_data):
        """Test bibliography compilation"""
        # Set up state with research data and mock chapters
        sample_state.research_data = {
            "processed_research": sample_research_data
        }
        
        # Add mock chapter with citations
        from src.models import ContentItem
        chapter_item = ContentItem(
            id="test_chapter",
            type=ContentType.CHAPTER,
            title="Test Chapter",
            content=json.dumps({
                "content": "This chapter discusses AI (Smith, 2023) and machine learning (Jones, 2024).",
                "citations": ["Smith, J. (2023). AI Fundamentals.", "Jones, M. (2024). ML Advances."]
            }),
            metadata={"chapter_number": 1}
        )
        sample_state.add_content_item(chapter_item)
        
        result = await bibliography_compiler.execute(
            sample_state,
            citation_style="APA",
            include_annotations=False
        )
        
        assert result.success, f"Bibliography compilation failed: {result.error}"
        assert "bibliography" in result.data
        assert "summary" in result.data
        
        bibliography = result.data["bibliography"]
        assert "formatted_bibliography" in bibliography
        assert bibliography["citation_count"] > 0
        
        print(f"✓ Compiled bibliography with {bibliography['citation_count']} citations")
    
    @pytest.mark.asyncio
    async def test_full_book_pipeline(self, sample_state):
        """Test the complete book generation pipeline"""
        print("\n--- Testing Full Book Pipeline ---")
        
        # Create temporary research file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Artificial intelligence is transforming industries worldwide. "
                   "Machine learning algorithms are becoming more sophisticated. "
                   "Ethical considerations in AI development are crucial for responsible innovation.")
            temp_file = f.name
        
        try:
            # Step 1: Process research
            print("1. Processing research data...")
            research_result = await research_processor.execute(
                sample_state,
                research_data=temp_file
            )
            assert research_result.success
            print(f"   ✓ Processed {research_result.data['summary']['sources']} sources")
            
            # Step 2: Generate outline
            print("2. Generating book outline...")
            outline_result = await book_outline_generator.execute(
                sample_state,
                book_title="Test AI Book",
                style="academic"
            )
            assert outline_result.success
            print(f"   ✓ Outline with {outline_result.data['summary']['chapters']} chapters")
            
            # Step 3: Generate chapters (limit to 1 for testing)
            print("3. Generating chapters...")
            # Limit chapters for testing
            if sample_state.outline and len(sample_state.outline.chapters) > 1:
                sample_state.outline.chapters = sample_state.outline.chapters[:1]
            
            chapter_result = await chapter_generator.execute(
                sample_state,
                style="academic",
                citation_style="APA"
            )
            assert chapter_result.success
            print(f"   ✓ {chapter_result.data['summary']['generated_chapters']} chapters generated")
            
            # Step 4: Compile bibliography
            print("4. Compiling bibliography...")
            bib_result = await bibliography_compiler.execute(
                sample_state,
                citation_style="APA"
            )
            assert bib_result.success
            print(f"   ✓ Bibliography with {bib_result.data['summary']['total_citations']} citations")
            
            # Verify final state
            assert len(sample_state.content_items) > 0
            outline_items = sample_state.get_content_by_type(ContentType.OUTLINE)
            chapter_items = sample_state.get_content_by_type(ContentType.CHAPTER)
            reference_items = sample_state.get_content_by_type(ContentType.REFERENCE)
            
            print(f"\nFinal state:")
            print(f"   - Outline items: {len(outline_items)}")
            print(f"   - Chapter items: {len(chapter_items)}")
            print(f"   - Reference items: {len(reference_items)}")
            print(f"   - Total content items: {len(sample_state.content_items)}")
            
            assert len(outline_items) > 0
            assert len(chapter_items) > 0
            
            print("✓ Full pipeline completed successfully")
            
        finally:
            # Clean up temp file
            os.unlink(temp_file)

def test_research_file_processing():
    """Test processing of actual research files"""
    print("\n--- Testing Research File Processing ---")
    
    # Create test files
    test_files = []
    
    # Text file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("This is a test research document about artificial intelligence. "
               "It contains information about machine learning and neural networks.")
        test_files.append(f.name)
    
    # Markdown file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
        f.write("# Research Notes\n\n"
               "## AI Development\n\n"
               "Recent advances in AI include:\n"
               "- Large language models\n"
               "- Computer vision improvements\n"
               "- Robotics integration\n")
        test_files.append(f.name)
    
    try:
        for file_path in test_files:
            print(f"Testing file: {os.path.basename(file_path)}")
            
            # Test file reading
            state = AgentState(topic="Test", content_type=ContentType.BOOK)
            
            async def test_file():
                result = await research_processor.execute(state, research_data=file_path)
                assert result.success, f"Failed to process {file_path}: {result.error}"
                assert result.data["summary"]["sources"] == 1
                print(f"   ✓ Processed successfully")
            
            asyncio.run(test_file())
    
    finally:
        # Clean up
        for file_path in test_files:
            os.unlink(file_path)

async def run_manual_tests():
    """Run manual tests for development"""
    print("="*60)
    print("BOOK TOOLS MANUAL TEST SUITE")
    print("="*60)
    
    # Test settings
    print("\n--- Testing Settings ---")
    has_ai_key = any([
        settings.GEMINI_API_KEY,
        settings.OPENAI_API_KEY,
        settings.CLAUDE_API_KEY
    ])
    
    if not has_ai_key:
        print("⚠ No AI API keys configured - tests will fail")
        return
    
    print("✓ AI API key configured")
    
    # Test file processing
    test_research_file_processing()
    
    # Create test instance
    test_instance = TestBookTools()
    
    # Create sample state
    sample_state = AgentState(
        topic="Manual Test Book",
        content_type=ContentType.BOOK,
        user_preferences={
            "style": "academic",
            "citation_style": "APA"
        }
    )
    
    # Create sample data
    sample_research_data = {
        "sources": [
            {
                "filename": "test_source.txt",
                "content": "This is test research content about artificial intelligence and machine learning.",
                "word_count": 12,
                "file_type": ".txt"
            }
        ],
        "total_words": 12,
        "source_count": 1
    }
    
    sample_outline = BookOutline(
        title="Test Book",
        description="A test book",
        main_argument="Test argument",
        target_audience="Test audience",
        chapters=[
            {
                "chapter_number": 1,
                "title": "Test Chapter",
                "description": "A test chapter",
                "main_points": ["Point 1", "Point 2"],
                "estimated_pages": 10,
                "sections": []
            }
        ]
    )
    
    try:
        # Run tests
        await test_instance.test_research_processor(sample_state, sample_research_data)
        
        # Reset state for outline test
        sample_state = AgentState(topic="Test Book", content_type=ContentType.BOOK)
        await test_instance.test_book_outline_generator(sample_state)
        
        await test_instance.test_chapter_generator(sample_state, sample_outline)
        await test_instance.test_bibliography_compiler(sample_state, sample_research_data)
        
        # Reset state for full pipeline test
        sample_state = AgentState(
            topic="Full Pipeline Test Book",
            content_type=ContentType.BOOK,
            user_preferences={"style": "academic"}
        )
        await test_instance.test_full_book_pipeline(sample_state)
        
        print("\n" + "="*60)
        print("ALL TESTS PASSED ✓")
        print("="*60)
        
    except Exception as e:
        print(f"\n" + "="*60)
        print(f"TEST FAILED ✗")
        print(f"Error: {e}")
        print("="*60)
        raise

if __name__ == "__main__":
    # Run manual tests
    asyncio.run(run_manual_tests())
