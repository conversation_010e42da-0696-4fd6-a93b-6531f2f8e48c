#!/usr/bin/env python3
"""
Test suite for API endpoints
"""

import asyncio
import pytest
import json
import sys
import os
import tempfile
from fastapi.testclient import TestClient

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api_server import app
from config.settings import settings

class TestAPIEndpoints:
    """Test suite for API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    def test_root_endpoint(self, client):
        """Test root endpoint"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "endpoints" in data
        print("✓ Root endpoint working")
    
    def test_health_endpoint(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        print("✓ Health endpoint working")
    
    def test_course_outline_endpoint(self, client):
        """Test course outline generation endpoint"""
        payload = {
            "course_title": "Test Course API",
            "style": "professional",
            "tone": "engaging",
            "depth": "intermediate",
            "target_audience": "developers",
            "duration": "4 hours"
        }
        
        response = client.post("/course/outline", json=payload)
        
        if response.status_code == 500:
            # Check if it's due to missing API keys
            error_detail = response.json().get("detail", "")
            if "API key" in error_detail or "No AI API key" in error_detail:
                pytest.skip("AI API key not configured")
            else:
                assert False, f"Unexpected error: {error_detail}"
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "outline" in data["data"]
        
        outline = data["data"]["outline"]
        assert outline["title"] == "Test Course API"
        assert len(outline["modules"]) > 0
        
        print(f"✓ Course outline endpoint - generated {len(outline['modules'])} modules")
    
    def test_book_outline_endpoint(self, client):
        """Test book outline generation endpoint"""
        payload = {
            "book_title": "Test Book API",
            "style": "academic",
            "citation_style": "APA",
            "target_audience": "researchers",
            "estimated_pages": 200
        }
        
        response = client.post("/book/outline", json=payload)
        
        if response.status_code == 500:
            # Check if it's due to missing API keys
            error_detail = response.json().get("detail", "")
            if "API key" in error_detail or "No AI API key" in error_detail:
                pytest.skip("AI API key not configured")
            else:
                assert False, f"Unexpected error: {error_detail}"
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "outline" in data["data"]
        
        outline = data["data"]["outline"]
        assert outline["title"] == "Test Book API"
        assert len(outline["chapters"]) > 0
        
        print(f"✓ Book outline endpoint - generated {len(outline['chapters'])} chapters")
    
    def test_book_research_endpoint(self, client):
        """Test book research processing endpoint"""
        # Create temporary research data
        research_data = {
            "sources": [
                {
                    "filename": "test_research.txt",
                    "content": "This is test research content about machine learning and artificial intelligence. It covers various aspects of AI development and implementation.",
                    "word_count": 20,
                    "file_type": ".txt"
                }
            ],
            "total_words": 20,
            "source_count": 1
        }
        
        payload = {
            "research_data": research_data,
            "book_title": "AI Research Book"
        }
        
        response = client.post("/book/research", json=payload)
        
        if response.status_code == 500:
            error_detail = response.json().get("detail", "")
            if "API key" in error_detail:
                pytest.skip("AI API key not configured")
            else:
                assert False, f"Unexpected error: {error_detail}"
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "processed_data" in data["data"]
        assert "analysis" in data["data"]
        
        print("✓ Book research endpoint working")
    
    def test_course_scripts_endpoint(self, client):
        """Test course scripts generation endpoint"""
        # Sample outline data
        outline = {
            "title": "Test Course Scripts",
            "description": "A test course for script generation",
            "learning_objectives": ["Learn testing", "Understand APIs"],
            "target_audience": "developers",
            "modules": [
                {
                    "module_number": 1,
                    "title": "Introduction",
                    "description": "Course introduction",
                    "lessons": [
                        {
                            "lesson_number": 1,
                            "title": "Getting Started",
                            "description": "Introduction to the course",
                            "learning_objectives": ["Understand course goals"],
                            "key_concepts": ["testing", "APIs"]
                        }
                    ]
                }
            ]
        }
        
        payload = {
            "outline": outline,
            "style": "professional",
            "tone": "engaging"
        }
        
        response = client.post("/course/scripts", json=payload)
        
        if response.status_code == 500:
            error_detail = response.json().get("detail", "")
            if "API key" in error_detail:
                pytest.skip("AI API key not configured")
            else:
                assert False, f"Unexpected error: {error_detail}"
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "scripts" in data["data"]
        
        print(f"✓ Course scripts endpoint - generated {len(data['data']['scripts'])} scripts")
    
    def test_full_course_creation_endpoint(self, client):
        """Test full course creation endpoint (background task)"""
        payload = {
            "course_title": "Full Course Test API",
            "style": "professional",
            "tone": "engaging",
            "depth": "beginner",
            "target_audience": "students",
            "duration": "2 hours",
            "context": "A test course for API validation"
        }
        
        response = client.post("/course/create", json=payload)
        
        if response.status_code == 500:
            error_detail = response.json().get("detail", "")
            if "API key" in error_detail:
                pytest.skip("AI API key not configured")
            else:
                assert False, f"Unexpected error: {error_detail}"
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "task_id" in data["data"]
        assert data["data"]["status"] == "started"
        
        task_id = data["data"]["task_id"]
        print(f"✓ Full course creation started - task ID: {task_id}")
        
        # Check task status
        status_response = client.get(f"/task/{task_id}")
        assert status_response.status_code == 200
        status_data = status_response.json()
        assert status_data["success"] is True
        assert "status" in status_data["data"]
        
        print(f"✓ Task status endpoint working - status: {status_data['data']['status']}")
    
    def test_full_book_generation_endpoint(self, client):
        """Test full book generation endpoint (background task)"""
        # Create sample research data
        research_data = "This is sample research content for book generation testing. It contains information about various topics that will be used to generate a comprehensive book."
        
        payload = {
            "book_title": "Full Book Test API",
            "research_data": research_data,
            "style": "academic",
            "citation_style": "APA",
            "target_audience": "researchers",
            "estimated_pages": 100,
            "context": "A test book for API validation"
        }
        
        response = client.post("/book/generate", json=payload)
        
        if response.status_code == 500:
            error_detail = response.json().get("detail", "")
            if "API key" in error_detail:
                pytest.skip("AI API key not configured")
            else:
                assert False, f"Unexpected error: {error_detail}"
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "task_id" in data["data"]
        assert data["data"]["status"] == "started"
        
        task_id = data["data"]["task_id"]
        print(f"✓ Full book generation started - task ID: {task_id}")
        
        # Check task status
        status_response = client.get(f"/task/{task_id}")
        assert status_response.status_code == 200
        status_data = status_response.json()
        assert status_data["success"] is True
        assert "status" in status_data["data"]
        
        print(f"✓ Task status endpoint working - status: {status_data['data']['status']}")
    
    def test_invalid_endpoints(self, client):
        """Test invalid endpoints return appropriate errors"""
        # Test non-existent endpoint
        response = client.get("/nonexistent")
        assert response.status_code == 404
        
        # Test invalid task ID
        response = client.get("/task/invalid_task_id")
        assert response.status_code == 404
        
        # Test missing required fields
        response = client.post("/course/outline", json={})
        assert response.status_code == 422  # Validation error
        
        print("✓ Invalid endpoints handled correctly")

def test_api_with_file_upload():
    """Test API with actual file upload simulation"""
    print("\n--- Testing File Upload Simulation ---")
    
    client = TestClient(app)
    
    # Create temporary research file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("This is a comprehensive research document about artificial intelligence. "
               "It covers machine learning algorithms, neural networks, and deep learning. "
               "The document also discusses ethical implications of AI development.")
        temp_file = f.name
    
    try:
        # Test research processing with file path
        payload = {
            "research_data": temp_file,
            "book_title": "AI Research Book"
        }
        
        response = client.post("/book/research", json=payload)
        
        if response.status_code == 500:
            error_detail = response.json().get("detail", "")
            if "API key" in error_detail:
                print("⚠ Skipping file upload test - AI API key not configured")
                return
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        
        print("✓ File upload simulation working")
        
    finally:
        os.unlink(temp_file)

def run_manual_api_tests():
    """Run manual API tests"""
    print("="*60)
    print("API ENDPOINTS MANUAL TEST SUITE")
    print("="*60)
    
    # Check if AI API key is configured
    has_ai_key = any([
        settings.GEMINI_API_KEY,
        settings.OPENAI_API_KEY,
        settings.CLAUDE_API_KEY
    ])
    
    if not has_ai_key:
        print("⚠ No AI API keys configured - most tests will be skipped")
    else:
        print("✓ AI API key configured")
    
    # Create test instance and client
    test_instance = TestAPIEndpoints()
    client = TestClient(app)
    
    try:
        # Run basic tests
        test_instance.test_root_endpoint(client)
        test_instance.test_health_endpoint(client)
        test_instance.test_invalid_endpoints(client)
        
        if has_ai_key:
            # Run AI-dependent tests
            test_instance.test_course_outline_endpoint(client)
            test_instance.test_book_outline_endpoint(client)
            test_instance.test_book_research_endpoint(client)
            test_instance.test_course_scripts_endpoint(client)
            test_instance.test_full_course_creation_endpoint(client)
            test_instance.test_full_book_generation_endpoint(client)
            
            # Test file upload simulation
            test_api_with_file_upload()
        
        print("\n" + "="*60)
        print("ALL API TESTS PASSED ✓")
        print("="*60)
        
    except Exception as e:
        print(f"\n" + "="*60)
        print(f"API TEST FAILED ✗")
        print(f"Error: {e}")
        print("="*60)
        raise

if __name__ == "__main__":
    # Run manual tests
    run_manual_api_tests()
