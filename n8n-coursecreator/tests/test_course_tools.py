#!/usr/bin/env python3
"""
Test suite for course creation tools
"""

import asyncio
import pytest
import json
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.models import AgentState, ContentType, CourseOutline
from tools.course_outline_generator import course_outline_generator
from tools.script_generator import script_generator
from tools.qa_generator import qa_generator
from config.settings import settings

class TestCourseTools:
    """Test suite for course creation tools"""
    
    @pytest.fixture
    def sample_state(self):
        """Create a sample agent state for testing"""
        return AgentState(
            topic="Introduction to Python Programming",
            content_type=ContentType.COURSE,
            context="A beginner-friendly course for new programmers",
            user_preferences={
                "style": "professional",
                "tone": "engaging",
                "depth": "beginner",
                "target_audience": "programming beginners",
                "duration": "6 hours"
            }
        )
    
    @pytest.fixture
    def sample_outline(self):
        """Create a sample course outline"""
        return CourseOutline(
            title="Introduction to Python Programming",
            description="A comprehensive introduction to Python programming for beginners",
            learning_objectives=[
                "Understand Python syntax and basic concepts",
                "Write simple Python programs",
                "Use Python data structures effectively"
            ],
            target_audience="Programming beginners",
            modules=[
                {
                    "module_number": 1,
                    "title": "Python Basics",
                    "description": "Introduction to Python syntax and concepts",
                    "lessons": [
                        {
                            "lesson_number": 1,
                            "title": "Variables and Data Types",
                            "description": "Learn about Python variables and basic data types",
                            "learning_objectives": ["Understand variables", "Use basic data types"],
                            "estimated_duration": "30 minutes",
                            "key_concepts": ["variables", "strings", "numbers", "booleans"]
                        }
                    ]
                }
            ]
        )
    
    @pytest.mark.asyncio
    async def test_course_outline_generator(self, sample_state):
        """Test course outline generation"""
        result = await course_outline_generator.execute(
            sample_state,
            course_title="Introduction to Python Programming",
            style="professional",
            tone="engaging",
            depth="beginner"
        )
        
        assert result.success, f"Outline generation failed: {result.error}"
        assert "outline" in result.data
        assert "summary" in result.data
        
        outline = result.data["outline"]
        assert outline["title"] == "Introduction to Python Programming"
        assert len(outline["modules"]) > 0
        assert len(outline["learning_objectives"]) > 0
        
        # Check that state was updated
        assert sample_state.outline is not None
        assert len(sample_state.content_items) > 0
        
        print(f"✓ Generated outline with {len(outline['modules'])} modules")
    
    @pytest.mark.asyncio
    async def test_script_generator(self, sample_state, sample_outline):
        """Test script generation"""
        # Set up state with outline
        sample_state.outline = sample_outline
        
        result = await script_generator.execute(sample_state)
        
        assert result.success, f"Script generation failed: {result.error}"
        assert "scripts" in result.data
        assert "summary" in result.data
        
        scripts = result.data["scripts"]
        assert len(scripts) > 0
        
        # Check script structure
        script = scripts[0]
        assert "script" in script
        assert "word_count" in script
        assert "estimated_duration_minutes" in script
        
        # Check that content items were created
        script_items = sample_state.get_content_by_type(ContentType.SCRIPT)
        assert len(script_items) > 0
        
        print(f"✓ Generated {len(scripts)} scripts")
    
    @pytest.mark.asyncio
    async def test_qa_generator(self, sample_state, sample_outline):
        """Test Q&A generation"""
        # Set up state with outline
        sample_state.outline = sample_outline
        
        result = await qa_generator.execute(
            sample_state,
            qa_count=3,
            include_audio=False,
            include_video=False
        )
        
        assert result.success, f"Q&A generation failed: {result.error}"
        assert "qa_content" in result.data
        assert "summary" in result.data
        
        qa_content = result.data["qa_content"]
        assert len(qa_content) > 0
        
        # Check Q&A structure
        module_qa = qa_content[0]
        assert "qa_pairs" in module_qa
        assert len(module_qa["qa_pairs"]) > 0
        
        qa_pair = module_qa["qa_pairs"][0]
        assert "question" in qa_pair
        assert "answer" in qa_pair
        assert "question_type" in qa_pair
        
        print(f"✓ Generated Q&A for {len(qa_content)} modules")
    
    @pytest.mark.asyncio
    async def test_full_course_pipeline(self, sample_state):
        """Test the complete course creation pipeline"""
        print("\n--- Testing Full Course Pipeline ---")
        
        # Step 1: Generate outline
        print("1. Generating outline...")
        outline_result = await course_outline_generator.execute(
            sample_state,
            course_title="Test Course",
            style="professional"
        )
        assert outline_result.success
        print(f"   ✓ Outline with {len(outline_result.data['outline']['modules'])} modules")
        
        # Step 2: Generate scripts
        print("2. Generating scripts...")
        script_result = await script_generator.execute(sample_state)
        assert script_result.success
        print(f"   ✓ {script_result.data['summary']['total_scripts']} scripts generated")
        
        # Step 3: Generate Q&A
        print("3. Generating Q&A...")
        qa_result = await qa_generator.execute(
            sample_state,
            qa_count=2,
            include_audio=False,
            include_video=False
        )
        assert qa_result.success
        print(f"   ✓ Q&A for {len(qa_result.data['qa_content'])} modules")
        
        # Verify final state
        assert len(sample_state.content_items) > 0
        outline_items = sample_state.get_content_by_type(ContentType.OUTLINE)
        script_items = sample_state.get_content_by_type(ContentType.SCRIPT)
        qa_items = sample_state.get_content_by_type(ContentType.QA)
        
        print(f"\nFinal state:")
        print(f"   - Outline items: {len(outline_items)}")
        print(f"   - Script items: {len(script_items)}")
        print(f"   - Q&A items: {len(qa_items)}")
        print(f"   - Total content items: {len(sample_state.content_items)}")
        
        assert len(outline_items) > 0
        assert len(script_items) > 0
        assert len(qa_items) > 0
        
        print("✓ Full pipeline completed successfully")

def test_settings_validation():
    """Test settings validation"""
    print("\n--- Testing Settings ---")
    
    # Check if at least one AI API key is configured
    has_ai_key = any([
        settings.GEMINI_API_KEY,
        settings.OPENAI_API_KEY,
        settings.CLAUDE_API_KEY
    ])
    
    if has_ai_key:
        print("✓ At least one AI API key is configured")
        
        # Test AI configuration
        try:
            ai_config = settings.get_primary_ai_api()
            print(f"✓ Primary AI provider: {ai_config['provider']}")
            print(f"✓ Model: {ai_config['model']}")
        except Exception as e:
            print(f"✗ AI configuration error: {e}")
            assert False, f"AI configuration failed: {e}"
    else:
        print("⚠ No AI API keys configured - tests will fail")
        pytest.skip("No AI API keys configured")
    
    # Check optional services
    if settings.HEYGEN_API_KEY:
        print("✓ HeyGen API key configured")
    else:
        print("⚠ HeyGen API key not configured - video generation will be skipped")
    
    if settings.ELEVENLABS_API_KEY:
        print("✓ ElevenLabs API key configured")
    else:
        print("⚠ ElevenLabs API key not configured - audio generation will be skipped")

async def run_manual_tests():
    """Run manual tests for development"""
    print("="*60)
    print("COURSE TOOLS MANUAL TEST SUITE")
    print("="*60)
    
    # Test settings
    test_settings_validation()
    
    # Create test instance
    test_instance = TestCourseTools()
    
    # Create sample state
    sample_state = AgentState(
        topic="Manual Test Course",
        content_type=ContentType.COURSE,
        user_preferences={
            "style": "professional",
            "tone": "engaging",
            "depth": "intermediate"
        }
    )
    
    # Create sample outline
    sample_outline = CourseOutline(
        title="Manual Test Course",
        description="A test course for validation",
        learning_objectives=["Test objective 1", "Test objective 2"],
        target_audience="Test audience",
        modules=[
            {
                "module_number": 1,
                "title": "Test Module",
                "description": "A test module",
                "lessons": [
                    {
                        "lesson_number": 1,
                        "title": "Test Lesson",
                        "description": "A test lesson",
                        "learning_objectives": ["Test lesson objective"],
                        "key_concepts": ["concept1", "concept2"]
                    }
                ]
            }
        ]
    )
    
    try:
        # Run tests
        await test_instance.test_course_outline_generator(sample_state)
        await test_instance.test_script_generator(sample_state, sample_outline)
        await test_instance.test_qa_generator(sample_state, sample_outline)
        
        # Reset state for full pipeline test
        sample_state = AgentState(
            topic="Full Pipeline Test",
            content_type=ContentType.COURSE,
            user_preferences={"style": "professional"}
        )
        await test_instance.test_full_course_pipeline(sample_state)
        
        print("\n" + "="*60)
        print("ALL TESTS PASSED ✓")
        print("="*60)
        
    except Exception as e:
        print(f"\n" + "="*60)
        print(f"TEST FAILED ✗")
        print(f"Error: {e}")
        print("="*60)
        raise

if __name__ == "__main__":
    # Run manual tests
    asyncio.run(run_manual_tests())
