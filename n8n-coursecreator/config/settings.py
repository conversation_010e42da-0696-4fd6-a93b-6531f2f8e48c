import os
from dotenv import load_dotenv

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
load_dotenv(os.path.join(project_root, '.env'))

class Settings:
    # AI API Keys
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    CLAUDE_API_KEY = os.getenv("CLAUDE_API_KEY")
    REPLICATE_API_TOKEN = os.getenv("REPLICATE_API_TOKEN")
    
    # Video Generation APIs
    HEYGEN_API_KEY = os.getenv("HEYGEN_API_KEY")
    ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")
    
    # Cloud Storage
    AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
    AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
    S3_BUCKET = os.getenv("S3_BUCKET")
    
    # Google Services
    GOOGLE_CREDENTIALS_PATH = os.getenv("GOOGLE_CREDENTIALS_PATH")
    
    # Model Configuration
    AGENT_MODEL = os.getenv("AGENT_MODEL", "gemini-1.5-flash")
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    CLAUDE_MODEL = os.getenv("CLAUDE_MODEL", "claude-3-5-sonnet-20241022")
    
    # Application Settings
    MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))
    TIMEOUT_SECONDS = int(os.getenv("TIMEOUT_SECONDS", "300"))
    DEBUG = os.getenv("DEBUG", "false").lower() == "true"
    
    # Course Creation Settings
    DEFAULT_COURSE_STYLE = os.getenv("DEFAULT_COURSE_STYLE", "professional")
    DEFAULT_COURSE_TONE = os.getenv("DEFAULT_COURSE_TONE", "engaging")
    DEFAULT_COURSE_DEPTH = os.getenv("DEFAULT_COURSE_DEPTH", "intermediate")
    
    # Book Generation Settings
    DEFAULT_BOOK_STYLE = os.getenv("DEFAULT_BOOK_STYLE", "academic")
    DEFAULT_CITATION_STYLE = os.getenv("DEFAULT_CITATION_STYLE", "APA")
    
    def validate_required_settings(self):
        """Validate that required environment variables are set"""
        required = []
        
        # Check for at least one AI API key
        if not any([self.GEMINI_API_KEY, self.OPENAI_API_KEY, self.CLAUDE_API_KEY]):
            required.append("At least one AI API key (GEMINI_API_KEY, OPENAI_API_KEY, or CLAUDE_API_KEY)")
        
        # Check for video generation if needed
        if not self.HEYGEN_API_KEY:
            print("WARNING: HEYGEN_API_KEY not set - video generation will not be available")
        
        # Check for TTS if needed
        if not self.ELEVENLABS_API_KEY:
            print("WARNING: ELEVENLABS_API_KEY not set - audio generation will not be available")
        
        if required:
            print(f"ERROR: Missing required environment variables: {', '.join(required)}")
            return False
        return True
    
    def get_primary_ai_api(self):
        """Get the primary AI API configuration"""
        if self.CLAUDE_API_KEY:
            return {
                "provider": "claude",
                "api_key": self.CLAUDE_API_KEY,
                "model": self.CLAUDE_MODEL,
                "base_url": "https://api.anthropic.com/v1/messages"
            }
        elif self.OPENAI_API_KEY:
            return {
                "provider": "openai",
                "api_key": self.OPENAI_API_KEY,
                "model": self.OPENAI_MODEL,
                "base_url": "https://api.openai.com/v1/chat/completions"
            }
        elif self.GEMINI_API_KEY:
            return {
                "provider": "gemini",
                "api_key": self.GEMINI_API_KEY,
                "model": self.AGENT_MODEL,
                "base_url": "https://generativelanguage.googleapis.com/v1beta/models"
            }
        else:
            raise ValueError("No AI API key configured")

settings = Settings()
