# n8n-MPAF Course Creator & Book Generator

A comprehensive hybrid application that combines n8n workflow automation with MPAF (Multi-Purpose Agent Framework) tools for automated course creation and book generation.

## 🚀 Features

### Course Creation
- **Automated Course Outline Generation**: AI-powered course structure creation
- **Script Generation**: Detailed lesson scripts with learning objectives
- **Video Creation**: Avatar-based video generation using HeyGen API
- **Q&A Generation**: Interactive questions and answers for each module
- **Multi-format Output**: Text, audio, and video content

### Book Generation
- **Research Processing**: Analyze and process research materials
- **Book Outline Creation**: Structured chapter and section planning
- **Chapter Generation**: Full chapter content with citations
- **Bibliography Compilation**: Automated reference formatting
- **Multiple Citation Styles**: APA, MLA, Chicago support

### Integration Features
- **n8n Workflow Integration**: Complete workflow automation
- **API Endpoints**: RESTful API for external integrations
- **Background Processing**: Long-running tasks with status tracking
- **Google Services Integration**: Drive storage and Sheets logging
- **Email Notifications**: Automated progress updates

## 📋 Prerequisites

### Required
- Python 3.8+
- At least one AI API key:
  - Claude API key (Anthropic)
  - OpenAI API key
  - Google Gemini API key

### Optional (for full functionality)
- HeyGen API key (for video generation)
- ElevenLabs API key (for audio generation)
- Google Cloud credentials (for Drive/Sheets integration)
- AWS credentials (for S3 storage)

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd n8n-coursecreator
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Environment Configuration
Create a `.env` file in the project root:

```env
# AI API Keys (at least one required)
CLAUDE_API_KEY=your_claude_api_key
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key

# Video Generation (optional)
HEYGEN_API_KEY=your_heygen_api_key

# Audio Generation (optional)
ELEVENLABS_API_KEY=your_elevenlabs_api_key

# Cloud Storage (optional)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET=your_s3_bucket

# Google Services (optional)
GOOGLE_CREDENTIALS_PATH=path/to/credentials.json

# Application Settings
DEBUG=false
MAX_RETRIES=3
TIMEOUT_SECONDS=300

# Default Preferences
DEFAULT_COURSE_STYLE=professional
DEFAULT_COURSE_TONE=engaging
DEFAULT_BOOK_STYLE=academic
DEFAULT_CITATION_STYLE=APA
```

## 🚀 Quick Start

### Command Line Usage

#### Create a Course
```bash
python course_agent.py "Introduction to Python Programming" \
  --style professional \
  --tone engaging \
  --depth beginner \
  --target-audience "programming beginners" \
  --duration "6 hours" \
  --verbose
```

#### Generate a Book
```bash
python book_agent.py "The Future of AI" \
  --research-data research_files/ \
  --style academic \
  --citation-style APA \
  --target-audience "general readers" \
  --estimated-pages 200 \
  --verbose
```

### API Server
```bash
# Start the API server
python api_server.py

# Server will be available at http://localhost:8000
# API documentation at http://localhost:8000/docs
```

### n8n Integration

1. Import the workflow files:
   - `workflows/course_creation_workflow.json`
   - `workflows/book_generation_workflow.json`

2. Configure environment variables in n8n:
   ```
   MPAF_ENDPOINT=http://your-api-server:8000
   MPAF_API_KEY=your_api_key
   GOOGLE_SHEET_ID=your_sheet_id
   GOOGLE_DRIVE_FOLDER_ID=your_folder_id
   DEFAULT_USER_EMAIL=<EMAIL>
   ```

3. Set up credentials:
   - SMTP for email notifications
   - Google Sheets API
   - Google Drive API

## 📚 API Documentation

### Course Creation Endpoints

#### Generate Course Outline
```http
POST /course/outline
Content-Type: application/json

{
  "course_title": "Course Title",
  "style": "professional",
  "tone": "engaging",
  "depth": "intermediate",
  "target_audience": "developers",
  "duration": "4 hours"
}
```

#### Generate Course Scripts
```http
POST /course/scripts
Content-Type: application/json

{
  "outline": { /* course outline object */ },
  "style": "professional",
  "tone": "engaging"
}
```

#### Create Videos
```http
POST /course/videos
Content-Type: application/json

{
  "scripts": [ /* array of script objects */ ],
  "avatar_id": "default",
  "voice_id": "default",
  "video_quality": "720p"
}
```

#### Full Course Creation
```http
POST /course/create
Content-Type: application/json

{
  "course_title": "Complete Course",
  "style": "professional",
  "tone": "engaging",
  "depth": "intermediate",
  "target_audience": "students",
  "duration": "6 hours"
}
```

### Book Generation Endpoints

#### Process Research Data
```http
POST /book/research
Content-Type: application/json

{
  "research_data": "file_path_or_content",
  "book_title": "Book Title"
}
```

#### Generate Book Outline
```http
POST /book/outline
Content-Type: application/json

{
  "book_title": "Book Title",
  "style": "academic",
  "citation_style": "APA",
  "target_audience": "researchers",
  "estimated_pages": 200
}
```

#### Generate Chapters
```http
POST /book/chapters
Content-Type: application/json

{
  "outline": { /* book outline object */ },
  "style": "academic",
  "citation_style": "APA",
  "include_citations": true
}
```

#### Full Book Generation
```http
POST /book/generate
Content-Type: application/json

{
  "book_title": "Complete Book",
  "research_data": "research_content",
  "style": "academic",
  "citation_style": "APA",
  "target_audience": "general readers",
  "estimated_pages": 200
}
```

### Task Status
```http
GET /task/{task_id}
```

## 🧪 Testing

### Run All Tests
```bash
# Course tools tests
python tests/test_course_tools.py

# Book tools tests
python tests/test_book_tools.py

# API endpoint tests
python tests/test_api_endpoints.py
```

### Run with pytest
```bash
pip install pytest pytest-asyncio
pytest tests/ -v
```

## 🏗️ Architecture

### MPAF Framework Components
- **Base Tool**: Abstract base class for all tools
- **Models**: Data structures for state management
- **Settings**: Configuration management
- **Tools**: Specialized tools for each function

### Tool Categories

#### Course Creation Tools
- `CourseOutlineGenerator`: Creates course structure
- `ScriptGenerator`: Generates lesson scripts
- `VideoCreator`: Creates avatar videos
- `QAGenerator`: Generates Q&A content

#### Book Generation Tools
- `ResearchProcessor`: Processes research materials
- `BookOutlineGenerator`: Creates book structure
- `ChapterGenerator`: Writes book chapters
- `BibliographyCompiler`: Formats citations

### Agent Runners
- `CourseCreationAgent`: Orchestrates course creation
- `BookGenerationAgent`: Orchestrates book generation

### API Layer
- FastAPI server with async endpoints
- Background task processing
- Request/response validation
- Error handling and logging

## 🔧 Configuration

### AI Provider Configuration
The system automatically selects the best available AI provider:
1. Claude (Anthropic) - preferred for quality
2. OpenAI GPT-4 - good balance of speed and quality
3. Google Gemini - fast and cost-effective

### Video Generation
- Requires HeyGen API key
- Supports multiple avatar and voice options
- Configurable video quality (720p, 1080p)

### Audio Generation
- Requires ElevenLabs API key
- Used for Q&A audio content
- Configurable voice settings

## 📊 Monitoring and Logging

### Logging Levels
- `INFO`: General operation information
- `WARNING`: Non-critical issues
- `ERROR`: Error conditions
- `DEBUG`: Detailed debugging information

### Task Tracking
- Background tasks store status in memory
- Production deployments should use Redis/database
- Task status includes progress percentage and messages

## 🚀 Deployment

### Local Development
```bash
python api_server.py
```

### Production Deployment
1. Use a production WSGI server (gunicorn, uvicorn)
2. Set up reverse proxy (nginx)
3. Configure environment variables
4. Set up monitoring and logging
5. Use Redis for task storage

### Docker Deployment
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "api_server:app", "--host", "0.0.0.0", "--port", "8000"]
```

### n8n Integration
1. Deploy API server to accessible endpoint
2. Import workflow JSON files
3. Configure environment variables
4. Set up required credentials
5. Test workflows with sample data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
1. Check the documentation
2. Run the test suite to verify setup
3. Check logs for error details
4. Review API responses for error messages

## 🔮 Future Enhancements

- Multi-language support
- Advanced video editing features
- Interactive course elements
- Real-time collaboration
- Advanced analytics and reporting
- Integration with learning management systems
