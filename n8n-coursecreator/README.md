# n8n-MPAF Course Creator & Book Generator

A comprehensive hybrid application that combines n8n workflow automation with MPAF (Multi-Purpose Agent Framework) tools for automated course creation and book generation.

## 🚀 Features

### Course Creation
- **Automated Course Outline Generation**: AI-powered course structure creation
- **Script Generation**: Detailed lesson scripts with learning objectives
- **Video Creation**: Avatar-based video generation using HeyGen API
- **Q&A Generation**: Interactive questions and answers for each module
- **Multi-format Output**: Text, audio, and video content

### Book Generation
- **Research Processing**: Analyze and process research materials
- **Book Outline Creation**: Structured chapter and section planning
- **Chapter Generation**: Full chapter content with citations
- **Bibliography Compilation**: Automated reference formatting
- **Multiple Citation Styles**: APA, MLA, Chicago support

### Integration Features
- **n8n Workflow Integration**: Complete workflow automation
- **API Endpoints**: RESTful API for external integrations
- **Background Processing**: Long-running tasks with status tracking
- **Google Services Integration**: Drive storage and Sheets logging
- **Email Notifications**: Automated progress updates

## 📋 Prerequisites

### Required
- Python 3.8+
- At least one AI API key:
  - Claude API key (Anthropic)
  - OpenAI API key
  - Google Gemini API key

### Optional (for full functionality)
- HeyGen API key (for video generation)
- ElevenLabs API key (for audio generation)
- Google Cloud credentials (for Drive/Sheets integration)
- AWS credentials (for S3 storage)

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd n8n-coursecreator
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Environment Configuration
Create a `.env` file in the project root:

```env
# AI API Keys (at least one required)
CLAUDE_API_KEY=your_claude_api_key
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key

# Video Generation (optional)
HEYGEN_API_KEY=your_heygen_api_key

# Audio Generation (optional)
ELEVENLABS_API_KEY=your_elevenlabs_api_key

# Cloud Storage (optional)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET=your_s3_bucket

# Google Services (optional)
GOOGLE_CREDENTIALS_PATH=path/to/credentials.json

# Application Settings
DEBUG=false
MAX_RETRIES=3
TIMEOUT_SECONDS=300

# Default Preferences
DEFAULT_COURSE_STYLE=professional
DEFAULT_COURSE_TONE=engaging
DEFAULT_BOOK_STYLE=academic
DEFAULT_CITATION_STYLE=APA
```

## 🚀 Quick Start

### Validation and Testing
```bash
# First, validate your setup
python run_tests.py

# Run quick validation only
python run_tests.py --quick

# Test specific components
python run_tests.py --course-only
python run_tests.py --book-only
python run_tests.py --api-only
```

### Command Line Usage

#### Create a Course
```bash
python course_agent.py "Introduction to Python Programming" \
  --style professional \
  --tone engaging \
  --depth beginner \
  --target-audience "programming beginners" \
  --duration "6 hours" \
  --verbose
```

#### Generate a Book
```bash
python book_agent.py "The Future of AI" \
  --research-data research_files/ \
  --style academic \
  --citation-style APA \
  --target-audience "general readers" \
  --estimated-pages 200 \
  --verbose
```

### API Server
```bash
# Start the API server
python api_server.py

# Server will be available at http://localhost:8000
# API documentation at http://localhost:8000/docs
# Health check: http://localhost:8000/health
```

### n8n Integration

1. Import the workflow files:
   - `workflows/course_creation_workflow.json`
   - `workflows/book_generation_workflow.json`

2. Configure environment variables in n8n:
   ```
   MPAF_ENDPOINT=http://your-api-server:8000
   MPAF_API_KEY=your_api_key
   GOOGLE_SHEET_ID=your_sheet_id
   GOOGLE_DRIVE_FOLDER_ID=your_folder_id
   DEFAULT_USER_EMAIL=<EMAIL>
   ```

3. Set up credentials:
   - SMTP for email notifications
   - Google Sheets API
   - Google Drive API

## 📚 API Documentation

### Course Creation Endpoints

#### Generate Course Outline
```http
POST /course/outline
Content-Type: application/json

{
  "course_title": "Course Title",
  "style": "professional",
  "tone": "engaging",
  "depth": "intermediate",
  "target_audience": "developers",
  "duration": "4 hours"
}
```

#### Generate Course Scripts
```http
POST /course/scripts
Content-Type: application/json

{
  "outline": { /* course outline object */ },
  "style": "professional",
  "tone": "engaging"
}
```

#### Create Videos
```http
POST /course/videos
Content-Type: application/json

{
  "scripts": [ /* array of script objects */ ],
  "avatar_id": "default",
  "voice_id": "default",
  "video_quality": "720p"
}
```

#### Full Course Creation
```http
POST /course/create
Content-Type: application/json

{
  "course_title": "Complete Course",
  "style": "professional",
  "tone": "engaging",
  "depth": "intermediate",
  "target_audience": "students",
  "duration": "6 hours"
}
```

### Book Generation Endpoints

#### Process Research Data
```http
POST /book/research
Content-Type: application/json

{
  "research_data": "file_path_or_content",
  "book_title": "Book Title"
}
```

#### Generate Book Outline
```http
POST /book/outline
Content-Type: application/json

{
  "book_title": "Book Title",
  "style": "academic",
  "citation_style": "APA",
  "target_audience": "researchers",
  "estimated_pages": 200
}
```

#### Generate Chapters
```http
POST /book/chapters
Content-Type: application/json

{
  "outline": { /* book outline object */ },
  "style": "academic",
  "citation_style": "APA",
  "include_citations": true
}
```

#### Compile Bibliography
```http
POST /book/bibliography
Content-Type: application/json

{
  "citation_style": "APA",
  "include_annotations": false,
  "research_data": { /* research data object */ },
  "chapters": [ /* array of chapter objects */ ]
}
```

#### Full Book Generation
```http
POST /book/generate
Content-Type: application/json

{
  "book_title": "Complete Book",
  "research_data": "research_content",
  "style": "academic",
  "citation_style": "APA",
  "target_audience": "general readers",
  "estimated_pages": 200
}
```

### Additional Endpoints

#### Generate Q&A Content
```http
POST /course/qa
Content-Type: application/json

{
  "outline": { /* course outline object */ },
  "scripts": [ /* array of script objects */ ],
  "qa_count": 5,
  "include_audio": true,
  "include_video": false
}
```

#### Task Status
```http
GET /task/{task_id}
```

#### Health Check
```http
GET /health
```

#### API Information
```http
GET /
```

## 🧪 Testing

### Comprehensive Test Runner
```bash
# Run all tests with comprehensive validation
python run_tests.py

# Quick validation (prerequisites + integration)
python run_tests.py --quick

# Test specific components
python run_tests.py --course-only    # Course creation tools
python run_tests.py --book-only      # Book generation tools
python run_tests.py --api-only       # API endpoints
python run_tests.py --pytest-only    # Pytest suite only
```

### Individual Test Suites
```bash
# Course tools tests
python tests/test_course_tools.py

# Book tools tests
python tests/test_book_tools.py

# API endpoint tests
python tests/test_api_endpoints.py
```

### Run with pytest
```bash
pip install pytest pytest-asyncio
pytest tests/ -v
```

### Test Coverage
The test suite covers:
- ✅ All MPAF tools (course and book generation)
- ✅ API endpoints with various scenarios
- ✅ Error handling and edge cases
- ✅ Integration between components
- ✅ n8n workflow validation
- ✅ Environment configuration validation

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        n8n Workflows                           │
│  ┌─────────────────┐           ┌─────────────────┐             │
│  │ Course Creation │           │ Book Generation │             │
│  │    Workflow     │           │    Workflow     │             │
│  └─────────────────┘           └─────────────────┘             │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                      MPAF API Server                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Course API    │  │    Book API     │  │  Background     │ │
│  │   Endpoints     │  │   Endpoints     │  │     Tasks       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                      MPAF Framework                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Course Tools   │  │   Book Tools    │  │  Agent Runners  │ │
│  │                 │  │                 │  │                 │ │
│  │ • Outline Gen   │  │ • Research Proc │  │ • Course Agent  │ │
│  │ • Script Gen    │  │ • Outline Gen   │  │ • Book Agent    │ │
│  │ • Video Creator │  │ • Chapter Gen   │  │                 │ │
│  │ • Q&A Generator │  │ • Bibliography  │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                    External Services                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   AI Providers  │  │ Media Services  │  │ Cloud Services  │ │
│  │                 │  │                 │  │                 │ │
│  │ • Claude        │  │ • HeyGen        │  │ • Google Drive  │ │
│  │ • OpenAI        │  │ • ElevenLabs    │  │ • Google Sheets │ │
│  │ • Gemini        │  │                 │  │ • AWS S3        │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### MPAF Framework Components
- **Base Tool**: Abstract base class with retry logic, error handling, and AI integration
- **Models**: Comprehensive data structures for state management and content tracking
- **Settings**: Environment-based configuration with validation and multi-provider support
- **Tools**: Specialized tools for each function with async processing

### Tool Categories

#### Course Creation Tools
- `CourseOutlineGenerator`: AI-powered course structure creation with modules and lessons
- `ScriptGenerator`: Detailed lesson scripts with learning objectives and stage directions
- `VideoCreator`: HeyGen API integration for avatar-based video generation
- `QAGenerator`: Interactive Q&A content with multiple formats (text, audio, video)

#### Book Generation Tools
- `ResearchProcessor`: Multi-format research data processing (PDF, text, archives)
- `BookOutlineGenerator`: Structured chapter and section planning with research integration
- `ChapterGenerator`: Full chapter content with citations and research integration
- `BibliographyCompiler`: Automated reference formatting with multiple citation styles

### Agent Runners
- `CourseCreationAgent`: Orchestrates complete course creation pipeline with state management
- `BookGenerationAgent`: Manages full book generation workflow with research integration

### API Layer
- FastAPI server with async endpoints and automatic documentation
- Background task processing with status tracking and progress updates
- Pydantic models for request/response validation
- Comprehensive error handling, logging, and retry logic

## 🔧 Configuration

### AI Provider Configuration
The system automatically selects the best available AI provider:
1. Claude (Anthropic) - preferred for quality
2. OpenAI GPT-4 - good balance of speed and quality
3. Google Gemini - fast and cost-effective

### Video Generation
- Requires HeyGen API key
- Supports multiple avatar and voice options
- Configurable video quality (720p, 1080p)

### Audio Generation
- Requires ElevenLabs API key
- Used for Q&A audio content
- Configurable voice settings

## 📊 Monitoring and Logging

### Logging Levels
- `INFO`: General operation information
- `WARNING`: Non-critical issues
- `ERROR`: Error conditions
- `DEBUG`: Detailed debugging information

### Task Tracking
- Background tasks store status in memory
- Production deployments should use Redis/database
- Task status includes progress percentage and messages

## 🚀 Deployment

### Local Development
```bash
# Validate setup first
python run_tests.py --quick

# Start API server
python api_server.py

# Server available at http://localhost:8000
# API docs at http://localhost:8000/docs
```

### Production Deployment
1. Use a production ASGI server (uvicorn with workers)
2. Set up reverse proxy (nginx) with SSL/TLS
3. Configure environment variables securely
4. Set up monitoring, logging, and health checks
5. Use Redis/database for persistent task storage
6. Implement proper backup and recovery procedures

### Docker Deployment
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["uvicorn", "api_server:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  n8n-mpaf-app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - CLAUDE_API_KEY=${CLAUDE_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./data:/app/data
    restart: unless-stopped
```

### n8n Integration
1. Deploy API server to accessible endpoint
2. Import workflow JSON files from `workflows/` directory
3. Configure environment variables in n8n
4. Set up required credentials (SMTP, Google APIs)
5. Test workflows with sample data
6. Monitor workflow execution and error handling

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📁 Project Structure

```
n8n-coursecreator/
├── config/
│   └── settings.py              # Environment configuration
├── src/
│   └── models.py               # Data models and structures
├── tools/
│   ├── base_tool.py            # Abstract base tool class
│   ├── course_outline_generator.py
│   ├── script_generator.py
│   ├── video_creator.py
│   ├── qa_generator.py
│   ├── research_processor.py
│   ├── book_outline_generator.py
│   ├── chapter_generator.py
│   └── bibliography_compiler.py
├── workflows/
│   ├── course_creation_workflow.json
│   └── book_generation_workflow.json
├── tests/
│   ├── test_course_tools.py
│   ├── test_book_tools.py
│   └── test_api_endpoints.py
├── course_agent.py             # Course creation agent runner
├── book_agent.py              # Book generation agent runner
├── api_server.py              # FastAPI server
├── run_tests.py               # Comprehensive test runner
├── requirements.txt           # Python dependencies
├── .env.example              # Environment template
├── README.md                 # This file
└── DEPLOYMENT.md             # Deployment guide
```

## 🆘 Support

For support and questions:
1. **Documentation**: Check README.md and DEPLOYMENT.md
2. **Validation**: Run `python run_tests.py` to verify setup
3. **Logs**: Check application logs for error details
4. **API Responses**: Review error messages in API responses
5. **Environment**: Verify all required API keys are configured
6. **Dependencies**: Ensure all requirements are installed

### Common Issues
- **API Key Errors**: Verify at least one AI API key is configured
- **Import Errors**: Run `pip install -r requirements.txt`
- **Connection Issues**: Check network connectivity to AI services
- **Permission Errors**: Verify Google Cloud credentials and permissions

## 🔮 Future Enhancements

### Planned Features
- **Multi-language Support**: Content generation in multiple languages
- **Advanced Video Editing**: Custom transitions, effects, and branding
- **Interactive Course Elements**: Quizzes, simulations, and hands-on exercises
- **Real-time Collaboration**: Multi-user editing and review workflows
- **Advanced Analytics**: Detailed usage metrics and performance tracking
- **LMS Integration**: Direct integration with popular learning management systems

### Technical Improvements
- **Caching Layer**: Redis-based caching for improved performance
- **Database Integration**: Persistent storage for content and user data
- **Advanced AI Models**: Support for latest AI models and fine-tuning
- **Batch Processing**: Optimized handling of large-scale content generation
- **API Rate Limiting**: Advanced rate limiting and quota management
- **Webhook Support**: Real-time notifications and integrations

## 🎯 Getting Started Checklist

### 1. Initial Setup
- [ ] Clone the repository
- [ ] Install Python 3.8+
- [ ] Run `pip install -r requirements.txt`
- [ ] Copy `.env.example` to `.env`
- [ ] Configure at least one AI API key

### 2. Validation
- [ ] Run `python run_tests.py --quick` to validate setup
- [ ] Test individual components if needed
- [ ] Verify API server starts: `python api_server.py`
- [ ] Check health endpoint: `curl http://localhost:8000/health`

### 3. First Content Generation
- [ ] Create a simple course: `python course_agent.py "Test Course" --verbose`
- [ ] Generate a book outline via API
- [ ] Test n8n workflow integration (if using n8n)

### 4. Production Deployment
- [ ] Review [DEPLOYMENT.md](DEPLOYMENT.md) for detailed instructions
- [ ] Set up monitoring and logging
- [ ] Configure backup procedures
- [ ] Test with production data

## 🔧 Troubleshooting

### Environment Issues
```bash
# Check Python version
python --version  # Should be 3.8+

# Verify dependencies
pip list | grep -E "(fastapi|uvicorn|pydantic|aiohttp)"

# Test AI API connectivity
python -c "from config.settings import settings; print(settings.validate_required_settings())"
```

### API Issues
```bash
# Test API server
curl -X GET http://localhost:8000/health

# Test course outline generation
curl -X POST http://localhost:8000/course/outline \
  -H "Content-Type: application/json" \
  -d '{"course_title": "Test Course", "style": "professional"}'
```

### Common Error Solutions
- **ModuleNotFoundError**: Run `pip install -r requirements.txt`
- **API Key Error**: Verify environment variables in `.env`
- **Connection Timeout**: Check internet connectivity and API service status
- **Permission Denied**: Verify file permissions and Google Cloud credentials
- **Port Already in Use**: Change port in `api_server.py` or kill existing process

---

**Ready to revolutionize content creation? Start generating courses and books today!** 🚀
