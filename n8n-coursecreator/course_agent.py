#!/usr/bin/env python3
"""
Course Creation Agent Runner
Handles course creation workflow including outline generation, script creation, video generation, and Q&A creation.
"""

import asyncio
import argparse
import sys
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

from config.settings import settings
from src.models import AgentState, AgentStatus, ContentType, ProcessingStage
from tools.course_outline_generator import course_outline_generator
from tools.script_generator import script_generator
from tools.video_creator import video_creator
from tools.qa_generator import qa_generator

class CourseCreationAgent:
    def __init__(self, course_title: str, context: str = "", user_preferences: Dict[str, Any] = None, verbose: bool = False):
        self.state = AgentState(
            topic=course_title,
            content_type=ContentType.COURSE,
            context=context,
            user_preferences=user_preferences or {}
        )
        self.verbose = verbose
        self.logger = self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging for the agent"""
        logging.basicConfig(
            level=logging.INFO if self.verbose else logging.WARNING,
            format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
        )
        return logging.getLogger("CourseCreationAgent")
    
    async def run_pipeline(self) -> Dict[str, Any]:
        """Run the complete course creation pipeline"""
        self.state.status = AgentStatus.RUNNING
        self.logger.info(f"Starting course creation for: {self.state.topic}")
        
        try:
            # Stage 1: Generate Course Outline
            await self._run_outline_generation()
            
            # Stage 2: Generate Scripts
            await self._run_script_generation()
            
            # Stage 3: Create Videos (if configured)
            if settings.HEYGEN_API_KEY:
                await self._run_video_creation()
            else:
                self.logger.warning("HeyGen API key not configured - skipping video creation")
            
            # Stage 4: Generate Q&A Content
            await self._run_qa_generation()
            
            # Stage 5: Finalize
            await self._finalize_course()
            
            self.state.status = AgentStatus.COMPLETED
            self.logger.info("Course creation pipeline completed successfully")
            
        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.add_error(str(e))
            self.logger.error(f"Course creation pipeline failed: {e}")
        
        return self._prepare_output()
    
    async def _run_outline_generation(self):
        """Generate course outline"""
        self.state.update_stage(ProcessingStage.OUTLINE_GENERATION)
        self.logger.info("Generating course outline...")
        
        result = await course_outline_generator.execute(
            self.state,
            course_title=self.state.topic,
            **self.state.user_preferences
        )
        
        if not result.success:
            raise Exception(f"Outline generation failed: {result.error}")
        
        self.logger.info(f"Generated outline with {result.data['summary']['modules']} modules")
    
    async def _run_script_generation(self):
        """Generate scripts for all lessons"""
        self.state.update_stage(ProcessingStage.CONTENT_GENERATION)
        self.logger.info("Generating lesson scripts...")
        
        result = await script_generator.execute(self.state, **self.state.user_preferences)
        
        if not result.success:
            raise Exception(f"Script generation failed: {result.error}")
        
        self.logger.info(f"Generated {result.data['summary']['total_scripts']} scripts")
    
    async def _run_video_creation(self):
        """Create videos from scripts"""
        self.state.update_stage(ProcessingStage.MULTIMEDIA_GENERATION)
        self.logger.info("Creating videos from scripts...")
        
        result = await video_creator.execute(self.state, **self.state.user_preferences)
        
        if not result.success:
            self.logger.warning(f"Video creation failed: {result.error}")
            return
        
        self.logger.info(f"Created {result.data['summary']['videos_created']} videos")
    
    async def _run_qa_generation(self):
        """Generate Q&A content"""
        self.state.update_stage(ProcessingStage.MULTIMEDIA_GENERATION)
        self.logger.info("Generating Q&A content...")
        
        result = await qa_generator.execute(self.state, **self.state.user_preferences)
        
        if not result.success:
            self.logger.warning(f"Q&A generation failed: {result.error}")
            return
        
        self.logger.info(f"Generated {result.data['summary']['total_qa_pairs']} Q&A pairs")
    
    async def _finalize_course(self):
        """Finalize course creation"""
        self.state.update_stage(ProcessingStage.FINALIZATION)
        self.logger.info("Finalizing course...")
        
        # Compile final output
        outline_items = self.state.get_content_by_type(ContentType.OUTLINE)
        script_items = self.state.get_content_by_type(ContentType.SCRIPT)
        video_items = self.state.get_content_by_type(ContentType.VIDEO)
        qa_items = self.state.get_content_by_type(ContentType.QA)
        
        self.state.final_output = {
            "course_title": self.state.outline.title if self.state.outline else self.state.topic,
            "outline": self.state.outline.to_dict() if self.state.outline else None,
            "content_summary": {
                "outlines": len(outline_items),
                "scripts": len([item for item in script_items if item.metadata.get("type") != "summary"]),
                "videos": len(video_items),
                "qa_modules": len(qa_items)
            },
            "content_items": [item.to_dict() for item in self.state.content_items],
            "created_at": self.state.created_at.isoformat(),
            "completed_at": datetime.now().isoformat()
        }
    
    def _prepare_output(self) -> Dict[str, Any]:
        """Prepare final output for API response"""
        return {
            "status": self.state.status.value,
            "course_title": self.state.topic,
            "outline": self.state.outline.to_dict() if self.state.outline else None,
            "final_output": self.state.final_output,
            "summary": {
                "modules": len(self.state.outline.modules) if self.state.outline else 0,
                "total_scripts": len([item for item in self.state.get_content_by_type(ContentType.SCRIPT) if item.metadata.get("type") != "summary"]),
                "videos_created": len(self.state.get_content_by_type(ContentType.VIDEO)),
                "qa_modules": len(self.state.get_content_by_type(ContentType.QA)),
                "total_content_items": len(self.state.content_items)
            },
            "errors": self.state.error_log,
            "processing_time": (datetime.now() - self.state.created_at).total_seconds(),
            "state_data": self.state.to_dict()
        }
    
    def present_output(self):
        """Present output to console"""
        print("\n" + "="*60)
        print("COURSE CREATION AGENT - RUN COMPLETE")
        print("="*60)
        print(f"Status: {self.state.status.value}")
        print(f"Course: {self.state.topic}")
        print(f"Processing Stage: {self.state.stage.value}")
        
        if self.state.status == AgentStatus.COMPLETED:
            print("\n--- COURSE SUMMARY ---")
            if self.state.outline:
                print(f"Title: {self.state.outline.title}")
                print(f"Modules: {len(self.state.outline.modules)}")
                print(f"Target Audience: {self.state.outline.target_audience}")
                print(f"Estimated Duration: {self.state.outline.estimated_duration}")
            
            print(f"\n--- CONTENT GENERATED ---")
            print(f"Scripts: {len([item for item in self.state.get_content_by_type(ContentType.SCRIPT) if item.metadata.get('type') != 'summary'])}")
            print(f"Videos: {len(self.state.get_content_by_type(ContentType.VIDEO))}")
            print(f"Q&A Modules: {len(self.state.get_content_by_type(ContentType.QA))}")
            print(f"Total Content Items: {len(self.state.content_items)}")
            
        else:
            print("\n--- ERRORS ---")
            for error in self.state.error_log:
                print(f"  {error}")
        
        print(f"\nProcessing Time: {(datetime.now() - self.state.created_at).total_seconds():.2f} seconds")
        print("="*60)

def create_parser():
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(description="Course Creation Agent")
    parser.add_argument("course_title", help="The title of the course to create")
    parser.add_argument("--context", "-c", default="", help="Additional context for course creation")
    parser.add_argument("--style", default="professional", help="Course style (professional, casual, academic)")
    parser.add_argument("--tone", default="engaging", help="Course tone (engaging, formal, conversational)")
    parser.add_argument("--depth", default="intermediate", help="Course depth (beginner, intermediate, advanced)")
    parser.add_argument("--target-audience", default="general audience", help="Target audience description")
    parser.add_argument("--duration", default="4-6 hours", help="Estimated course duration")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable detailed logging")
    return parser

async def main():
    """Main entry point"""
    # Validate settings
    if not settings.validate_required_settings():
        sys.exit(1)
    
    parser = create_parser()
    args = parser.parse_args()
    
    # Prepare user preferences
    user_preferences = {
        "style": args.style,
        "tone": args.tone,
        "depth": args.depth,
        "target_audience": args.target_audience,
        "duration": args.duration
    }
    
    # Create and run agent
    agent = CourseCreationAgent(
        course_title=args.course_title,
        context=args.context,
        user_preferences=user_preferences,
        verbose=args.verbose
    )
    
    result = await agent.run_pipeline()
    agent.present_output()
    
    # Return appropriate exit code
    sys.exit(0 if result["status"] == "completed" else 1)

if __name__ == "__main__":
    asyncio.run(main())
