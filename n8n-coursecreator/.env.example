# n8n-MPAF Course Creator & Book Generator Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# AI API KEYS (At least one required)
# =============================================================================

# Claude API (Anthropic) - Recommended for best quality
CLAUDE_API_KEY=your_claude_api_key_here

# OpenAI API - Good balance of speed and quality
OPENAI_API_KEY=your_openai_api_key_here

# Google Gemini API - Fast and cost-effective
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# VIDEO GENERATION (Optional)
# =============================================================================

# HeyGen API for avatar video generation
HEYGEN_API_KEY=your_heygen_api_key_here

# =============================================================================
# AUDIO GENERATION (Optional)
# =============================================================================

# ElevenLabs API for text-to-speech
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# =============================================================================
# CLOUD STORAGE (Optional)
# =============================================================================

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
S3_BUCKET=your_s3_bucket_name

# =============================================================================
# GOOGLE SERVICES (Optional)
# =============================================================================

# Path to Google Cloud service account credentials JSON file
GOOGLE_CREDENTIALS_PATH=path/to/your/google-credentials.json

# =============================================================================
# MODEL CONFIGURATION
# =============================================================================

# Primary agent model (for Gemini)
AGENT_MODEL=gemini-1.5-flash

# OpenAI model selection
OPENAI_MODEL=gpt-4o-mini

# Claude model selection
CLAUDE_MODEL=claude-3-5-sonnet-********

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Debug mode (true/false)
DEBUG=false

# Maximum retry attempts for API calls
MAX_RETRIES=3

# Timeout for API requests (seconds)
TIMEOUT_SECONDS=300

# =============================================================================
# DEFAULT PREFERENCES
# =============================================================================

# Course Creation Defaults
DEFAULT_COURSE_STYLE=professional
DEFAULT_COURSE_TONE=engaging
DEFAULT_COURSE_DEPTH=intermediate

# Book Generation Defaults
DEFAULT_BOOK_STYLE=academic
DEFAULT_CITATION_STYLE=APA

# =============================================================================
# n8n INTEGRATION SETTINGS
# =============================================================================

# API endpoint for n8n workflows (when deployed)
MPAF_ENDPOINT=http://localhost:8000

# API key for securing endpoints (optional)
MPAF_API_KEY=your_secure_api_key

# Default user email for notifications
DEFAULT_USER_EMAIL=<EMAIL>

# Google Sheets ID for logging (optional)
GOOGLE_SHEET_ID=your_google_sheet_id

# Google Drive folder ID for file storage (optional)
GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id

# Default avatar and voice IDs for video generation
DEFAULT_AVATAR_ID=default_avatar_id
DEFAULT_VOICE_ID=default_voice_id

# =============================================================================
# ADVANCED SETTINGS
# =============================================================================

# Custom API base URLs (if using proxies or custom endpoints)
# OPENAI_BASE_URL=https://api.openai.com/v1
# CLAUDE_BASE_URL=https://api.anthropic.com/v1
# GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta

# Rate limiting settings
# RATE_LIMIT_REQUESTS_PER_MINUTE=60
# RATE_LIMIT_TOKENS_PER_MINUTE=100000

# Cache settings (for future implementation)
# ENABLE_CACHE=true
# CACHE_TTL_SECONDS=3600

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# JWT secret for API authentication (if implementing auth)
# JWT_SECRET=your_jwt_secret_key

# CORS origins (comma-separated)
# CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Log file path (optional)
# LOG_FILE=logs/application.log

# =============================================================================
# DATABASE CONFIGURATION (Future)
# =============================================================================

# Database URL for persistent storage (future implementation)
# DATABASE_URL=postgresql://user:password@localhost/dbname

# Redis URL for task queue (future implementation)
# REDIS_URL=redis://localhost:6379/0
