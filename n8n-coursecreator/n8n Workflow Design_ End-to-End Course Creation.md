## n8n Workflow Design: End-to-End Course Creation

This document details the proposed design for an end-to-end n8n workflow to automate the course creation process, as outlined in `detailed_requirements.md` and analyzed in `n8n_workflow_analysis.md`.

### Workflow Overview

The workflow aims to guide a user from providing a course title to receiving a complete package of course materials, including outlines, scripts, avatar videos, Q&A bot content, and reference materials. It will involve LLM integrations for content generation, potential API calls to services like Heygen for video, and user interaction points for approvals and feedback.

### Phase 1: Initiation and Outline Generation

1.  **Trigger & Input (Course Title):**
    *   **Nodes:** `Manual Trigger` / `Webhook Trigger` / `Form Trigger` (e.g., a simple form asking for "Course Title").
    *   **Action:** The workflow starts when the user provides a course title.
    *   **Data Output:** Course Title.

2.  **Generate Initial Course Plan & Outline:**
    *   **Nodes:** `Set` (to prepare the prompt for the LLM), `HTTP Request` (to an LLM API like OpenAI, Anthropic, or a self-hosted model).
    *   **Configuration:** The `HTTP Request` node will send the course title along with a structured prompt requesting a course plan (learning objectives, target audience) and a detailed outline (modules, lessons/topics).
    *   **Data Output:** Structured JSON or text containing the course plan and outline.

3.  **Present Outline for User Review:**
    *   **Nodes:** `Set` (to format the outline for presentation), `Email Node` / `Slack Node` / `Google Sheets Node` (to write the outline to a sheet for review) / `HTTP Request` (to a custom front-end or user interface if available).
    *   **Action:** The generated outline is sent to the user for review. The message should include instructions on how to provide feedback (e.g., reply to email, update a specific Google Sheet, use a feedback form).
    *   **Data Output:** Notification sent, link to reviewable outline.

### Phase 2: Outline Refinement (Iterative Feedback Loop)

4.  **Wait for User Feedback/Approval:**
    *   **Nodes:** `Wait Node` (with a timeout) OR a separate `Webhook Trigger` that the user calls after reviewing (e.g., by submitting a feedback form that triggers the webhook).
    *   **Action:** The workflow pauses until the user provides feedback or approval. If using a `Wait` node, a mechanism to check for feedback (e.g., checking a specific email inbox or a Google Sheet cell) would be needed in a subsequent step.

5.  **Receive and Process Feedback:**
    *   **Nodes:** If using email, `Email Read IMAP/POP3 Node`. If Google Sheets, `Google Sheets Node` (to read feedback). If a form, the webhook in step 4 receives the data directly. `Set` or `Function Node` to parse the feedback.
    *   **Action:** The workflow ingests the user's feedback.
    *   **Data Output:** Parsed feedback, approval status.

6.  **Conditional Update of Outline:**
    *   **Nodes:** `IF Node`.
    *   **Action:** Checks if feedback requires changes. 
        *   **If Changes Needed:** Loop back to an `HTTP Request` node (LLM) with the original outline and the user's feedback to generate a revised outline. Then, return to Step 3 (Present Outline for User Review).
        *   **If Approved:** Proceed to Script Generation (Phase 3).
    *   **Data Output:** Approved and finalized course plan and outline.

### Phase 3: Script Generation

7.  **Generate Scripts per Outline Item:**
    *   **Nodes:** `SplitInBatches Node` (to process each lesson/topic from the approved outline individually), `Set` (to prepare prompts for each script), `HTTP Request` (to LLM API for script generation for each item), `Merge Node` (if processing in batches, to bring results together, or simply collect results iteratively).
    *   **Configuration:** For each lesson/topic, a prompt is sent to the LLM requesting a detailed script, considering user-defined style, tone, and depth (these preferences could be collected at the start or assumed).
    *   **Data Output:** A collection of scripts, one for each outline item.

8.  **Present Scripts for User Review (Optional but Recommended):**
    *   **Nodes:** Similar to Step 3 (Email, Slack, Google Sheets).
    *   **Action:** Scripts are sent to the user for a final review before video generation. A simpler approval mechanism might be used here (e.g., a single approval for all scripts).
    *   **Feedback Loop (Optional):** A simplified version of Phase 2 could be implemented if script revisions are common.
    *   **Data Output:** Approved scripts.

### Phase 4: Multimedia Content Generation

9.  **Create Avatar Videos (e.g., Heygen Integration):**
    *   **Nodes:** `SplitInBatches Node` (to process each script), `HTTP Request` (to Heygen API - **this requires the user to have a Heygen API key and for the API to be accessible and understood**). `Wait Node` (Heygen video generation can be asynchronous; the workflow might need to poll for completion or use a webhook callback from Heygen if available). `HTTP Request` (to download the video once ready) or `Set` (to store the video URL).
    *   **Configuration:** Each approved script is sent to the Heygen API. Parameters like avatar choice, voice, etc., would need to be pre-configured or dynamically set if the API allows.
    *   **Error Handling:** Crucial here, as API calls can fail. Use `Error Trigger` or check response status codes.
    *   **Data Output:** Collection of video files (or links to them), organized by lesson/topic.

10. **Generate Q&A Content (Text):**
    *   **Nodes:** `SplitInBatches` (per module), `HTTP Request` (to LLM to generate potential Q&A pairs based on module scripts/content).
    *   **Action:** For each module, generate a set of relevant questions and their answers.
    *   **Data Output:** Structured Q&A pairs (text) for each module.

11. **Create Q&A Audio & Video Snippets:**
    *   **Nodes:** 
        *   **Audio:** `SplitInBatches` (for each Q&A pair), `HTTP Request` (to a Text-to-Speech API like Google TTS, ElevenLabs - requires API key) to convert answers to audio. `Write Binary File` or cloud storage upload.
        *   **Video:** `SplitInBatches` (for each Q&A pair), `HTTP Request` (to Heygen API, similar to step 9, but with Q&A text as input for short video snippets). `Wait Node`, download/link storage.
    *   **Action:** Convert Q&A text into audio files and short avatar video snippets.
    *   **Data Output:** Audio files and video snippet files (or links) for Q&A, organized by module.

### Phase 5: Finalization and Delivery

12. **Compile Reference Materials:**
    *   **Nodes:** `Set` (if user provides materials as text), `Read File` (if user uploads files), `HTTP Request` (LLM to suggest additional resources if desired). `Write File` (to create a formatted reference document, e.g., Markdown).
    *   **Action:** Gathers user-provided references and potentially auto-generates additional ones. Formats them into a single document.
    *   **Data Output:** Reference material document(s).

13. **Package All Deliverables:**
    *   **Nodes:** `Move/Copy File Nodes` (to gather all generated files - outlines, scripts, videos, Q&A media, reference doc - into a structured temporary directory if not already in cloud storage). `Zip Node` (to create an archive). `Google Drive Node` / `Dropbox Node` / `S3 Node` (to upload the final package).
    *   **Action:** All course components are collected and packaged.
    *   **Data Output:** A zip file or a link to a cloud storage folder containing all materials.

14. **Notify User and Deliver:**
    *   **Nodes:** `Email Node` / `Slack Node`.
    *   **Action:** Sends a final notification to the user with the download link for the course package or the attached zip file.
    *   **Data Output:** Final notification sent.

### General Error Handling Strategy:

*   **Throughout the workflow:** Connect an `Error Trigger` node to critical steps (especially API calls like LLM, Heygen, TTS).
*   **Error Path:** The `Error Trigger` can lead to a sequence of nodes that:
    *   Log the error details (`Set` node, then `Google Sheets` or a logging service).
    *   Notify the user or an administrator about the failure (`Email Node`, `Slack Node`).
    *   Potentially offer a retry mechanism for transient errors (`Wait Node` then loop back, with a max retry count).

This design provides a comprehensive structure for the n8n course creation workflow. Specific node configurations will depend on the user's chosen services, API keys, and n8n environment.
