from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState, BookOutline, ContentType, ContentItem
from config.settings import settings
import json
import uuid
from typing import Dict, Any, Optional

class BookOutlineGenerator(BaseTool):
    def __init__(self):
        super().__init__(
            name="book_outline_generator",
            description="Generates comprehensive book outlines based on research data and book theme."
        )
    
    async def execute(self, state: AgentState, book_title: str, **kwargs) -> ToolResult:
        """Generate a book outline based on the book title and research data"""
        error = self.validate_required_params(["book_title"], book_title=book_title)
        if error:
            return ToolResult.error_result(error)
        
        self.logger.info(f"Generating book outline for: {book_title}")
        
        try:
            # Extract user preferences and research data
            style = kwargs.get("style", state.user_preferences.get("style", settings.DEFAULT_BOOK_STYLE))
            citation_style = kwargs.get("citation_style", state.user_preferences.get("citation_style", settings.DEFAULT_CITATION_STYLE))
            target_audience = kwargs.get("target_audience", state.user_preferences.get("target_audience", "general readers"))
            estimated_pages = kwargs.get("estimated_pages", state.user_preferences.get("estimated_pages", 200))
            
            # Get research analysis if available
            research_analysis = state.research_data.get("research_analysis", {})
            processed_research = state.research_data.get("processed_research", {})
            
            # Prepare the prompt
            system_prompt = self._get_system_prompt()
            user_prompt = self._get_user_prompt(
                book_title, style, citation_style, target_audience, 
                estimated_pages, research_analysis, processed_research, state.context
            )
            
            # Make API request
            payload = self.prepare_ai_prompt(system_prompt, user_prompt, self._build_research_context(research_analysis, processed_research))
            headers = self.get_ai_headers()
            url = self.get_ai_url()
            
            response = await self.make_api_request(url, "POST", headers, payload)
            
            if not response.success:
                return ToolResult.error_result(f"AI API request failed: {response.error}")
            
            # Extract and parse the response
            outline_text = self.extract_ai_response(response.data)
            outline_data = self._parse_outline_response(outline_text)
            
            if not outline_data:
                return ToolResult.error_result("Failed to parse book outline from AI response")
            
            # Create BookOutline object
            book_outline = BookOutline(
                title=outline_data.get("title", book_title),
                subtitle=outline_data.get("subtitle", ""),
                description=outline_data.get("description", ""),
                main_argument=outline_data.get("main_argument", ""),
                target_audience=outline_data.get("target_audience", target_audience),
                chapters=outline_data.get("chapters", []),
                estimated_pages=outline_data.get("estimated_pages", estimated_pages)
            )
            
            # Create content item
            content_item = ContentItem(
                id=str(uuid.uuid4()),
                type=ContentType.OUTLINE,
                title=f"Book Outline: {book_title}",
                content=json.dumps(book_outline.to_dict(), indent=2),
                metadata={
                    "style": style,
                    "citation_style": citation_style,
                    "chapter_count": len(book_outline.chapters),
                    "estimated_pages": book_outline.estimated_pages,
                    "has_research_data": bool(research_analysis or processed_research)
                }
            )
            
            # Update state
            state.outline = book_outline
            state.add_content_item(content_item)
            
            self.logger.info(f"Successfully generated book outline with {len(book_outline.chapters)} chapters")
            
            return ToolResult.success_result(
                {
                    "outline": book_outline.to_dict(),
                    "content_item_id": content_item.id,
                    "summary": {
                        "chapters": len(book_outline.chapters),
                        "estimated_pages": book_outline.estimated_pages,
                        "main_argument": book_outline.main_argument[:100] + "..." if len(book_outline.main_argument) > 100 else book_outline.main_argument
                    }
                },
                metadata={"tool": self.name, "book_title": book_title}
            )
            
        except Exception as e:
            error_msg = f"Error generating book outline: {str(e)}"
            self.logger.error(error_msg)
            return ToolResult.error_result(error_msg)
    
    def _get_system_prompt(self) -> str:
        return """You are an expert book editor and publishing consultant with extensive experience in creating compelling book outlines. Your task is to generate comprehensive, well-structured book outlines that provide clear roadmaps for authors.

When creating a book outline, you should:
1. Develop a compelling main argument or thesis
2. Structure content into logical chapters and sections
3. Ensure progressive development of ideas
4. Consider the target audience's needs and background
5. Balance depth with accessibility
6. Include practical applications where relevant
7. Provide realistic page estimates

Return your response as a valid JSON object with the following structure:
{
    "title": "Book Title",
    "subtitle": "Book Subtitle (if applicable)",
    "description": "Compelling book description (2-3 paragraphs)",
    "main_argument": "The central thesis or argument of the book",
    "target_audience": "Description of the intended readers",
    "estimated_pages": 250,
    "chapters": [
        {
            "chapter_number": 1,
            "title": "Chapter Title",
            "description": "Chapter description and purpose",
            "main_points": ["Point 1", "Point 2", "Point 3"],
            "estimated_pages": 20,
            "sections": [
                {
                    "section_number": 1,
                    "title": "Section Title",
                    "description": "Section description",
                    "key_concepts": ["Concept 1", "Concept 2"],
                    "estimated_pages": 5
                }
            ],
            "research_sources": ["Source 1", "Source 2"],
            "key_takeaways": ["Takeaway 1", "Takeaway 2"]
        }
    ],
    "appendices": ["Appendix A: Title", "Appendix B: Title"],
    "bibliography_notes": "Notes about required citations and sources"
}"""
    
    def _get_user_prompt(
        self, 
        book_title: str, 
        style: str, 
        citation_style: str, 
        target_audience: str, 
        estimated_pages: int,
        research_analysis: Dict[str, Any],
        processed_research: Dict[str, Any],
        context: str
    ) -> str:
        prompt = f"""Create a comprehensive book outline for: "{book_title}"

Book Requirements:
- Style: {style}
- Citation Style: {citation_style}
- Target Audience: {target_audience}
- Estimated Length: {estimated_pages} pages"""
        
        if context:
            prompt += f"\n- Additional Context: {context}"
        
        # Add research insights if available
        if research_analysis:
            prompt += f"\n\nResearch Insights Available:"
            if research_analysis.get("key_themes"):
                prompt += f"\n- Key Themes: {', '.join(research_analysis['key_themes'][:5])}"
            if research_analysis.get("main_concepts"):
                prompt += f"\n- Main Concepts: {', '.join(research_analysis['main_concepts'][:5])}"
            if research_analysis.get("potential_arguments"):
                prompt += f"\n- Potential Arguments: {', '.join(research_analysis['potential_arguments'][:3])}"
            if research_analysis.get("suggested_chapters"):
                prompt += f"\n- Suggested Chapter Topics: {', '.join(research_analysis['suggested_chapters'][:5])}"
        
        if processed_research:
            prompt += f"\n\nResearch Data Available:"
            prompt += f"\n- Total Sources: {processed_research.get('source_count', 0)}"
            prompt += f"\n- Total Words: {processed_research.get('total_words', 0)}"
        
        prompt += """

Please generate a detailed book outline that includes:
1. A compelling book description that would appeal to the target audience
2. A clear main argument or thesis that runs through the entire book
3. 8-15 chapters with logical progression
4. 2-5 sections per chapter with specific focus areas
5. Realistic page estimates for each chapter and section
6. Key takeaways for each chapter
7. Integration of available research data and themes
8. Consideration of the specified style and citation requirements

Ensure the outline:
- Provides a clear roadmap for writing the book
- Balances theoretical concepts with practical applications
- Considers the target audience's background and needs
- Creates a compelling narrative arc throughout the book
- Incorporates the available research effectively"""
        
        return prompt
    
    def _build_research_context(self, research_analysis: Dict[str, Any], processed_research: Dict[str, Any]) -> str:
        """Build context string from research data"""
        context_parts = []
        
        if research_analysis:
            if research_analysis.get("content_summary"):
                context_parts.append(f"Research Summary: {research_analysis['content_summary']}")
            
            if research_analysis.get("key_themes"):
                context_parts.append(f"Key Themes: {', '.join(research_analysis['key_themes'])}")
            
            if research_analysis.get("main_concepts"):
                context_parts.append(f"Main Concepts: {', '.join(research_analysis['main_concepts'])}")
        
        if processed_research and processed_research.get("sources"):
            # Add sample content from sources
            sample_content = []
            for source in processed_research["sources"][:3]:  # First 3 sources
                content_preview = source["content"][:200] + "..." if len(source["content"]) > 200 else source["content"]
                sample_content.append(f"{source['filename']}: {content_preview}")
            
            if sample_content:
                context_parts.append(f"Sample Research Content:\n{chr(10).join(sample_content)}")
        
        return "\n\n".join(context_parts)
    
    def _parse_outline_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """Parse the AI response and extract book outline data"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # If no JSON found, try to parse as structured text
                return self._parse_text_outline(response_text)
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse JSON response: {e}")
            return self._parse_text_outline(response_text)
        except Exception as e:
            self.logger.error(f"Error parsing outline response: {e}")
            return None
    
    def _parse_text_outline(self, text: str) -> Dict[str, Any]:
        """Fallback method to parse text-based outline"""
        lines = text.strip().split('\n')
        
        outline = {
            "title": "Generated Book",
            "subtitle": "",
            "description": "Book generated from outline",
            "main_argument": "Main argument to be developed",
            "target_audience": "General readers",
            "estimated_pages": 200,
            "chapters": [],
            "appendices": [],
            "bibliography_notes": "Citations to be added during writing"
        }
        
        current_chapter = None
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Simple heuristics to identify structure
            if line.startswith('Chapter') or line.startswith('Ch.'):
                if current_chapter:
                    outline["chapters"].append(current_chapter)
                current_chapter = {
                    "chapter_number": len(outline["chapters"]) + 1,
                    "title": line,
                    "description": "",
                    "main_points": [],
                    "estimated_pages": 15,
                    "sections": [],
                    "research_sources": [],
                    "key_takeaways": []
                }
                current_section = None
            elif line.startswith('Section') or line.startswith('Part'):
                if current_chapter and current_section:
                    current_chapter["sections"].append(current_section)
                current_section = {
                    "section_number": len(current_chapter["sections"]) + 1 if current_chapter else 1,
                    "title": line,
                    "description": "",
                    "key_concepts": [],
                    "estimated_pages": 3
                }
            elif line.startswith('-') or line.startswith('•'):
                # Bullet point - add to current context
                point = line.lstrip('-•').strip()
                if current_section:
                    current_section["key_concepts"].append(point)
                elif current_chapter:
                    current_chapter["main_points"].append(point)
        
        # Add the last chapter and section
        if current_section and current_chapter:
            current_chapter["sections"].append(current_section)
        if current_chapter:
            outline["chapters"].append(current_chapter)
        
        return outline

book_outline_generator = BookOutlineGenerator()
