from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState, ContentType, ContentItem
from config.settings import settings
import json
import uuid
import asyncio
from typing import List, Dict, Any

class VideoCreator(BaseTool):
    def __init__(self):
        super().__init__(
            name="video_creator",
            description="Creates avatar videos using HeyGen API or other video generation services."
        )
    
    async def execute(self, state: AgentState, **kwargs) -> ToolResult:
        """Create videos for all scripts in the state"""
        if not settings.HEYGEN_API_KEY:
            return ToolResult.error_result("HeyGen API key not configured")
        
        # Get script content items
        script_items = state.get_content_by_type(ContentType.SCRIPT)
        if not script_items:
            return ToolResult.error_result("No scripts found to create videos from")
        
        self.logger.info(f"Creating videos for {len(script_items)} scripts")
        
        try:
            # Extract video preferences
            avatar_id = kwargs.get("avatar_id", state.user_preferences.get("avatar_id", "default"))
            voice_id = kwargs.get("voice_id", state.user_preferences.get("voice_id", "default"))
            video_quality = kwargs.get("video_quality", state.user_preferences.get("video_quality", "720p"))
            
            # Create videos for each script
            created_videos = []
            failed_videos = []
            
            for script_item in script_items:
                if script_item.metadata.get("type") == "summary":
                    continue  # Skip summary items
                
                video_result = await self._create_video_from_script(
                    script_item, avatar_id, voice_id, video_quality
                )
                
                if video_result.success:
                    created_videos.append(video_result.data)
                    
                    # Create content item for the video
                    video_item = ContentItem(
                        id=str(uuid.uuid4()),
                        type=ContentType.VIDEO,
                        title=f"Video: {script_item.title}",
                        content=json.dumps(video_result.data, indent=2),
                        metadata={
                            "script_id": script_item.id,
                            "module_number": script_item.metadata.get("module_number"),
                            "lesson_number": script_item.metadata.get("lesson_number"),
                            "video_url": video_result.data.get("video_url"),
                            "video_id": video_result.data.get("video_id"),
                            "duration_seconds": video_result.data.get("duration_seconds"),
                            "status": video_result.data.get("status")
                        }
                    )
                    
                    state.add_content_item(video_item)
                    video_result.data["content_item_id"] = video_item.id
                else:
                    failed_videos.append({
                        "script_id": script_item.id,
                        "script_title": script_item.title,
                        "error": video_result.error
                    })
                    self.logger.warning(f"Failed to create video for script: {script_item.title}")
            
            # Create summary
            summary = {
                "total_scripts": len(script_items),
                "videos_created": len(created_videos),
                "videos_failed": len(failed_videos),
                "success_rate": len(created_videos) / len(script_items) if script_items else 0,
                "failed_videos": failed_videos
            }
            
            self.logger.info(f"Video creation completed: {len(created_videos)} successful, {len(failed_videos)} failed")
            
            return ToolResult.success_result(
                {
                    "videos": created_videos,
                    "summary": summary
                },
                metadata={"tool": self.name, "total_videos": len(created_videos)}
            )
            
        except Exception as e:
            error_msg = f"Error creating videos: {str(e)}"
            self.logger.error(error_msg)
            return ToolResult.error_result(error_msg)
    
    async def _create_video_from_script(
        self, 
        script_item: ContentItem, 
        avatar_id: str, 
        voice_id: str, 
        video_quality: str
    ) -> ToolResult:
        """Create a video from a single script"""
        try:
            # Parse script content
            script_data = json.loads(script_item.content) if script_item.content.startswith('{') else {"script": script_item.content}
            script_text = script_data.get("script", script_item.content)
            
            # Clean script text (remove stage directions)
            cleaned_script = self._clean_script_text(script_text)
            
            # Prepare HeyGen API request
            heygen_payload = {
                "video_inputs": [
                    {
                        "character": {
                            "type": "avatar",
                            "avatar_id": avatar_id,
                            "avatar_style": "normal"
                        },
                        "voice": {
                            "type": "text",
                            "input_text": cleaned_script,
                            "voice_id": voice_id,
                            "speed": 1.0
                        },
                        "background": {
                            "type": "color",
                            "value": "#ffffff"
                        }
                    }
                ],
                "dimension": {
                    "width": 1280 if video_quality == "720p" else 1920,
                    "height": 720 if video_quality == "720p" else 1080
                },
                "aspect_ratio": "16:9",
                "test": False
            }
            
            headers = {
                "X-API-KEY": settings.HEYGEN_API_KEY,
                "Content-Type": "application/json"
            }
            
            # Submit video generation request
            response = await self.make_api_request(
                "https://api.heygen.com/v2/video/generate",
                "POST",
                headers,
                heygen_payload
            )
            
            if not response.success:
                return ToolResult.error_result(f"HeyGen API request failed: {response.error}")
            
            video_id = response.data.get("data", {}).get("video_id")
            if not video_id:
                return ToolResult.error_result("No video ID returned from HeyGen API")
            
            # Poll for video completion
            video_url = await self._poll_video_status(video_id, headers)
            
            if not video_url:
                return ToolResult.error_result("Video generation failed or timed out")
            
            video_data = {
                "video_id": video_id,
                "video_url": video_url,
                "script_title": script_item.title,
                "script_id": script_item.id,
                "avatar_id": avatar_id,
                "voice_id": voice_id,
                "video_quality": video_quality,
                "status": "completed",
                "duration_seconds": script_data.get("estimated_duration_minutes", 5) * 60,
                "created_at": script_item.created_at.isoformat()
            }
            
            return ToolResult.success_result(video_data)
            
        except Exception as e:
            return ToolResult.error_result(f"Error creating video: {str(e)}")
    
    async def _poll_video_status(self, video_id: str, headers: Dict[str, str], max_wait_minutes: int = 10) -> str:
        """Poll HeyGen API for video completion status"""
        max_attempts = max_wait_minutes * 6  # Check every 10 seconds
        
        for attempt in range(max_attempts):
            try:
                response = await self.make_api_request(
                    f"https://api.heygen.com/v1/video_status.get?video_id={video_id}",
                    "GET",
                    headers
                )
                
                if response.success:
                    status = response.data.get("data", {}).get("status")
                    
                    if status == "completed":
                        video_url = response.data.get("data", {}).get("video_url")
                        if video_url:
                            return video_url
                    elif status == "failed":
                        self.logger.error(f"Video generation failed for video_id: {video_id}")
                        return None
                    elif status in ["processing", "pending"]:
                        # Continue polling
                        await asyncio.sleep(10)
                        continue
                
            except Exception as e:
                self.logger.warning(f"Error polling video status: {e}")
            
            await asyncio.sleep(10)
        
        self.logger.warning(f"Video generation timed out for video_id: {video_id}")
        return None
    
    def _clean_script_text(self, script_text: str) -> str:
        """Clean script text by removing stage directions and formatting for TTS"""
        import re
        
        # Remove stage directions in brackets
        cleaned = re.sub(r'\[.*?\]', '', script_text)
        
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # Remove markdown formatting
        cleaned = re.sub(r'\*\*(.*?)\*\*', r'\1', cleaned)  # Bold
        cleaned = re.sub(r'\*(.*?)\*', r'\1', cleaned)      # Italic
        cleaned = re.sub(r'`(.*?)`', r'\1', cleaned)        # Code
        
        # Clean up punctuation for better TTS
        cleaned = cleaned.replace('...', '. ')
        cleaned = cleaned.replace('--', ', ')
        
        return cleaned.strip()
    
    async def get_available_avatars(self) -> ToolResult:
        """Get list of available avatars from HeyGen"""
        if not settings.HEYGEN_API_KEY:
            return ToolResult.error_result("HeyGen API key not configured")
        
        try:
            headers = {
                "X-API-KEY": settings.HEYGEN_API_KEY,
                "Content-Type": "application/json"
            }
            
            response = await self.make_api_request(
                "https://api.heygen.com/v1/avatar.list",
                "GET",
                headers
            )
            
            if response.success:
                avatars = response.data.get("data", {}).get("avatars", [])
                return ToolResult.success_result({"avatars": avatars})
            else:
                return ToolResult.error_result(f"Failed to get avatars: {response.error}")
                
        except Exception as e:
            return ToolResult.error_result(f"Error getting avatars: {str(e)}")
    
    async def get_available_voices(self) -> ToolResult:
        """Get list of available voices from HeyGen"""
        if not settings.HEYGEN_API_KEY:
            return ToolResult.error_result("HeyGen API key not configured")
        
        try:
            headers = {
                "X-API-KEY": settings.HEYGEN_API_KEY,
                "Content-Type": "application/json"
            }
            
            response = await self.make_api_request(
                "https://api.heygen.com/v1/voice.list",
                "GET",
                headers
            )
            
            if response.success:
                voices = response.data.get("data", {}).get("voices", [])
                return ToolResult.success_result({"voices": voices})
            else:
                return ToolResult.error_result(f"Failed to get voices: {response.error}")
                
        except Exception as e:
            return ToolResult.error_result(f"Error getting voices: {str(e)}")

video_creator = VideoCreator()
