import logging
import sys
import asyncio
import aiohttp
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
from config.settings import settings
from src.models import ToolResult, AgentState

class BaseTool(ABC):
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.logger = self._setup_logging()
        self.max_retries = settings.MAX_RETRIES
        self.timeout = settings.TIMEOUT_SECONDS
    
    def _setup_logging(self):
        """Setup logging for the tool"""
        logger = logging.getLogger(f"tools.{self.name}")
        if not logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO if settings.DEBUG else logging.WARNING)
        return logger
    
    def validate_required_params(self, required_params: List[str], **kwargs) -> Optional[str]:
        """Validate that all required parameters are provided"""
        missing = [p for p in required_params if kwargs.get(p) is None]
        if missing:
            error_msg = f"Missing required parameters for tool '{self.name}': {', '.join(missing)}"
            self.logger.error(error_msg)
            return error_msg
        return None
    
    async def make_api_request(
        self, 
        url: str, 
        method: str = "POST",
        headers: Optional[Dict[str, str]] = None,
        payload: Optional[Dict[str, Any]] = None,
        timeout: Optional[int] = None
    ) -> ToolResult:
        """Make an HTTP API request with retry logic"""
        timeout = timeout or self.timeout
        headers = headers or {}
        
        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                    if method.upper() == "GET":
                        async with session.get(url, headers=headers) as response:
                            return await self._process_response(response)
                    elif method.upper() == "POST":
                        async with session.post(url, json=payload, headers=headers) as response:
                            return await self._process_response(response)
                    elif method.upper() == "PUT":
                        async with session.put(url, json=payload, headers=headers) as response:
                            return await self._process_response(response)
                    else:
                        return ToolResult.error_result(f"Unsupported HTTP method: {method}")
            
            except asyncio.TimeoutError:
                error_msg = f"Request timeout after {timeout} seconds (attempt {attempt + 1}/{self.max_retries})"
                self.logger.warning(error_msg)
                if attempt == self.max_retries - 1:
                    return ToolResult.error_result(error_msg)
            
            except aiohttp.ClientError as e:
                error_msg = f"HTTP client error: {str(e)} (attempt {attempt + 1}/{self.max_retries})"
                self.logger.warning(error_msg)
                if attempt == self.max_retries - 1:
                    return ToolResult.error_result(error_msg)
            
            except Exception as e:
                error_msg = f"Unexpected error: {str(e)} (attempt {attempt + 1}/{self.max_retries})"
                self.logger.error(error_msg)
                if attempt == self.max_retries - 1:
                    return ToolResult.error_result(error_msg)
            
            # Wait before retry (exponential backoff)
            if attempt < self.max_retries - 1:
                wait_time = 2 ** attempt
                self.logger.info(f"Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
        
        return ToolResult.error_result("Max retries exceeded")
    
    async def _process_response(self, response: aiohttp.ClientResponse) -> ToolResult:
        """Process HTTP response and return ToolResult"""
        try:
            if response.status == 200:
                data = await response.json()
                return ToolResult.success_result(data)
            elif response.status == 429:
                # Rate limit - should be retried
                error_msg = f"Rate limit exceeded (HTTP {response.status})"
                self.logger.warning(error_msg)
                raise aiohttp.ClientError(error_msg)
            else:
                error_text = await response.text()
                error_msg = f"API error (HTTP {response.status}): {error_text}"
                return ToolResult.error_result(error_msg)
        
        except Exception as e:
            return ToolResult.error_result(f"Error processing response: {str(e)}")
    
    def prepare_ai_prompt(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """Prepare a prompt for AI API calls"""
        ai_config = settings.get_primary_ai_api()
        
        if ai_config["provider"] == "claude":
            return {
                "model": ai_config["model"],
                "max_tokens": 4000,
                "messages": [
                    {"role": "user", "content": f"{system_prompt}\n\n{context or ''}\n\n{user_prompt}"}
                ]
            }
        elif ai_config["provider"] == "openai":
            messages = [{"role": "system", "content": system_prompt}]
            if context:
                messages.append({"role": "user", "content": f"Context: {context}"})
            messages.append({"role": "user", "content": user_prompt})
            
            return {
                "model": ai_config["model"],
                "messages": messages,
                "max_tokens": 4000,
                "temperature": 0.7
            }
        elif ai_config["provider"] == "gemini":
            full_prompt = f"{system_prompt}\n\n{context or ''}\n\n{user_prompt}"
            return {
                "contents": [{"parts": [{"text": full_prompt}]}],
                "generationConfig": {
                    "maxOutputTokens": 4000,
                    "temperature": 0.7
                }
            }
        else:
            raise ValueError(f"Unsupported AI provider: {ai_config['provider']}")
    
    def get_ai_headers(self) -> Dict[str, str]:
        """Get headers for AI API requests"""
        ai_config = settings.get_primary_ai_api()
        
        if ai_config["provider"] == "claude":
            return {
                "x-api-key": ai_config["api_key"],
                "anthropic-version": "2023-06-01",
                "content-type": "application/json"
            }
        elif ai_config["provider"] == "openai":
            return {
                "Authorization": f"Bearer {ai_config['api_key']}",
                "Content-Type": "application/json"
            }
        elif ai_config["provider"] == "gemini":
            return {
                "Content-Type": "application/json"
            }
        else:
            raise ValueError(f"Unsupported AI provider: {ai_config['provider']}")
    
    def get_ai_url(self) -> str:
        """Get the API URL for AI requests"""
        ai_config = settings.get_primary_ai_api()
        
        if ai_config["provider"] == "gemini":
            return f"{ai_config['base_url']}/{ai_config['model']}:generateContent?key={ai_config['api_key']}"
        else:
            return ai_config["base_url"]
    
    def extract_ai_response(self, response_data: Dict[str, Any]) -> str:
        """Extract text content from AI API response"""
        ai_config = settings.get_primary_ai_api()
        
        try:
            if ai_config["provider"] == "claude":
                return response_data["content"][0]["text"]
            elif ai_config["provider"] == "openai":
                return response_data["choices"][0]["message"]["content"]
            elif ai_config["provider"] == "gemini":
                return response_data["candidates"][0]["content"]["parts"][0]["text"]
            else:
                raise ValueError(f"Unsupported AI provider: {ai_config['provider']}")
        except (KeyError, IndexError) as e:
            raise ValueError(f"Invalid response format from {ai_config['provider']}: {e}")
    
    @abstractmethod
    async def execute(self, state: AgentState, **kwargs) -> ToolResult:
        """Execute the tool's main functionality"""
        pass
