from tools.base_tool import <PERSON>Tool
from src.models import ToolResult, AgentState, ContentType, ContentItem
from config.settings import settings
import json
import uuid
import os
import zipfile
import tempfile
from typing import List, Dict, Any, Optional
import aiofiles

class ResearchProcessor(BaseTool):
    def __init__(self):
        super().__init__(
            name="research_processor",
            description="Processes research data including text files, PDFs, and archives for book generation."
        )
    
    async def execute(self, state: AgentState, research_data: Any, **kwargs) -> ToolResult:
        """Process research data and extract key information for book generation"""
        self.logger.info("Processing research data for book generation")
        
        try:
            # Determine the type of research data
            if isinstance(research_data, str):
                # File path or text content
                if os.path.exists(research_data):
                    processed_data = await self._process_file(research_data)
                else:
                    # Treat as text content
                    processed_data = await self._process_text_content(research_data)
            elif isinstance(research_data, list):
                # Multiple files or text chunks
                processed_data = await self._process_multiple_sources(research_data)
            elif isinstance(research_data, dict):
                # Structured data
                processed_data = await self._process_structured_data(research_data)
            else:
                return ToolResult.error_result("Unsupported research data format")
            
            if not processed_data:
                return ToolResult.error_result("Failed to process research data")
            
            # Extract key themes and concepts
            analysis_result = await self._analyze_research_content(processed_data)
            
            if not analysis_result.success:
                return ToolResult.error_result(f"Failed to analyze research content: {analysis_result.error}")
            
            # Create content item for processed research
            research_item = ContentItem(
                id=str(uuid.uuid4()),
                type=ContentType.REFERENCE,
                title="Processed Research Data",
                content=json.dumps(processed_data, indent=2),
                metadata={
                    "source_count": len(processed_data.get("sources", [])),
                    "total_words": processed_data.get("total_words", 0),
                    "key_themes_count": len(analysis_result.data.get("key_themes", [])),
                    "processing_method": processed_data.get("processing_method", "unknown")
                }
            )
            
            state.add_content_item(research_item)
            state.research_data.update({
                "processed_research": processed_data,
                "research_analysis": analysis_result.data,
                "research_item_id": research_item.id
            })
            
            self.logger.info(f"Successfully processed research data with {len(processed_data.get('sources', []))} sources")
            
            return ToolResult.success_result(
                {
                    "processed_data": processed_data,
                    "analysis": analysis_result.data,
                    "content_item_id": research_item.id,
                    "summary": {
                        "sources": len(processed_data.get("sources", [])),
                        "total_words": processed_data.get("total_words", 0),
                        "key_themes": len(analysis_result.data.get("key_themes", []))
                    }
                },
                metadata={"tool": self.name, "sources_processed": len(processed_data.get("sources", []))}
            )
            
        except Exception as e:
            error_msg = f"Error processing research data: {str(e)}"
            self.logger.error(error_msg)
            return ToolResult.error_result(error_msg)
    
    async def _process_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Process a single file"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.zip':
                return await self._process_zip_file(file_path)
            elif file_ext == '.pdf':
                return await self._process_pdf_file(file_path)
            elif file_ext in ['.txt', '.md', '.doc', '.docx']:
                return await self._process_text_file(file_path)
            else:
                # Try to read as text
                return await self._process_text_file(file_path)
                
        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {e}")
            return None
    
    async def _process_zip_file(self, zip_path: str) -> Optional[Dict[str, Any]]:
        """Process a ZIP archive containing multiple research files"""
        try:
            sources = []
            total_words = 0
            
            with tempfile.TemporaryDirectory() as temp_dir:
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)
                
                # Process each file in the archive
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        file_content = await self._read_file_content(file_path)
                        
                        if file_content:
                            word_count = len(file_content.split())
                            sources.append({
                                "filename": file,
                                "content": file_content,
                                "word_count": word_count,
                                "file_type": os.path.splitext(file)[1].lower()
                            })
                            total_words += word_count
            
            return {
                "sources": sources,
                "total_words": total_words,
                "processing_method": "zip_archive",
                "source_count": len(sources)
            }
            
        except Exception as e:
            self.logger.error(f"Error processing ZIP file: {e}")
            return None
    
    async def _process_pdf_file(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """Process a PDF file (placeholder - would need PDF processing library)"""
        try:
            # In a real implementation, you would use a library like PyPDF2 or pdfplumber
            # For now, return a placeholder
            content = f"PDF content from {os.path.basename(pdf_path)} (PDF processing not implemented)"
            word_count = len(content.split())
            
            return {
                "sources": [{
                    "filename": os.path.basename(pdf_path),
                    "content": content,
                    "word_count": word_count,
                    "file_type": ".pdf"
                }],
                "total_words": word_count,
                "processing_method": "pdf_extraction",
                "source_count": 1
            }
            
        except Exception as e:
            self.logger.error(f"Error processing PDF file: {e}")
            return None
    
    async def _process_text_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Process a text-based file"""
        try:
            content = await self._read_file_content(file_path)
            if not content:
                return None
            
            word_count = len(content.split())
            
            return {
                "sources": [{
                    "filename": os.path.basename(file_path),
                    "content": content,
                    "word_count": word_count,
                    "file_type": os.path.splitext(file_path)[1].lower()
                }],
                "total_words": word_count,
                "processing_method": "text_file",
                "source_count": 1
            }
            
        except Exception as e:
            self.logger.error(f"Error processing text file: {e}")
            return None
    
    async def _read_file_content(self, file_path: str) -> Optional[str]:
        """Read content from a file with encoding detection"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                        return await f.read()
                except UnicodeDecodeError:
                    continue
            
            # If all encodings fail, try binary mode and decode with errors='ignore'
            async with aiofiles.open(file_path, 'rb') as f:
                content = await f.read()
                return content.decode('utf-8', errors='ignore')
                
        except Exception as e:
            self.logger.warning(f"Could not read file {file_path}: {e}")
            return None
    
    async def _process_text_content(self, text_content: str) -> Dict[str, Any]:
        """Process raw text content"""
        word_count = len(text_content.split())
        
        return {
            "sources": [{
                "filename": "direct_input.txt",
                "content": text_content,
                "word_count": word_count,
                "file_type": ".txt"
            }],
            "total_words": word_count,
            "processing_method": "direct_text",
            "source_count": 1
        }
    
    async def _process_multiple_sources(self, sources: List[Any]) -> Optional[Dict[str, Any]]:
        """Process multiple research sources"""
        try:
            processed_sources = []
            total_words = 0
            
            for i, source in enumerate(sources):
                if isinstance(source, str):
                    if os.path.exists(source):
                        # File path
                        file_data = await self._process_file(source)
                        if file_data:
                            processed_sources.extend(file_data["sources"])
                            total_words += file_data["total_words"]
                    else:
                        # Text content
                        word_count = len(source.split())
                        processed_sources.append({
                            "filename": f"source_{i+1}.txt",
                            "content": source,
                            "word_count": word_count,
                            "file_type": ".txt"
                        })
                        total_words += word_count
            
            return {
                "sources": processed_sources,
                "total_words": total_words,
                "processing_method": "multiple_sources",
                "source_count": len(processed_sources)
            }
            
        except Exception as e:
            self.logger.error(f"Error processing multiple sources: {e}")
            return None
    
    async def _process_structured_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process structured research data"""
        try:
            sources = []
            total_words = 0
            
            # Handle different structured data formats
            if "sources" in data:
                for source in data["sources"]:
                    if isinstance(source, dict) and "content" in source:
                        word_count = len(source["content"].split())
                        sources.append({
                            "filename": source.get("filename", "unknown.txt"),
                            "content": source["content"],
                            "word_count": word_count,
                            "file_type": source.get("file_type", ".txt"),
                            "metadata": source.get("metadata", {})
                        })
                        total_words += word_count
            elif "content" in data:
                # Single content item
                word_count = len(data["content"].split())
                sources.append({
                    "filename": data.get("filename", "structured_data.txt"),
                    "content": data["content"],
                    "word_count": word_count,
                    "file_type": data.get("file_type", ".txt"),
                    "metadata": data.get("metadata", {})
                })
                total_words += word_count
            
            return {
                "sources": sources,
                "total_words": total_words,
                "processing_method": "structured_data",
                "source_count": len(sources)
            }
            
        except Exception as e:
            self.logger.error(f"Error processing structured data: {e}")
            return None
    
    async def _analyze_research_content(self, processed_data: Dict[str, Any]) -> ToolResult:
        """Analyze processed research content to extract key themes and concepts"""
        try:
            # Combine all source content
            all_content = " ".join([source["content"] for source in processed_data.get("sources", [])])
            
            if not all_content.strip():
                return ToolResult.error_result("No content to analyze")
            
            # Prepare AI prompt for analysis
            system_prompt = self._get_analysis_system_prompt()
            user_prompt = self._get_analysis_user_prompt(all_content, processed_data)
            
            # Make API request
            payload = self.prepare_ai_prompt(system_prompt, user_prompt)
            headers = self.get_ai_headers()
            url = self.get_ai_url()
            
            response = await self.make_api_request(url, "POST", headers, payload)
            
            if not response.success:
                return ToolResult.error_result(f"AI API request failed: {response.error}")
            
            # Extract and parse the response
            analysis_text = self.extract_ai_response(response.data)
            analysis_data = self._parse_analysis_response(analysis_text)
            
            return ToolResult.success_result(analysis_data)
            
        except Exception as e:
            return ToolResult.error_result(f"Error analyzing research content: {str(e)}")
    
    def _get_analysis_system_prompt(self) -> str:
        return """You are an expert research analyst and content strategist. Your task is to analyze research content and extract key insights that will be useful for book writing.

When analyzing research content, you should:
1. Identify the main themes and topics
2. Extract key concepts and terminology
3. Identify potential arguments and perspectives
4. Note important facts, statistics, and evidence
5. Suggest potential book structure based on the content
6. Identify gaps that might need additional research

Return your analysis as a valid JSON object with this structure:
{
    "key_themes": ["Theme 1", "Theme 2", ...],
    "main_concepts": ["Concept 1", "Concept 2", ...],
    "potential_arguments": ["Argument 1", "Argument 2", ...],
    "important_facts": ["Fact 1", "Fact 2", ...],
    "statistics": ["Stat 1", "Stat 2", ...],
    "suggested_chapters": ["Chapter 1", "Chapter 2", ...],
    "research_gaps": ["Gap 1", "Gap 2", ...],
    "content_summary": "Brief summary of the research content",
    "tone_analysis": "Academic/Popular/Technical/etc.",
    "target_audience": "Suggested target audience based on content"
}"""
    
    def _get_analysis_user_prompt(self, content: str, processed_data: Dict[str, Any]) -> str:
        # Truncate content if too long (keep first and last parts)
        max_content_length = 8000
        if len(content) > max_content_length:
            content = content[:max_content_length//2] + "\n\n[... content truncated ...]\n\n" + content[-max_content_length//2:]
        
        return f"""Analyze this research content for book writing purposes:

Research Overview:
- Total Sources: {processed_data.get('source_count', 0)}
- Total Words: {processed_data.get('total_words', 0)}
- Processing Method: {processed_data.get('processing_method', 'unknown')}

Content to Analyze:
{content}

Please provide a comprehensive analysis that will help in:
1. Creating a book outline
2. Identifying the main arguments to develop
3. Understanding the scope and depth of available material
4. Determining the appropriate tone and target audience
5. Identifying areas that need additional research

Focus on extracting actionable insights that will guide the book writing process."""
    
    def _parse_analysis_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the AI analysis response"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # If no JSON found, create basic analysis
                return {
                    "key_themes": [],
                    "main_concepts": [],
                    "potential_arguments": [],
                    "important_facts": [],
                    "statistics": [],
                    "suggested_chapters": [],
                    "research_gaps": [],
                    "content_summary": response_text[:500] + "..." if len(response_text) > 500 else response_text,
                    "tone_analysis": "Unknown",
                    "target_audience": "General audience"
                }
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse analysis JSON: {e}")
            return {
                "key_themes": [],
                "main_concepts": [],
                "potential_arguments": [],
                "important_facts": [],
                "statistics": [],
                "suggested_chapters": [],
                "research_gaps": [],
                "content_summary": "Analysis parsing failed",
                "tone_analysis": "Unknown",
                "target_audience": "General audience",
                "parse_error": str(e)
            }

research_processor = ResearchProcessor()
