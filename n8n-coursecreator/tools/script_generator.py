from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState, ContentType, ContentItem
from config.settings import settings
import json
import uuid
from typing import List, Dict, Any

class ScriptGenerator(BaseTool):
    def __init__(self):
        super().__init__(
            name="script_generator",
            description="Generates detailed scripts for course lessons based on course outline."
        )
    
    async def execute(self, state: AgentState, **kwargs) -> ToolResult:
        """Generate scripts for all lessons in the course outline"""
        if not state.outline:
            return ToolResult.error_result("No course outline found in state")
        
        self.logger.info("Generating scripts for course lessons")
        
        try:
            # Extract preferences
            style = kwargs.get("style", state.user_preferences.get("style", settings.DEFAULT_COURSE_STYLE))
            tone = kwargs.get("tone", state.user_preferences.get("tone", settings.DEFAULT_COURSE_TONE))
            depth = kwargs.get("depth", state.user_preferences.get("depth", settings.DEFAULT_COURSE_DEPTH))
            
            # Generate scripts for each lesson
            generated_scripts = []
            total_lessons = 0
            
            for module in state.outline.modules:
                module_scripts = await self._generate_module_scripts(
                    module, state, style, tone, depth
                )
                generated_scripts.extend(module_scripts)
                total_lessons += len(module.get("lessons", []))
            
            # Create summary content item
            summary_item = ContentItem(
                id=str(uuid.uuid4()),
                type=ContentType.SCRIPT,
                title=f"Course Scripts Summary - {state.outline.title}",
                content=json.dumps({
                    "course_title": state.outline.title,
                    "total_scripts": len(generated_scripts),
                    "total_lessons": total_lessons,
                    "scripts": [script["metadata"] for script in generated_scripts]
                }, indent=2),
                metadata={
                    "type": "summary",
                    "script_count": len(generated_scripts),
                    "total_word_count": sum(script.get("word_count", 0) for script in generated_scripts),
                    "estimated_duration": sum(script.get("estimated_duration_minutes", 0) for script in generated_scripts)
                }
            )
            
            state.add_content_item(summary_item)
            
            self.logger.info(f"Successfully generated {len(generated_scripts)} scripts")
            
            return ToolResult.success_result(
                {
                    "scripts": generated_scripts,
                    "summary": {
                        "total_scripts": len(generated_scripts),
                        "total_lessons": total_lessons,
                        "estimated_total_duration": sum(script.get("estimated_duration_minutes", 0) for script in generated_scripts)
                    },
                    "summary_content_item_id": summary_item.id
                },
                metadata={"tool": self.name, "course_title": state.outline.title}
            )
            
        except Exception as e:
            error_msg = f"Error generating scripts: {str(e)}"
            self.logger.error(error_msg)
            return ToolResult.error_result(error_msg)
    
    async def _generate_module_scripts(
        self, 
        module: Dict[str, Any], 
        state: AgentState, 
        style: str, 
        tone: str, 
        depth: str
    ) -> List[Dict[str, Any]]:
        """Generate scripts for all lessons in a module"""
        scripts = []
        
        for lesson in module.get("lessons", []):
            script_result = await self._generate_lesson_script(
                module, lesson, state, style, tone, depth
            )
            
            if script_result.success:
                scripts.append(script_result.data)
                
                # Create content item for this script
                content_item = ContentItem(
                    id=str(uuid.uuid4()),
                    type=ContentType.SCRIPT,
                    title=f"Script: {lesson['title']}",
                    content=script_result.data["script"],
                    metadata={
                        "module_number": module["module_number"],
                        "lesson_number": lesson["lesson_number"],
                        "module_title": module["title"],
                        "lesson_title": lesson["title"],
                        "word_count": script_result.data.get("word_count", 0),
                        "estimated_duration_minutes": script_result.data.get("estimated_duration_minutes", 0)
                    }
                )
                
                state.add_content_item(content_item)
                script_result.data["content_item_id"] = content_item.id
            else:
                self.logger.warning(f"Failed to generate script for lesson: {lesson['title']}")
        
        return scripts
    
    async def _generate_lesson_script(
        self, 
        module: Dict[str, Any], 
        lesson: Dict[str, Any], 
        state: AgentState, 
        style: str, 
        tone: str, 
        depth: str
    ) -> ToolResult:
        """Generate a script for a single lesson"""
        try:
            # Prepare the prompt
            system_prompt = self._get_system_prompt()
            user_prompt = self._get_user_prompt(
                state.outline.title, module, lesson, style, tone, depth
            )
            
            # Make API request
            payload = self.prepare_ai_prompt(system_prompt, user_prompt)
            headers = self.get_ai_headers()
            url = self.get_ai_url()
            
            response = await self.make_api_request(url, "POST", headers, payload)
            
            if not response.success:
                return ToolResult.error_result(f"AI API request failed: {response.error}")
            
            # Extract and parse the response
            script_text = self.extract_ai_response(response.data)
            script_data = self._parse_script_response(script_text, lesson)
            
            return ToolResult.success_result(script_data)
            
        except Exception as e:
            return ToolResult.error_result(f"Error generating lesson script: {str(e)}")
    
    def _get_system_prompt(self) -> str:
        return """You are an expert course script writer and instructional designer. Your task is to create engaging, educational scripts for video lessons that effectively teach the specified content.

When writing scripts, you should:
1. Start with a compelling hook to grab attention
2. Clearly state the learning objectives
3. Use conversational, engaging language
4. Include smooth transitions between topics
5. Incorporate examples and practical applications
6. Add interactive elements (questions, pauses for reflection)
7. Summarize key points at the end
8. Include clear calls-to-action

Structure your script with:
- Introduction (hook + objectives)
- Main content (broken into digestible sections)
- Examples and applications
- Summary and key takeaways
- Next steps or call-to-action

Use natural speech patterns and include stage directions in [brackets] for the presenter.

Return your response as a JSON object with this structure:
{
    "script": "Full script text with stage directions",
    "word_count": 500,
    "estimated_duration_minutes": 5,
    "key_points": ["Point 1", "Point 2", ...],
    "interactive_elements": ["Question 1", "Activity 1", ...],
    "stage_directions": ["Direction 1", "Direction 2", ...],
    "materials_needed": ["Material 1", "Material 2", ...]
}"""
    
    def _get_user_prompt(
        self, 
        course_title: str, 
        module: Dict[str, Any], 
        lesson: Dict[str, Any], 
        style: str, 
        tone: str, 
        depth: str
    ) -> str:
        return f"""Create a detailed script for this lesson:

Course: {course_title}
Module: {module['title']} (Module {module['module_number']})
Lesson: {lesson['title']} (Lesson {lesson['lesson_number']})

Lesson Details:
- Description: {lesson.get('description', 'No description provided')}
- Learning Objectives: {', '.join(lesson.get('learning_objectives', []))}
- Key Concepts: {', '.join(lesson.get('key_concepts', []))}
- Estimated Duration: {lesson.get('estimated_duration', '15 minutes')}
- Content Type: {lesson.get('content_type', 'video')}

Style Requirements:
- Style: {style}
- Tone: {tone}
- Depth: {depth}

Additional Requirements:
- Target the script for a {lesson.get('estimated_duration', '15 minutes')} video
- Include natural pauses and transitions
- Make it engaging and conversational
- Include practical examples where relevant
- Add interactive elements to maintain engagement
- Ensure the content matches the learning objectives

Please create a comprehensive script that an instructor can follow to deliver an effective lesson."""
    
    def _parse_script_response(self, response_text: str, lesson: Dict[str, Any]) -> Dict[str, Any]:
        """Parse the AI response and extract script data"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                script_data = json.loads(json_str)
            else:
                # If no JSON found, treat entire response as script
                script_data = {
                    "script": response_text,
                    "word_count": len(response_text.split()),
                    "estimated_duration_minutes": max(1, len(response_text.split()) // 150),  # ~150 words per minute
                    "key_points": [],
                    "interactive_elements": [],
                    "stage_directions": [],
                    "materials_needed": []
                }
            
            # Add metadata
            script_data.update({
                "module_number": lesson.get("lesson_number", 1),
                "lesson_number": lesson.get("lesson_number", 1),
                "lesson_title": lesson.get("title", "Untitled Lesson"),
                "lesson_objectives": lesson.get("learning_objectives", [])
            })
            
            return script_data
            
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse JSON response: {e}")
            # Return basic script data
            return {
                "script": response_text,
                "word_count": len(response_text.split()),
                "estimated_duration_minutes": max(1, len(response_text.split()) // 150),
                "key_points": [],
                "interactive_elements": [],
                "stage_directions": [],
                "materials_needed": [],
                "module_number": lesson.get("module_number", 1),
                "lesson_number": lesson.get("lesson_number", 1),
                "lesson_title": lesson.get("title", "Untitled Lesson"),
                "lesson_objectives": lesson.get("learning_objectives", [])
            }
        except Exception as e:
            self.logger.error(f"Error parsing script response: {e}")
            return {
                "script": "Error generating script",
                "word_count": 0,
                "estimated_duration_minutes": 0,
                "key_points": [],
                "interactive_elements": [],
                "stage_directions": [],
                "materials_needed": [],
                "error": str(e)
            }

script_generator = ScriptGenerator()
