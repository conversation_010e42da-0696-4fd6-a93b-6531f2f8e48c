from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState, ContentType, ContentItem
from config.settings import settings
import json
import uuid
from typing import List, Dict, Any

class ChapterGenerator(BaseTool):
    def __init__(self):
        super().__init__(
            name="chapter_generator",
            description="Generates detailed chapter content for books based on book outline and research data."
        )
    
    async def execute(self, state: AgentState, **kwargs) -> ToolResult:
        """Generate content for all chapters in the book outline"""
        if not state.outline:
            return ToolResult.error_result("No book outline found in state")
        
        self.logger.info("Generating chapter content for book")
        
        try:
            # Extract preferences
            style = kwargs.get("style", state.user_preferences.get("style", settings.DEFAULT_BOOK_STYLE))
            citation_style = kwargs.get("citation_style", state.user_preferences.get("citation_style", settings.DEFAULT_CITATION_STYLE))
            include_citations = kwargs.get("include_citations", state.user_preferences.get("include_citations", True))
            
            # Generate content for each chapter
            generated_chapters = []
            total_chapters = len(state.outline.chapters)
            
            for chapter in state.outline.chapters:
                chapter_result = await self._generate_chapter_content(
                    chapter, state, style, citation_style, include_citations
                )
                
                if chapter_result.success:
                    generated_chapters.append(chapter_result.data)
                    
                    # Create content item for this chapter
                    content_item = ContentItem(
                        id=str(uuid.uuid4()),
                        type=ContentType.CHAPTER,
                        title=f"Chapter {chapter['chapter_number']}: {chapter['title']}",
                        content=chapter_result.data["content"],
                        metadata={
                            "chapter_number": chapter["chapter_number"],
                            "chapter_title": chapter["title"],
                            "word_count": chapter_result.data.get("word_count", 0),
                            "estimated_pages": chapter_result.data.get("estimated_pages", 0),
                            "section_count": len(chapter_result.data.get("sections", [])),
                            "citation_count": len(chapter_result.data.get("citations", []))
                        }
                    )
                    
                    state.add_content_item(content_item)
                    chapter_result.data["content_item_id"] = content_item.id
                else:
                    self.logger.warning(f"Failed to generate content for chapter: {chapter['title']}")
            
            # Create summary content item
            summary_item = ContentItem(
                id=str(uuid.uuid4()),
                type=ContentType.CHAPTER,
                title=f"Book Chapters Summary - {state.outline.title}",
                content=json.dumps({
                    "book_title": state.outline.title,
                    "total_chapters": total_chapters,
                    "generated_chapters": len(generated_chapters),
                    "chapters": [chapter["metadata"] for chapter in generated_chapters]
                }, indent=2),
                metadata={
                    "type": "summary",
                    "chapter_count": len(generated_chapters),
                    "total_word_count": sum(chapter.get("word_count", 0) for chapter in generated_chapters),
                    "total_estimated_pages": sum(chapter.get("estimated_pages", 0) for chapter in generated_chapters)
                }
            )
            
            state.add_content_item(summary_item)
            
            self.logger.info(f"Successfully generated {len(generated_chapters)} chapters")
            
            return ToolResult.success_result(
                {
                    "chapters": generated_chapters,
                    "summary": {
                        "total_chapters": total_chapters,
                        "generated_chapters": len(generated_chapters),
                        "total_word_count": sum(chapter.get("word_count", 0) for chapter in generated_chapters),
                        "total_estimated_pages": sum(chapter.get("estimated_pages", 0) for chapter in generated_chapters)
                    },
                    "summary_content_item_id": summary_item.id
                },
                metadata={"tool": self.name, "book_title": state.outline.title}
            )
            
        except Exception as e:
            error_msg = f"Error generating chapters: {str(e)}"
            self.logger.error(error_msg)
            return ToolResult.error_result(error_msg)
    
    async def _generate_chapter_content(
        self, 
        chapter: Dict[str, Any], 
        state: AgentState, 
        style: str, 
        citation_style: str,
        include_citations: bool
    ) -> ToolResult:
        """Generate content for a single chapter"""
        try:
            # Build research context for this chapter
            research_context = self._build_chapter_research_context(chapter, state)
            
            # Prepare the prompt
            system_prompt = self._get_system_prompt()
            user_prompt = self._get_user_prompt(
                chapter, state.outline, style, citation_style, include_citations, research_context
            )
            
            # Make API request
            payload = self.prepare_ai_prompt(system_prompt, user_prompt, research_context)
            headers = self.get_ai_headers()
            url = self.get_ai_url()
            
            response = await self.make_api_request(url, "POST", headers, payload)
            
            if not response.success:
                return ToolResult.error_result(f"AI API request failed: {response.error}")
            
            # Extract and parse the response
            chapter_text = self.extract_ai_response(response.data)
            chapter_data = self._parse_chapter_response(chapter_text, chapter)
            
            return ToolResult.success_result(chapter_data)
            
        except Exception as e:
            return ToolResult.error_result(f"Error generating chapter content: {str(e)}")
    
    def _get_system_prompt(self) -> str:
        return """You are an expert book writer and content creator with extensive experience in crafting engaging, well-researched chapters. Your task is to write comprehensive chapter content that advances the book's main argument while being accessible to the target audience.

When writing chapters, you should:
1. Start with a compelling opening that connects to the overall book narrative
2. Develop ideas logically and progressively
3. Use clear, engaging prose appropriate to the style and audience
4. Integrate research and evidence naturally
5. Include relevant examples, case studies, or anecdotes
6. Maintain consistent voice and tone throughout
7. End with a strong conclusion that transitions to the next chapter
8. Include proper citations when referencing sources

Structure your chapter with:
- Opening hook and chapter overview
- Main content organized by sections
- Supporting evidence and examples
- Smooth transitions between ideas
- Chapter summary and key takeaways
- Transition to next chapter

Return your response as a JSON object with this structure:
{
    "content": "Full chapter text with proper formatting",
    "word_count": 3000,
    "estimated_pages": 12,
    "sections": [
        {
            "title": "Section Title",
            "content": "Section content",
            "word_count": 500
        }
    ],
    "key_points": ["Point 1", "Point 2", ...],
    "citations": ["Citation 1", "Citation 2", ...],
    "examples_used": ["Example 1", "Example 2", ...],
    "chapter_summary": "Brief summary of chapter content",
    "transition_to_next": "How this chapter connects to the next"
}"""
    
    def _get_user_prompt(
        self, 
        chapter: Dict[str, Any], 
        outline: Any, 
        style: str, 
        citation_style: str,
        include_citations: bool,
        research_context: str
    ) -> str:
        return f"""Write a comprehensive chapter for this book:

Book: {outline.title}
{f"Subtitle: {outline.subtitle}" if outline.subtitle else ""}
Main Argument: {outline.main_argument}
Target Audience: {outline.target_audience}

Chapter Details:
- Chapter {chapter['chapter_number']}: {chapter['title']}
- Description: {chapter.get('description', 'No description provided')}
- Main Points: {', '.join(chapter.get('main_points', []))}
- Estimated Pages: {chapter.get('estimated_pages', 15)}

Chapter Sections:
{self._format_sections(chapter.get('sections', []))}

Writing Requirements:
- Style: {style}
- Citation Style: {citation_style}
- Include Citations: {include_citations}
- Target Length: {chapter.get('estimated_pages', 15)} pages (~{chapter.get('estimated_pages', 15) * 250} words)

Research Context Available:
{research_context[:2000] + "..." if len(research_context) > 2000 else research_context}

Please write a complete chapter that:
1. Advances the book's main argument effectively
2. Covers all specified main points and sections
3. Integrates available research naturally
4. Maintains the appropriate style and tone
5. Includes proper citations where relevant
6. Provides practical examples and applications
7. Engages the target audience throughout
8. Creates smooth transitions between sections
9. Ends with a strong conclusion and transition

Ensure the chapter is well-structured, engaging, and contributes meaningfully to the overall book narrative."""
    
    def _format_sections(self, sections: List[Dict[str, Any]]) -> str:
        """Format chapter sections for the prompt"""
        if not sections:
            return "No specific sections defined"
        
        formatted = []
        for section in sections:
            formatted.append(f"- {section.get('title', 'Untitled Section')}: {section.get('description', 'No description')}")
            if section.get('key_concepts'):
                formatted.append(f"  Key Concepts: {', '.join(section['key_concepts'])}")
        
        return "\n".join(formatted)
    
    def _build_chapter_research_context(self, chapter: Dict[str, Any], state: AgentState) -> str:
        """Build research context specific to this chapter"""
        context_parts = []
        
        # Add research analysis relevant to this chapter
        research_analysis = state.research_data.get("research_analysis", {})
        if research_analysis:
            # Filter themes and concepts relevant to this chapter
            chapter_keywords = [
                chapter.get("title", "").lower(),
                chapter.get("description", "").lower()
            ] + [point.lower() for point in chapter.get("main_points", [])]
            
            relevant_themes = []
            for theme in research_analysis.get("key_themes", []):
                if any(keyword in theme.lower() for keyword in chapter_keywords if keyword):
                    relevant_themes.append(theme)
            
            if relevant_themes:
                context_parts.append(f"Relevant Themes: {', '.join(relevant_themes[:5])}")
            
            # Add relevant facts and statistics
            relevant_facts = []
            for fact in research_analysis.get("important_facts", []):
                if any(keyword in fact.lower() for keyword in chapter_keywords if keyword):
                    relevant_facts.append(fact)
            
            if relevant_facts:
                context_parts.append(f"Relevant Facts: {'; '.join(relevant_facts[:3])}")
        
        # Add relevant source content
        processed_research = state.research_data.get("processed_research", {})
        if processed_research and processed_research.get("sources"):
            relevant_content = []
            for source in processed_research["sources"][:3]:  # Limit to first 3 sources
                content_preview = source["content"][:300] + "..." if len(source["content"]) > 300 else source["content"]
                relevant_content.append(f"{source['filename']}: {content_preview}")
            
            if relevant_content:
                context_parts.append(f"Source Content:\n{chr(10).join(relevant_content)}")
        
        return "\n\n".join(context_parts)
    
    def _parse_chapter_response(self, response_text: str, chapter: Dict[str, Any]) -> Dict[str, Any]:
        """Parse the AI response and extract chapter data"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                chapter_data = json.loads(json_str)
            else:
                # If no JSON found, treat entire response as chapter content
                chapter_data = {
                    "content": response_text,
                    "word_count": len(response_text.split()),
                    "estimated_pages": max(1, len(response_text.split()) // 250),  # ~250 words per page
                    "sections": [],
                    "key_points": [],
                    "citations": [],
                    "examples_used": [],
                    "chapter_summary": "",
                    "transition_to_next": ""
                }
            
            # Add metadata
            chapter_data.update({
                "chapter_number": chapter.get("chapter_number", 1),
                "chapter_title": chapter.get("title", "Untitled Chapter"),
                "metadata": {
                    "original_description": chapter.get("description", ""),
                    "original_main_points": chapter.get("main_points", []),
                    "target_pages": chapter.get("estimated_pages", 15)
                }
            })
            
            return chapter_data
            
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse JSON response: {e}")
            # Return basic chapter data
            return {
                "content": response_text,
                "word_count": len(response_text.split()),
                "estimated_pages": max(1, len(response_text.split()) // 250),
                "sections": [],
                "key_points": [],
                "citations": [],
                "examples_used": [],
                "chapter_summary": "",
                "transition_to_next": "",
                "chapter_number": chapter.get("chapter_number", 1),
                "chapter_title": chapter.get("title", "Untitled Chapter"),
                "metadata": {
                    "original_description": chapter.get("description", ""),
                    "original_main_points": chapter.get("main_points", []),
                    "target_pages": chapter.get("estimated_pages", 15)
                }
            }
        except Exception as e:
            self.logger.error(f"Error parsing chapter response: {e}")
            return {
                "content": "Error generating chapter content",
                "word_count": 0,
                "estimated_pages": 0,
                "sections": [],
                "key_points": [],
                "citations": [],
                "examples_used": [],
                "chapter_summary": "",
                "transition_to_next": "",
                "error": str(e)
            }

chapter_generator = ChapterGenerator()
