from tools.base_tool import <PERSON>Tool
from src.models import ToolResult, AgentState, ContentType, ContentItem
from config.settings import settings
import json
import uuid
from typing import List, Dict, Any

class QAGenerator(BaseTool):
    def __init__(self):
        super().__init__(
            name="qa_generator",
            description="Generates Q&A content for course modules including text, audio, and video formats."
        )
    
    async def execute(self, state: AgentState, **kwargs) -> ToolResult:
        """Generate Q&A content for all modules in the course"""
        if not state.outline:
            return ToolResult.error_result("No course outline found in state")
        
        self.logger.info("Generating Q&A content for course modules")
        
        try:
            # Extract preferences
            qa_count_per_module = kwargs.get("qa_count", state.user_preferences.get("qa_count", 5))
            include_audio = kwargs.get("include_audio", state.user_preferences.get("include_audio", True))
            include_video = kwargs.get("include_video", state.user_preferences.get("include_video", False))
            
            # Generate Q&A for each module
            all_qa_content = []
            
            for module in state.outline.modules:
                module_qa = await self._generate_module_qa(
                    module, state, qa_count_per_module, include_audio, include_video
                )
                
                if module_qa.success:
                    all_qa_content.append(module_qa.data)
                    
                    # Create content item for module Q&A
                    qa_item = ContentItem(
                        id=str(uuid.uuid4()),
                        type=ContentType.QA,
                        title=f"Q&A: {module['title']}",
                        content=json.dumps(module_qa.data, indent=2),
                        metadata={
                            "module_number": module["module_number"],
                            "module_title": module["title"],
                            "qa_count": len(module_qa.data.get("qa_pairs", [])),
                            "has_audio": include_audio,
                            "has_video": include_video
                        }
                    )
                    
                    state.add_content_item(qa_item)
                    module_qa.data["content_item_id"] = qa_item.id
                else:
                    self.logger.warning(f"Failed to generate Q&A for module: {module['title']}")
            
            # Create summary
            total_qa_pairs = sum(len(module_qa.get("qa_pairs", [])) for module_qa in all_qa_content)
            
            summary = {
                "total_modules": len(state.outline.modules),
                "modules_with_qa": len(all_qa_content),
                "total_qa_pairs": total_qa_pairs,
                "average_qa_per_module": total_qa_pairs / len(all_qa_content) if all_qa_content else 0,
                "includes_audio": include_audio,
                "includes_video": include_video
            }
            
            self.logger.info(f"Generated Q&A content for {len(all_qa_content)} modules with {total_qa_pairs} total Q&A pairs")
            
            return ToolResult.success_result(
                {
                    "qa_content": all_qa_content,
                    "summary": summary
                },
                metadata={"tool": self.name, "total_qa_pairs": total_qa_pairs}
            )
            
        except Exception as e:
            error_msg = f"Error generating Q&A content: {str(e)}"
            self.logger.error(error_msg)
            return ToolResult.error_result(error_msg)
    
    async def _generate_module_qa(
        self, 
        module: Dict[str, Any], 
        state: AgentState, 
        qa_count: int,
        include_audio: bool,
        include_video: bool
    ) -> ToolResult:
        """Generate Q&A content for a single module"""
        try:
            # Get module content context
            module_context = self._build_module_context(module, state)
            
            # Generate Q&A pairs
            qa_result = await self._generate_qa_pairs(module, module_context, qa_count)
            
            if not qa_result.success:
                return qa_result
            
            qa_pairs = qa_result.data["qa_pairs"]
            
            # Generate audio for Q&A if requested
            if include_audio and settings.ELEVENLABS_API_KEY:
                audio_result = await self._generate_qa_audio(qa_pairs, module)
                if audio_result.success:
                    qa_result.data["audio_files"] = audio_result.data["audio_files"]
            
            # Generate video for Q&A if requested
            if include_video and settings.HEYGEN_API_KEY:
                video_result = await self._generate_qa_videos(qa_pairs, module)
                if video_result.success:
                    qa_result.data["video_files"] = video_result.data["video_files"]
            
            qa_result.data.update({
                "module_number": module["module_number"],
                "module_title": module["title"],
                "generated_at": state.updated_at.isoformat()
            })
            
            return qa_result
            
        except Exception as e:
            return ToolResult.error_result(f"Error generating module Q&A: {str(e)}")
    
    def _build_module_context(self, module: Dict[str, Any], state: AgentState) -> str:
        """Build context for Q&A generation from module and related scripts"""
        context_parts = [
            f"Module: {module['title']}",
            f"Description: {module.get('description', '')}",
            f"Learning Objectives: {', '.join(module.get('learning_objectives', []))}"
        ]
        
        # Add lesson information
        lessons = module.get("lessons", [])
        if lessons:
            context_parts.append("Lessons:")
            for lesson in lessons:
                context_parts.append(f"- {lesson['title']}: {lesson.get('description', '')}")
                if lesson.get('key_concepts'):
                    context_parts.append(f"  Key Concepts: {', '.join(lesson['key_concepts'])}")
        
        # Add script content if available
        script_items = state.get_content_by_type(ContentType.SCRIPT)
        module_scripts = [
            item for item in script_items 
            if item.metadata.get("module_number") == module["module_number"]
        ]
        
        if module_scripts:
            context_parts.append("Script Content:")
            for script_item in module_scripts:
                try:
                    script_data = json.loads(script_item.content)
                    if isinstance(script_data, dict) and "key_points" in script_data:
                        context_parts.append(f"- {script_item.title}: {', '.join(script_data['key_points'])}")
                except:
                    pass
        
        return "\n".join(context_parts)
    
    async def _generate_qa_pairs(self, module: Dict[str, Any], context: str, qa_count: int) -> ToolResult:
        """Generate Q&A pairs for a module"""
        try:
            system_prompt = self._get_qa_system_prompt()
            user_prompt = self._get_qa_user_prompt(module, context, qa_count)
            
            # Make API request
            payload = self.prepare_ai_prompt(system_prompt, user_prompt, context)
            headers = self.get_ai_headers()
            url = self.get_ai_url()
            
            response = await self.make_api_request(url, "POST", headers, payload)
            
            if not response.success:
                return ToolResult.error_result(f"AI API request failed: {response.error}")
            
            # Extract and parse the response
            qa_text = self.extract_ai_response(response.data)
            qa_data = self._parse_qa_response(qa_text)
            
            if not qa_data or not qa_data.get("qa_pairs"):
                return ToolResult.error_result("Failed to generate valid Q&A pairs")
            
            return ToolResult.success_result(qa_data)
            
        except Exception as e:
            return ToolResult.error_result(f"Error generating Q&A pairs: {str(e)}")
    
    def _get_qa_system_prompt(self) -> str:
        return """You are an expert educational content creator specializing in creating engaging Q&A content for online courses. Your task is to generate thoughtful questions and comprehensive answers that help students test their understanding and reinforce key concepts.

When creating Q&A pairs, you should:
1. Create questions that test different levels of understanding (recall, comprehension, application, analysis)
2. Include a mix of question types (multiple choice, short answer, scenario-based)
3. Write clear, concise questions that are unambiguous
4. Provide comprehensive answers that explain the reasoning
5. Include practical examples and real-world applications
6. Ensure questions cover all key concepts from the module
7. Make questions progressively challenging

Return your response as a valid JSON object with this structure:
{
    "qa_pairs": [
        {
            "id": 1,
            "question": "Question text",
            "answer": "Comprehensive answer with explanation",
            "question_type": "multiple_choice|short_answer|true_false|scenario",
            "difficulty": "beginner|intermediate|advanced",
            "key_concepts": ["concept1", "concept2"],
            "options": ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
            "correct_option": "A",
            "explanation": "Why this answer is correct and others are wrong"
        }
    ],
    "summary": {
        "total_questions": 5,
        "difficulty_distribution": {"beginner": 2, "intermediate": 2, "advanced": 1},
        "question_types": {"multiple_choice": 3, "short_answer": 2}
    }
}"""
    
    def _get_qa_user_prompt(self, module: Dict[str, Any], context: str, qa_count: int) -> str:
        return f"""Create {qa_count} Q&A pairs for this course module:

Module: {module['title']}
Module Number: {module['module_number']}

Context and Content:
{context}

Requirements:
- Generate exactly {qa_count} questions
- Include a mix of question types (at least 60% multiple choice for easy grading)
- Cover all key learning objectives and concepts
- Range from beginner to advanced difficulty
- Make questions practical and applicable
- Provide detailed explanations for all answers
- Ensure questions are clear and unambiguous

Focus on creating questions that:
1. Test understanding of key concepts
2. Apply knowledge to real-world scenarios
3. Help students identify common misconceptions
4. Reinforce the most important learning points
5. Prepare students for practical application

Please generate comprehensive Q&A content that will effectively assess student learning."""
    
    def _parse_qa_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the AI response and extract Q&A data"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # If no JSON found, try to parse as text
                return self._parse_text_qa(response_text)
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse JSON response: {e}")
            return self._parse_text_qa(response_text)
        except Exception as e:
            self.logger.error(f"Error parsing Q&A response: {e}")
            return None
    
    def _parse_text_qa(self, text: str) -> Dict[str, Any]:
        """Fallback method to parse text-based Q&A"""
        lines = text.strip().split('\n')
        qa_pairs = []
        current_question = None
        current_answer = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if line.startswith('Q') or line.startswith('Question'):
                if current_question and current_answer:
                    qa_pairs.append({
                        "id": len(qa_pairs) + 1,
                        "question": current_question,
                        "answer": current_answer,
                        "question_type": "short_answer",
                        "difficulty": "intermediate",
                        "key_concepts": [],
                        "options": [],
                        "correct_option": "",
                        "explanation": ""
                    })
                current_question = line
                current_answer = None
            elif line.startswith('A') or line.startswith('Answer'):
                current_answer = line
        
        # Add the last Q&A pair
        if current_question and current_answer:
            qa_pairs.append({
                "id": len(qa_pairs) + 1,
                "question": current_question,
                "answer": current_answer,
                "question_type": "short_answer",
                "difficulty": "intermediate",
                "key_concepts": [],
                "options": [],
                "correct_option": "",
                "explanation": ""
            })
        
        return {
            "qa_pairs": qa_pairs,
            "summary": {
                "total_questions": len(qa_pairs),
                "difficulty_distribution": {"intermediate": len(qa_pairs)},
                "question_types": {"short_answer": len(qa_pairs)}
            }
        }
    
    async def _generate_qa_audio(self, qa_pairs: List[Dict[str, Any]], module: Dict[str, Any]) -> ToolResult:
        """Generate audio files for Q&A pairs using ElevenLabs"""
        try:
            audio_files = []
            
            headers = {
                "xi-api-key": settings.ELEVENLABS_API_KEY,
                "Content-Type": "application/json"
            }
            
            for qa_pair in qa_pairs:
                # Generate audio for the question
                question_audio = await self._generate_single_audio(
                    qa_pair["question"], 
                    f"qa_question_{module['module_number']}_{qa_pair['id']}", 
                    headers
                )
                
                # Generate audio for the answer
                answer_audio = await self._generate_single_audio(
                    qa_pair["answer"], 
                    f"qa_answer_{module['module_number']}_{qa_pair['id']}", 
                    headers
                )
                
                if question_audio and answer_audio:
                    audio_files.append({
                        "qa_id": qa_pair["id"],
                        "question_audio": question_audio,
                        "answer_audio": answer_audio
                    })
            
            return ToolResult.success_result({"audio_files": audio_files})
            
        except Exception as e:
            return ToolResult.error_result(f"Error generating Q&A audio: {str(e)}")
    
    async def _generate_single_audio(self, text: str, filename: str, headers: Dict[str, str]) -> str:
        """Generate a single audio file using ElevenLabs"""
        try:
            payload = {
                "text": text,
                "model_id": "eleven_monolingual_v1",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.5
                }
            }
            
            response = await self.make_api_request(
                "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM",  # Default voice
                "POST",
                headers,
                payload
            )
            
            if response.success:
                # In a real implementation, you would save the audio file
                # For now, return a placeholder URL
                return f"https://storage.example.com/audio/{filename}.mp3"
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Failed to generate audio for {filename}: {e}")
            return None
    
    async def _generate_qa_videos(self, qa_pairs: List[Dict[str, Any]], module: Dict[str, Any]) -> ToolResult:
        """Generate video files for Q&A pairs using HeyGen"""
        # This would be similar to the audio generation but using HeyGen API
        # For brevity, returning a placeholder implementation
        try:
            video_files = []
            
            for qa_pair in qa_pairs:
                video_files.append({
                    "qa_id": qa_pair["id"],
                    "question_video": f"https://storage.example.com/video/qa_question_{module['module_number']}_{qa_pair['id']}.mp4",
                    "answer_video": f"https://storage.example.com/video/qa_answer_{module['module_number']}_{qa_pair['id']}.mp4"
                })
            
            return ToolResult.success_result({"video_files": video_files})
            
        except Exception as e:
            return ToolResult.error_result(f"Error generating Q&A videos: {str(e)}")

qa_generator = QAGenerator()
