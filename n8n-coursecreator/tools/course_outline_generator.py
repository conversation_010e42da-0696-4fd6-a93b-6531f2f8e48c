from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState, CourseOutline, ContentType, ContentItem
from config.settings import settings
import json
import uuid
from datetime import datetime

class CourseOutlineGenerator(BaseTool):
    def __init__(self):
        super().__init__(
            name="course_outline_generator",
            description="Generates comprehensive course outlines with learning objectives, modules, and lessons."
        )
    
    async def execute(self, state: AgentState, course_title: str, **kwargs) -> ToolResult:
        """Generate a course outline based on the course title and optional context"""
        error = self.validate_required_params(["course_title"], course_title=course_title)
        if error:
            return ToolResult.error_result(error)
        
        self.logger.info(f"Generating course outline for: {course_title}")
        
        try:
            # Extract user preferences
            style = kwargs.get("style", state.user_preferences.get("style", settings.DEFAULT_COURSE_STYLE))
            tone = kwargs.get("tone", state.user_preferences.get("tone", settings.DEFAULT_COURSE_TONE))
            depth = kwargs.get("depth", state.user_preferences.get("depth", settings.DEFAULT_COURSE_DEPTH))
            target_audience = kwargs.get("target_audience", state.user_preferences.get("target_audience", "general audience"))
            duration = kwargs.get("duration", state.user_preferences.get("duration", "4-6 hours"))
            
            # Prepare the prompt
            system_prompt = self._get_system_prompt()
            user_prompt = self._get_user_prompt(
                course_title, style, tone, depth, target_audience, duration, state.context
            )
            
            # Make API request
            payload = self.prepare_ai_prompt(system_prompt, user_prompt, state.context)
            headers = self.get_ai_headers()
            url = self.get_ai_url()
            
            response = await self.make_api_request(url, "POST", headers, payload)
            
            if not response.success:
                return ToolResult.error_result(f"AI API request failed: {response.error}")
            
            # Extract and parse the response
            outline_text = self.extract_ai_response(response.data)
            outline_data = self._parse_outline_response(outline_text)
            
            if not outline_data:
                return ToolResult.error_result("Failed to parse course outline from AI response")
            
            # Create CourseOutline object
            course_outline = CourseOutline(
                title=outline_data.get("title", course_title),
                description=outline_data.get("description", ""),
                learning_objectives=outline_data.get("learning_objectives", []),
                target_audience=outline_data.get("target_audience", target_audience),
                modules=outline_data.get("modules", []),
                estimated_duration=outline_data.get("estimated_duration", duration),
                prerequisites=outline_data.get("prerequisites", [])
            )
            
            # Create content item
            content_item = ContentItem(
                id=str(uuid.uuid4()),
                type=ContentType.OUTLINE,
                title=f"Course Outline: {course_title}",
                content=json.dumps(course_outline.to_dict(), indent=2),
                metadata={
                    "style": style,
                    "tone": tone,
                    "depth": depth,
                    "module_count": len(course_outline.modules),
                    "total_lessons": sum(len(module.get("lessons", [])) for module in course_outline.modules)
                }
            )
            
            # Update state
            state.outline = course_outline
            state.add_content_item(content_item)
            
            self.logger.info(f"Successfully generated course outline with {len(course_outline.modules)} modules")
            
            return ToolResult.success_result(
                {
                    "outline": course_outline.to_dict(),
                    "content_item_id": content_item.id,
                    "summary": {
                        "modules": len(course_outline.modules),
                        "total_lessons": sum(len(module.get("lessons", [])) for module in course_outline.modules),
                        "estimated_duration": course_outline.estimated_duration
                    }
                },
                metadata={"tool": self.name, "course_title": course_title}
            )
            
        except Exception as e:
            error_msg = f"Error generating course outline: {str(e)}"
            self.logger.error(error_msg)
            return ToolResult.error_result(error_msg)
    
    def _get_system_prompt(self) -> str:
        return """You are an expert instructional designer and course creator. Your task is to generate comprehensive, well-structured course outlines that provide clear learning paths for students.

When creating a course outline, you should:
1. Define clear, measurable learning objectives
2. Structure content into logical modules and lessons
3. Consider the target audience's background and needs
4. Ensure progressive difficulty and skill building
5. Include practical applications and assessments
6. Provide realistic time estimates

Return your response as a valid JSON object with the following structure:
{
    "title": "Course Title",
    "description": "Brief course description",
    "learning_objectives": ["Objective 1", "Objective 2", ...],
    "target_audience": "Description of target audience",
    "prerequisites": ["Prerequisite 1", "Prerequisite 2", ...],
    "estimated_duration": "Total course duration",
    "modules": [
        {
            "module_number": 1,
            "title": "Module Title",
            "description": "Module description",
            "learning_objectives": ["Module objective 1", ...],
            "estimated_duration": "Module duration",
            "lessons": [
                {
                    "lesson_number": 1,
                    "title": "Lesson Title",
                    "description": "Lesson description",
                    "learning_objectives": ["Lesson objective 1", ...],
                    "estimated_duration": "Lesson duration",
                    "content_type": "video/text/interactive",
                    "key_concepts": ["Concept 1", "Concept 2", ...],
                    "activities": ["Activity 1", "Activity 2", ...],
                    "assessment": "Assessment description"
                }
            ]
        }
    ]
}"""
    
    def _get_user_prompt(self, course_title: str, style: str, tone: str, depth: str, target_audience: str, duration: str, context: str) -> str:
        prompt = f"""Create a comprehensive course outline for: "{course_title}"

Course Requirements:
- Style: {style}
- Tone: {tone}
- Depth: {depth}
- Target Audience: {target_audience}
- Estimated Duration: {duration}"""
        
        if context:
            prompt += f"\n- Additional Context: {context}"
        
        prompt += """

Please generate a detailed course outline that includes:
1. A compelling course description
2. 3-5 clear learning objectives
3. 4-8 modules with logical progression
4. 2-5 lessons per module
5. Specific learning objectives for each module and lesson
6. Realistic time estimates
7. Appropriate prerequisites
8. Mix of content types (video, text, interactive)
9. Practical activities and assessments

Ensure the course is engaging, practical, and provides real value to the target audience."""
        
        return prompt
    
    def _parse_outline_response(self, response_text: str) -> dict:
        """Parse the AI response and extract course outline data"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # If no JSON found, try to parse as structured text
                return self._parse_text_outline(response_text)
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse JSON response: {e}")
            return self._parse_text_outline(response_text)
        except Exception as e:
            self.logger.error(f"Error parsing outline response: {e}")
            return None
    
    def _parse_text_outline(self, text: str) -> dict:
        """Fallback method to parse text-based outline"""
        # This is a simplified parser for text-based responses
        # In a production system, you might want a more sophisticated parser
        lines = text.strip().split('\n')
        
        outline = {
            "title": "Generated Course",
            "description": "Course generated from outline",
            "learning_objectives": [],
            "target_audience": "General audience",
            "prerequisites": [],
            "estimated_duration": "4-6 hours",
            "modules": []
        }
        
        current_module = None
        current_lesson = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Simple heuristics to identify structure
            if line.startswith('Module') or line.startswith('Chapter'):
                if current_module:
                    outline["modules"].append(current_module)
                current_module = {
                    "module_number": len(outline["modules"]) + 1,
                    "title": line,
                    "description": "",
                    "learning_objectives": [],
                    "estimated_duration": "1 hour",
                    "lessons": []
                }
            elif line.startswith('Lesson') or line.startswith('Topic'):
                if current_module and current_lesson:
                    current_module["lessons"].append(current_lesson)
                current_lesson = {
                    "lesson_number": len(current_module["lessons"]) + 1 if current_module else 1,
                    "title": line,
                    "description": "",
                    "learning_objectives": [],
                    "estimated_duration": "15 minutes",
                    "content_type": "video",
                    "key_concepts": [],
                    "activities": [],
                    "assessment": ""
                }
        
        # Add the last module and lesson
        if current_lesson and current_module:
            current_module["lessons"].append(current_lesson)
        if current_module:
            outline["modules"].append(current_module)
        
        return outline

course_outline_generator = CourseOutlineGenerator()
