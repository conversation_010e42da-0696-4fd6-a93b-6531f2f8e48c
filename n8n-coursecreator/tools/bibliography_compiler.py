from tools.base_tool import BaseTool
from src.models import ToolResult, AgentState, ContentType, ContentItem
from config.settings import settings
import json
import uuid
import re
from typing import List, Dict, Any, Optional

class BibliographyCompiler(BaseTool):
    def __init__(self):
        super().__init__(
            name="bibliography_compiler",
            description="Compiles and formats bibliography and reference materials for books."
        )
    
    async def execute(self, state: AgentState, **kwargs) -> ToolResult:
        """Compile bibliography from research sources and chapter citations"""
        self.logger.info("Compiling bibliography and reference materials")
        
        try:
            # Extract preferences
            citation_style = kwargs.get("citation_style", state.user_preferences.get("citation_style", settings.DEFAULT_CITATION_STYLE))
            include_annotations = kwargs.get("include_annotations", state.user_preferences.get("include_annotations", False))
            
            # Collect citations from various sources
            citations = await self._collect_citations(state)
            
            # Format bibliography
            formatted_bibliography = await self._format_bibliography(citations, citation_style, include_annotations)
            
            if not formatted_bibliography.success:
                return ToolResult.error_result(f"Failed to format bibliography: {formatted_bibliography.error}")
            
            # Generate additional reference materials
            reference_materials = await self._generate_reference_materials(state, citations)
            
            # Create content items
            bibliography_item = ContentItem(
                id=str(uuid.uuid4()),
                type=ContentType.REFERENCE,
                title="Bibliography",
                content=formatted_bibliography.data["formatted_bibliography"],
                metadata={
                    "citation_style": citation_style,
                    "citation_count": len(citations),
                    "includes_annotations": include_annotations,
                    "source_types": formatted_bibliography.data.get("source_types", {})
                }
            )
            
            state.add_content_item(bibliography_item)
            
            # Create reference materials item if additional materials were generated
            reference_item = None
            if reference_materials.success:
                reference_item = ContentItem(
                    id=str(uuid.uuid4()),
                    type=ContentType.REFERENCE,
                    title="Additional Reference Materials",
                    content=reference_materials.data["content"],
                    metadata={
                        "material_types": reference_materials.data.get("material_types", []),
                        "related_to_bibliography": True
                    }
                )
                state.add_content_item(reference_item)
            
            result_data = {
                "bibliography": formatted_bibliography.data,
                "bibliography_content_item_id": bibliography_item.id,
                "summary": {
                    "total_citations": len(citations),
                    "citation_style": citation_style,
                    "source_types": formatted_bibliography.data.get("source_types", {}),
                    "includes_annotations": include_annotations
                }
            }
            
            if reference_item:
                result_data["reference_materials"] = reference_materials.data
                result_data["reference_content_item_id"] = reference_item.id
            
            self.logger.info(f"Successfully compiled bibliography with {len(citations)} citations")
            
            return ToolResult.success_result(
                result_data,
                metadata={"tool": self.name, "citation_count": len(citations)}
            )
            
        except Exception as e:
            error_msg = f"Error compiling bibliography: {str(e)}"
            self.logger.error(error_msg)
            return ToolResult.error_result(error_msg)
    
    async def _collect_citations(self, state: AgentState) -> List[Dict[str, Any]]:
        """Collect citations from research sources and chapter content"""
        citations = []
        
        # Extract citations from research sources
        processed_research = state.research_data.get("processed_research", {})
        if processed_research and processed_research.get("sources"):
            for source in processed_research["sources"]:
                citation = {
                    "type": "research_source",
                    "title": source.get("filename", "Unknown Source"),
                    "content": source.get("content", ""),
                    "file_type": source.get("file_type", ""),
                    "word_count": source.get("word_count", 0),
                    "metadata": source.get("metadata", {})
                }
                citations.append(citation)
        
        # Extract citations from chapter content
        chapter_items = state.get_content_by_type(ContentType.CHAPTER)
        for chapter_item in chapter_items:
            if chapter_item.metadata.get("type") == "summary":
                continue
            
            try:
                # Try to parse chapter content for citations
                if chapter_item.content.startswith('{'):
                    chapter_data = json.loads(chapter_item.content)
                    chapter_citations = chapter_data.get("citations", [])
                    
                    for citation in chapter_citations:
                        citations.append({
                            "type": "chapter_citation",
                            "citation": citation,
                            "chapter_number": chapter_item.metadata.get("chapter_number"),
                            "chapter_title": chapter_item.metadata.get("chapter_title")
                        })
                else:
                    # Extract citations from text content
                    extracted_citations = self._extract_citations_from_text(chapter_item.content)
                    for citation in extracted_citations:
                        citations.append({
                            "type": "extracted_citation",
                            "citation": citation,
                            "chapter_number": chapter_item.metadata.get("chapter_number"),
                            "chapter_title": chapter_item.metadata.get("chapter_title")
                        })
            except Exception as e:
                self.logger.warning(f"Could not extract citations from chapter {chapter_item.title}: {e}")
        
        # Remove duplicates and clean up
        unique_citations = self._deduplicate_citations(citations)
        
        return unique_citations
    
    def _extract_citations_from_text(self, text: str) -> List[str]:
        """Extract citations from text using regex patterns"""
        citations = []
        
        # Common citation patterns
        patterns = [
            r'\(([^)]+\d{4}[^)]*)\)',  # (Author, 2023) or (Author et al., 2023)
            r'\[([^\]]+\d{4}[^\]]*)\]',  # [Author, 2023] or [1]
            r'(?:According to|As noted by|See)\s+([^.]+\d{4}[^.]*)',  # According to Author (2023)
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            citations.extend(matches)
        
        return citations
    
    def _deduplicate_citations(self, citations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate citations"""
        seen = set()
        unique_citations = []
        
        for citation in citations:
            # Create a key for deduplication
            if citation["type"] == "research_source":
                key = citation["title"]
            else:
                key = citation.get("citation", str(citation))
            
            if key not in seen:
                seen.add(key)
                unique_citations.append(citation)
        
        return unique_citations
    
    async def _format_bibliography(self, citations: List[Dict[str, Any]], citation_style: str, include_annotations: bool) -> ToolResult:
        """Format citations according to the specified citation style"""
        try:
            # Prepare AI prompt for bibliography formatting
            system_prompt = self._get_bibliography_system_prompt()
            user_prompt = self._get_bibliography_user_prompt(citations, citation_style, include_annotations)
            
            # Make API request
            payload = self.prepare_ai_prompt(system_prompt, user_prompt)
            headers = self.get_ai_headers()
            url = self.get_ai_url()
            
            response = await self.make_api_request(url, "POST", headers, payload)
            
            if not response.success:
                return ToolResult.error_result(f"AI API request failed: {response.error}")
            
            # Extract and parse the response
            bibliography_text = self.extract_ai_response(response.data)
            bibliography_data = self._parse_bibliography_response(bibliography_text, citations)
            
            return ToolResult.success_result(bibliography_data)
            
        except Exception as e:
            return ToolResult.error_result(f"Error formatting bibliography: {str(e)}")
    
    def _get_bibliography_system_prompt(self) -> str:
        return """You are an expert academic librarian and citation specialist. Your task is to format bibliographies and reference lists according to various citation styles (APA, MLA, Chicago, etc.).

When formatting bibliographies, you should:
1. Follow the exact formatting rules for the specified citation style
2. Organize entries alphabetically by author's last name
3. Use proper punctuation, capitalization, and italics
4. Include all available bibliographic information
5. Create consistent formatting throughout
6. Add annotations if requested
7. Categorize sources by type when helpful

Return your response as a JSON object with this structure:
{
    "formatted_bibliography": "Complete formatted bibliography text",
    "citation_count": 15,
    "source_types": {
        "books": 5,
        "journal_articles": 7,
        "websites": 2,
        "other": 1
    },
    "style_used": "APA",
    "annotations_included": true,
    "organization_method": "alphabetical",
    "notes": "Any special notes about the bibliography"
}"""
    
    def _get_bibliography_user_prompt(self, citations: List[Dict[str, Any]], citation_style: str, include_annotations: bool) -> str:
        citation_list = []
        for i, citation in enumerate(citations[:20], 1):  # Limit to first 20 citations
            if citation["type"] == "research_source":
                citation_list.append(f"{i}. Source: {citation['title']} (File: {citation.get('file_type', 'unknown')})")
                if citation.get("content"):
                    preview = citation["content"][:200] + "..." if len(citation["content"]) > 200 else citation["content"]
                    citation_list.append(f"   Content preview: {preview}")
            else:
                citation_list.append(f"{i}. Citation: {citation.get('citation', 'Unknown citation')}")
                if citation.get("chapter_title"):
                    citation_list.append(f"   From: Chapter {citation.get('chapter_number', '?')}: {citation['chapter_title']}")
        
        return f"""Format a bibliography for these sources using {citation_style} style:

Citations to Format:
{chr(10).join(citation_list)}

Requirements:
- Citation Style: {citation_style}
- Include Annotations: {include_annotations}
- Organize alphabetically by author (when available)
- For research sources without clear bibliographic info, create reasonable entries based on available information
- For extracted citations, format them properly according to the style
- Include source type categorization

Please create a properly formatted bibliography that follows {citation_style} guidelines exactly. If some sources lack complete bibliographic information, make reasonable assumptions and note any limitations."""
    
    def _parse_bibliography_response(self, response_text: str, original_citations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Parse the AI response and extract bibliography data"""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # If no JSON found, treat entire response as bibliography
                return {
                    "formatted_bibliography": response_text,
                    "citation_count": len(original_citations),
                    "source_types": self._categorize_sources(original_citations),
                    "style_used": "Unknown",
                    "annotations_included": False,
                    "organization_method": "as_provided",
                    "notes": "Bibliography formatting could not be parsed as JSON"
                }
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse bibliography JSON: {e}")
            return {
                "formatted_bibliography": response_text,
                "citation_count": len(original_citations),
                "source_types": self._categorize_sources(original_citations),
                "style_used": "Unknown",
                "annotations_included": False,
                "organization_method": "as_provided",
                "notes": f"JSON parsing failed: {e}"
            }
    
    def _categorize_sources(self, citations: List[Dict[str, Any]]) -> Dict[str, int]:
        """Categorize sources by type"""
        categories = {
            "research_files": 0,
            "extracted_citations": 0,
            "chapter_citations": 0,
            "other": 0
        }
        
        for citation in citations:
            citation_type = citation.get("type", "other")
            if citation_type in categories:
                categories[citation_type] += 1
            else:
                categories["other"] += 1
        
        return categories
    
    async def _generate_reference_materials(self, state: AgentState, citations: List[Dict[str, Any]]) -> ToolResult:
        """Generate additional reference materials like glossary, index, etc."""
        try:
            # Extract key terms and concepts from the book content
            key_terms = await self._extract_key_terms(state)
            
            # Generate glossary
            glossary = await self._generate_glossary(key_terms, state)
            
            # Create reference materials content
            reference_content = []
            
            if glossary.success:
                reference_content.append("## Glossary")
                reference_content.append(glossary.data["content"])
            
            # Add suggested further reading
            if citations:
                reference_content.append("\n## Suggested Further Reading")
                reference_content.append("Based on the sources used in this book, the following materials may be of interest for further study:")
                
                for citation in citations[:10]:  # Top 10 sources
                    if citation["type"] == "research_source":
                        reference_content.append(f"- {citation['title']}")
            
            if not reference_content:
                return ToolResult.error_result("No reference materials could be generated")
            
            return ToolResult.success_result({
                "content": "\n".join(reference_content),
                "material_types": ["glossary", "further_reading"] if citations else ["glossary"],
                "key_terms_count": len(key_terms) if key_terms else 0
            })
            
        except Exception as e:
            return ToolResult.error_result(f"Error generating reference materials: {str(e)}")
    
    async def _extract_key_terms(self, state: AgentState) -> List[str]:
        """Extract key terms from book content"""
        # This is a simplified implementation
        # In a real system, you might use NLP techniques to extract key terms
        
        key_terms = set()
        
        # Extract from research analysis
        research_analysis = state.research_data.get("research_analysis", {})
        if research_analysis:
            key_terms.update(research_analysis.get("main_concepts", []))
            key_terms.update(research_analysis.get("key_themes", []))
        
        # Extract from chapter content (simplified)
        chapter_items = state.get_content_by_type(ContentType.CHAPTER)
        for chapter_item in chapter_items:
            if chapter_item.metadata.get("type") == "summary":
                continue
            
            try:
                if chapter_item.content.startswith('{'):
                    chapter_data = json.loads(chapter_item.content)
                    key_terms.update(chapter_data.get("key_points", []))
            except:
                pass
        
        return list(key_terms)[:50]  # Limit to 50 terms
    
    async def _generate_glossary(self, key_terms: List[str], state: AgentState) -> ToolResult:
        """Generate a glossary for key terms"""
        if not key_terms:
            return ToolResult.error_result("No key terms to create glossary from")
        
        try:
            system_prompt = """You are an expert academic writer creating a glossary for a book. Define each term clearly and concisely, appropriate for the book's target audience."""
            
            user_prompt = f"""Create a glossary for these key terms from the book "{state.outline.title if state.outline else 'Unknown Title'}":

Terms to define:
{', '.join(key_terms)}

For each term, provide:
1. A clear, concise definition
2. Context relevant to the book's subject matter
3. Any important distinctions or nuances

Format as a simple glossary with terms in alphabetical order."""
            
            # Make API request
            payload = self.prepare_ai_prompt(system_prompt, user_prompt)
            headers = self.get_ai_headers()
            url = self.get_ai_url()
            
            response = await self.make_api_request(url, "POST", headers, payload)
            
            if response.success:
                glossary_text = self.extract_ai_response(response.data)
                return ToolResult.success_result({"content": glossary_text})
            else:
                return ToolResult.error_result(f"Failed to generate glossary: {response.error}")
                
        except Exception as e:
            return ToolResult.error_result(f"Error generating glossary: {str(e)}")

bibliography_compiler = BibliographyCompiler()
